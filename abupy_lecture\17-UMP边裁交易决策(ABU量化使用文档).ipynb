{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# ABU量化系统使用文档 \n", "\n", "<center>\n", "        <img src=\"./image/abu_logo.png\" alt=\"\" style=\"vertical-align:middle;padding:10px 20px;\"><font size=\"6\" color=\"black\"><b>第17节 UMP边裁交易决策</b></font>\n", "</center>\n", "\n", "-----------------\n", "\n", "作者: 阿布\n", "\n", "阿布量化版权所有 未经允许 禁止转载\n", "\n", "[abu量化系统github地址](https://github.com/bbfamily/abu) (欢迎+star)\n", "\n", "[本节ipython notebook](https://github.com/bbfamily/abu/tree/master/abupy_lecture)\n", "\n", "上一节示例了ump主裁的使用以及回测示例 本节将示例ump边裁交易决策, 首先导入abupy中本节使用的模块："]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["enable example env will only read RomDataBu/df_kl.h5\n"]}], "source": ["# 基础库导入\n", "\n", "from __future__ import print_function\n", "from __future__ import division\n", "\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "%matplotlib inline\n", "\n", "import os\n", "import sys\n", "# 使用insert 0即只使用github，避免交叉使用了pip安装的abupy，导致的版本不一致问题\n", "sys.path.insert(0, os.path.abspath('../'))\n", "import abupy\n", "\n", "# 使用沙盒数据，目的是和书中一样的数据环境\n", "abupy.env.enable_example_env_ipython()"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"collapsed": true}, "outputs": [], "source": ["from abupy import AbuFactorAtrNStop, AbuFactorPreAtrNStop, AbuFactorCloseAtrNStop, AbuFactorBuyBreak, ABuProgress\n", "from abupy import abu, EMarketTargetType, AbuMetricsBase, ABuMarketDrawing, AbuFuturesCn, ABuSymbolPd, AbuOrderPdProxy\n", "from abupy import AbuUmpMainDeg, AbuUmpMainJump, AbuUmpMainPrice, AbuUmpMainWave, AbuFuturesCn, EStoreAbu, AbuML\n", "from abupy import AbuUmpEdgeDeg, AbuUmpEdgePrice, AbuUmpEdgeWave, AbuUmpEdgeFull"]}, {"cell_type": "markdown", "metadata": {}, "source": ["受限于沙盒中数据限制，本节示例的相关性分析只限制在abupy内置沙盒数据中，完整示例以及代码请阅读《量化交易之路》中相关章节。\n", "\n", "和上一节一样首先将内置沙盒中美股，A股，港股, 比特币，莱特币，期货市场中的symbol都列出来，然后组成训练集和测试集，买入卖出因子等相同设置:"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"collapsed": true}, "outputs": [], "source": ["us_choice_symbols = ['usTSLA', 'usNOAH', 'usSFUN', 'usBIDU', 'usAAPL', 'usGOOG', 'usWUBA', 'usVIPS']\n", "cn_choice_symbols = ['002230', '300104', '300059', '601766', '600085', '600036', '600809', '000002', '002594']\n", "hk_choice_symbols = ['hk03333', 'hk00700', 'hk02333', 'hk01359', 'hk00656', 'hk03888', 'hk02318']\n", "tc_choice_symbols = ['btc', 'ltc']\n", "# 期货市场的直接从AbuFuturesCn().symbo中读取\n", "ft_choice_symbols = AbuFuturesCn().symbol.tolist()\n", "\n", "# 训练集：沙盒中所有美股 ＋ 沙盒中所有A股 ＋ 沙盒中所有港股 ＋ 比特币\n", "train_choice_symbols = us_choice_symbols + cn_choice_symbols +  hk_choice_symbols + tc_choice_symbols[:1]\n", "# 测试集：沙盒中所有期货 ＋ 莱特币\n", "test_choice_symbols = ft_choice_symbols  + tc_choice_symbols[1:]\n", "\n", "# 设置初始资金数\n", "read_cash = 1000000\n", "# 买入因子依然延用向上突破因子\n", "buy_factors = [{'xd': 60, 'class': AbuFactorBuyBreak},\n", "               {'xd': 42, 'class': AbuFactorBuyBreak}]\n", "\n", "# 卖出因子继续使用上一节使用的因子\n", "sell_factors = [\n", "    {'stop_loss_n': 1.0, 'stop_win_n': 3.0,\n", "     'class': AbuFactorAtrNStop},\n", "    {'class': AbuFactorPreAtrNStop, 'pre_atr_n': 1.5},\n", "    {'class': AbuFactorCloseAtr<PERSON><PERSON>, 'close_atr_n': 1.5}\n", "]\n", "# 回测生成买入时刻特征\n", "abupy.env.g_enable_ml_feature = True"]}, {"cell_type": "markdown", "metadata": {}, "source": ["在运行完成第15节中相关内容后，使用load_abu_result_tuple读取第15节中保存在本地的训练集数据："]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["买入后卖出的交易数量:196\n", "买入后尚未卖出的交易数量:9\n", "胜率:59.6939%\n", "平均获利期望:18.6899%\n", "平均亏损期望:-7.1235%\n", "盈亏比:4.4972\n", "所有交易收益比例和:16.2396 \n", "所有交易总盈亏和:2717948.4900 \n"]}, {"name": "stdout", "output_type": "stream", "text": ["please wait! load_pickle....: /Users/<USER>/abu/data/cache/n2_lecture_train_capital\n", "please wait! load_pickle....: /Users/<USER>/abu/data/cache/n2_lecture_train_benchmark\n"]}], "source": ["abu_result_tuple_train = abu.load_abu_result_tuple(n_folds=2, store_type=EStoreAbu.E_STORE_CUSTOM_NAME, \n", "                                             custom_name='lecture_train')\n", "orders_pd_train = abu_result_tuple_train.orders_pd\n", "AbuMetricsBase.show_general(*abu_result_tuple_train, returns_cmp=True, only_info=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["边裁核心代码在基类AbuUmpEdgeBase源代码中, 之所以称为边裁，是由于它的实现原理是通过统计训练集数据两端边缘胜负数据实现的，边裁的训练步骤很简单如下所示：\n", "\n", "1. 将交易训练数据集根据获利比例进行排序\n", "2. 找到top win、top loss，做上标记rk（比如把获利最多的25% rk＝1，亏损最多25% rk＝－1，其他rk＝0）\n", "\n", "边裁比较复杂的地方在裁决方式上，它多次使用非均衡技术对最后的结果概率进行干预，目的是使最终的裁决正确率达成非均衡的目标，非均衡技术思想是量化中很很重要的一种设计思路，**因为我们量化的目标结果就是非均衡，我们想要赢的钱比输的多**。\n", "\n", "\n", "更多详情实现请阅读AbuUmpEdgeBase源代码\n", "\n", "### 1. 角度边裁\n", "\n", "与主裁代码设计结构类似，子类完成的主要工作就是对特征进行处理，如AbuUmpEdgeDeg的特征为21、42、60、252日拟合角度，与主裁对特征处理的架构类似，主要区别如下：\n", "\n", "1. 边裁特征不变区域为获利值(profit)与获利比例(profit_cg)。\n", "2. 边裁make_xy()函数上的装饰器使用的为@ump_edge_make_xy，主裁使用@ump_main_make_xy，详情请查阅ABuUmpBase.py\n", "\n", "下面不再分步讲解边裁的训练过程，与主裁类似直接使用类方法ump_edge_clf_dump完成主裁的训练和数据本地序列化操作："]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["please wait! dump_pickle....: /Users/<USER>/abu/data/ump/ump_edge_umpdegfiter\n"]}, {"data": {"text/html": ["<div>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>profit</th>\n", "      <th>profit_cg</th>\n", "      <th>buy_deg_ang42</th>\n", "      <th>buy_deg_ang252</th>\n", "      <th>buy_deg_ang60</th>\n", "      <th>buy_deg_ang21</th>\n", "      <th>p_rk_cg</th>\n", "      <th>rk</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2014-09-25</th>\n", "      <td>4368.00</td>\n", "      <td>0.0271</td>\n", "      <td>7.168</td>\n", "      <td>-3.708</td>\n", "      <td>4.342</td>\n", "      <td>2.255</td>\n", "      <td>99.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-10-09</th>\n", "      <td>-11707.50</td>\n", "      <td>-0.0588</td>\n", "      <td>-0.567</td>\n", "      <td>-6.527</td>\n", "      <td>1.309</td>\n", "      <td>1.837</td>\n", "      <td>47.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-10-17</th>\n", "      <td>23360.00</td>\n", "      <td>0.1170</td>\n", "      <td>2.328</td>\n", "      <td>4.764</td>\n", "      <td>2.096</td>\n", "      <td>2.357</td>\n", "      <td>142.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-10-24</th>\n", "      <td>20410.88</td>\n", "      <td>0.1021</td>\n", "      <td>-0.454</td>\n", "      <td>5.532</td>\n", "      <td>2.142</td>\n", "      <td>0.931</td>\n", "      <td>137.5</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-10-24</th>\n", "      <td>20410.88</td>\n", "      <td>0.1021</td>\n", "      <td>-0.454</td>\n", "      <td>5.532</td>\n", "      <td>2.142</td>\n", "      <td>0.931</td>\n", "      <td>137.5</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["              profit  profit_cg  buy_deg_ang42  buy_deg_ang252  buy_deg_ang60  \\\n", "2014-09-25   4368.00     0.0271          7.168          -3.708          4.342   \n", "2014-10-09 -11707.50    -0.0588         -0.567          -6.527          1.309   \n", "2014-10-17  23360.00     0.1170          2.328           4.764          2.096   \n", "2014-10-24  20410.88     0.1021         -0.454           5.532          2.142   \n", "2014-10-24  20410.88     0.1021         -0.454           5.532          2.142   \n", "\n", "            buy_deg_ang21  p_rk_cg  rk  \n", "2014-09-25          2.255     99.0   0  \n", "2014-10-09          1.837     47.0   0  \n", "2014-10-17          2.357    142.0   0  \n", "2014-10-24          0.931    137.5   0  \n", "2014-10-24          0.931    137.5   0  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["edge_deg = AbuUmpEdgeDeg.ump_edge_clf_dump(orders_pd_train)\n", "edge_deg.fiter.df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["上面所示的角度边裁除了profit和profit_cg与主裁使用的result不同外，多出了rk列，它代表了边裁的属性rk=1的交易为所有交易中表现最好的top个，rk=-1的交易为所有交易中表现最差的top个，如下可视化rk的比例："]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["<matplotlib.axes._subplots.AxesSubplot at 0x11b4e3278>"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAzMAAAGaCAYAAAA7Jx25AAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\n5MEkryV5IcmFWQ4FzEbv/euttc8meUfv3XEA3tyeSnIpyS/****************************/\nfWttoffuf1TZkjMzAADMhd77Nzf9+MW4vIxt+J4PAADm0cL2v8KbnZgBAGAefWXWAzD/3DMDAACU\n5MwMAABQkpgBAABKEjMAAEBJYgYAACjp/wGV9/RudAWhcAAAAABJRU5ErkJggg==\n", "text/plain": ["<matplotlib.figure.Figure at 0x11b480d30>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["edge_deg.fiter.df.rk.value_counts().plot(kind='bar')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["角度边裁与主裁的不同点，还多出了p_rk_cg列，它代表了交易最终收益的rank值，如下按照p_rk_cg排序，head即是所有交易中损失最大的5笔交易，显然它们的rk值也都是-1, 如下所示："]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>profit</th>\n", "      <th>profit_cg</th>\n", "      <th>buy_deg_ang42</th>\n", "      <th>buy_deg_ang252</th>\n", "      <th>buy_deg_ang60</th>\n", "      <th>buy_deg_ang21</th>\n", "      <th>p_rk_cg</th>\n", "      <th>rk</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2016-01-04</th>\n", "      <td>-16488.00</td>\n", "      <td>-0.2192</td>\n", "      <td>22.712</td>\n", "      <td>6.586</td>\n", "      <td>21.090</td>\n", "      <td>18.350</td>\n", "      <td>1.0</td>\n", "      <td>-1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-03-10</th>\n", "      <td>-18368.36</td>\n", "      <td>-0.1837</td>\n", "      <td>4.353</td>\n", "      <td>-31.744</td>\n", "      <td>-15.324</td>\n", "      <td>12.718</td>\n", "      <td>2.0</td>\n", "      <td>-1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-01-23</th>\n", "      <td>-15314.00</td>\n", "      <td>-0.1797</td>\n", "      <td>28.126</td>\n", "      <td>14.501</td>\n", "      <td>21.693</td>\n", "      <td>37.728</td>\n", "      <td>3.0</td>\n", "      <td>-1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-07-30</th>\n", "      <td>-13345.00</td>\n", "      <td>-0.1473</td>\n", "      <td>-0.450</td>\n", "      <td>21.599</td>\n", "      <td>-19.631</td>\n", "      <td>12.807</td>\n", "      <td>4.0</td>\n", "      <td>-1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-08-12</th>\n", "      <td>-16708.50</td>\n", "      <td>-0.1471</td>\n", "      <td>4.853</td>\n", "      <td>27.275</td>\n", "      <td>2.048</td>\n", "      <td>1.801</td>\n", "      <td>5.0</td>\n", "      <td>-1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["              profit  profit_cg  buy_deg_ang42  buy_deg_ang252  buy_deg_ang60  \\\n", "2016-01-04 -16488.00    -0.2192         22.712           6.586         21.090   \n", "2015-03-10 -18368.36    -0.1837          4.353         -31.744        -15.324   \n", "2015-01-23 -15314.00    -0.1797         28.126          14.501         21.693   \n", "2015-07-30 -13345.00    -0.1473         -0.450          21.599        -19.631   \n", "2015-08-12 -16708.50    -0.1471          4.853          27.275          2.048   \n", "\n", "            buy_deg_ang21  p_rk_cg  rk  \n", "2016-01-04         18.350      1.0  -1  \n", "2015-03-10         12.718      2.0  -1  \n", "2015-01-23         37.728      3.0  -1  \n", "2015-07-30         12.807      4.0  -1  \n", "2015-08-12          1.801      5.0  -1  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["edge_deg.fiter.df.sort_values(by='p_rk_cg').head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["相反tail即是所有交易中利润最大的5笔交易，显然它们的rk值也都是1, 如下所示："]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>profit</th>\n", "      <th>profit_cg</th>\n", "      <th>buy_deg_ang42</th>\n", "      <th>buy_deg_ang252</th>\n", "      <th>buy_deg_ang60</th>\n", "      <th>buy_deg_ang21</th>\n", "      <th>p_rk_cg</th>\n", "      <th>rk</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2014-11-12</th>\n", "      <td>122126.0</td>\n", "      <td>0.6110</td>\n", "      <td>0.632</td>\n", "      <td>5.289</td>\n", "      <td>-1.389</td>\n", "      <td>2.873</td>\n", "      <td>191.5</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-10-31</th>\n", "      <td>94207.5</td>\n", "      <td>0.6596</td>\n", "      <td>7.114</td>\n", "      <td>18.657</td>\n", "      <td>16.574</td>\n", "      <td>-0.098</td>\n", "      <td>193.5</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-10-31</th>\n", "      <td>94207.5</td>\n", "      <td>0.6596</td>\n", "      <td>7.114</td>\n", "      <td>18.657</td>\n", "      <td>16.574</td>\n", "      <td>-0.098</td>\n", "      <td>193.5</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-03-19</th>\n", "      <td>81188.5</td>\n", "      <td>0.7848</td>\n", "      <td>-0.406</td>\n", "      <td>33.161</td>\n", "      <td>26.840</td>\n", "      <td>6.430</td>\n", "      <td>195.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-04-17</th>\n", "      <td>255794.0</td>\n", "      <td>1.2882</td>\n", "      <td>0.712</td>\n", "      <td>51.299</td>\n", "      <td>-0.378</td>\n", "      <td>1.357</td>\n", "      <td>196.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["              profit  profit_cg  buy_deg_ang42  buy_deg_ang252  buy_deg_ang60  \\\n", "2014-11-12  122126.0     0.6110          0.632           5.289         -1.389   \n", "2014-10-31   94207.5     0.6596          7.114          18.657         16.574   \n", "2014-10-31   94207.5     0.6596          7.114          18.657         16.574   \n", "2015-03-19   81188.5     0.7848         -0.406          33.161         26.840   \n", "2015-04-17  255794.0     1.2882          0.712          51.299         -0.378   \n", "\n", "            buy_deg_ang21  p_rk_cg  rk  \n", "2014-11-12          2.873    191.5   1  \n", "2014-10-31         -0.098    193.5   1  \n", "2014-10-31         -0.098    193.5   1  \n", "2015-03-19          6.430    195.0   1  \n", "2015-04-17          1.357    196.0   1  "]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["edge_deg.fiter.df.sort_values(by='p_rk_cg').tail(5)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2. 价格边裁\n", "\n", "与角度边裁流程基本一致，下面训练价格边裁，输出为价格边裁特征："]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["please wait! dump_pickle....: /Users/<USER>/abu/data/ump/ump_edge_umppricefiter\n"]}, {"data": {"text/html": ["<div>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>profit</th>\n", "      <th>profit_cg</th>\n", "      <th>buy_price_rank120</th>\n", "      <th>buy_price_rank90</th>\n", "      <th>buy_price_rank60</th>\n", "      <th>buy_price_rank252</th>\n", "      <th>p_rk_cg</th>\n", "      <th>rk</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2014-09-25</th>\n", "      <td>4368.00</td>\n", "      <td>0.0271</td>\n", "      <td>1.000</td>\n", "      <td>1.000</td>\n", "      <td>1.000</td>\n", "      <td>0.857</td>\n", "      <td>99.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-10-09</th>\n", "      <td>-11707.50</td>\n", "      <td>-0.0588</td>\n", "      <td>0.992</td>\n", "      <td>0.989</td>\n", "      <td>0.983</td>\n", "      <td>0.798</td>\n", "      <td>47.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-10-17</th>\n", "      <td>23360.00</td>\n", "      <td>0.1170</td>\n", "      <td>1.000</td>\n", "      <td>1.000</td>\n", "      <td>1.000</td>\n", "      <td>1.000</td>\n", "      <td>142.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-10-24</th>\n", "      <td>20410.88</td>\n", "      <td>0.1021</td>\n", "      <td>1.000</td>\n", "      <td>1.000</td>\n", "      <td>1.000</td>\n", "      <td>1.000</td>\n", "      <td>137.5</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-10-24</th>\n", "      <td>20410.88</td>\n", "      <td>0.1021</td>\n", "      <td>1.000</td>\n", "      <td>1.000</td>\n", "      <td>1.000</td>\n", "      <td>1.000</td>\n", "      <td>137.5</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["              profit  profit_cg  buy_price_rank120  buy_price_rank90  \\\n", "2014-09-25   4368.00     0.0271              1.000             1.000   \n", "2014-10-09 -11707.50    -0.0588              0.992             0.989   \n", "2014-10-17  23360.00     0.1170              1.000             1.000   \n", "2014-10-24  20410.88     0.1021              1.000             1.000   \n", "2014-10-24  20410.88     0.1021              1.000             1.000   \n", "\n", "            buy_price_rank60  buy_price_rank252  p_rk_cg  rk  \n", "2014-09-25             1.000              0.857     99.0   0  \n", "2014-10-09             0.983              0.798     47.0   0  \n", "2014-10-17             1.000              1.000    142.0   0  \n", "2014-10-24             1.000              1.000    137.5   0  \n", "2014-10-24             1.000              1.000    137.5   0  "]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["edge_price = AbuUmpEdgePrice.ump_edge_clf_dump(orders_pd_train)\n", "edge_price.fiter.df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3. 波动边裁\n", "\n", "下面训练波动边裁，输出为波动边裁特征："]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["please wait! dump_pickle....: /Users/<USER>/abu/data/ump/ump_edge_umpwavefiter\n"]}, {"data": {"text/html": ["<div>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>profit</th>\n", "      <th>profit_cg</th>\n", "      <th>buy_wave_score1</th>\n", "      <th>buy_wave_score2</th>\n", "      <th>buy_wave_score3</th>\n", "      <th>p_rk_cg</th>\n", "      <th>rk</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2014-09-25</th>\n", "      <td>4368.00</td>\n", "      <td>0.0271</td>\n", "      <td>0.496</td>\n", "      <td>0.454</td>\n", "      <td>0.441</td>\n", "      <td>99.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-10-09</th>\n", "      <td>-11707.50</td>\n", "      <td>-0.0588</td>\n", "      <td>0.230</td>\n", "      <td>0.072</td>\n", "      <td>-0.001</td>\n", "      <td>47.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-10-17</th>\n", "      <td>23360.00</td>\n", "      <td>0.1170</td>\n", "      <td>0.743</td>\n", "      <td>0.374</td>\n", "      <td>0.232</td>\n", "      <td>142.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-10-24</th>\n", "      <td>20410.88</td>\n", "      <td>0.1021</td>\n", "      <td>1.251</td>\n", "      <td>1.290</td>\n", "      <td>1.289</td>\n", "      <td>137.5</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-10-24</th>\n", "      <td>20410.88</td>\n", "      <td>0.1021</td>\n", "      <td>1.251</td>\n", "      <td>1.290</td>\n", "      <td>1.289</td>\n", "      <td>137.5</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["              profit  profit_cg  buy_wave_score1  buy_wave_score2  \\\n", "2014-09-25   4368.00     0.0271            0.496            0.454   \n", "2014-10-09 -11707.50    -0.0588            0.230            0.072   \n", "2014-10-17  23360.00     0.1170            0.743            0.374   \n", "2014-10-24  20410.88     0.1021            1.251            1.290   \n", "2014-10-24  20410.88     0.1021            1.251            1.290   \n", "\n", "            buy_wave_score3  p_rk_cg  rk  \n", "2014-09-25            0.441     99.0   0  \n", "2014-10-09           -0.001     47.0   0  \n", "2014-10-17            0.232    142.0   0  \n", "2014-10-24            1.289    137.5   0  \n", "2014-10-24            1.289    137.5   0  "]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["edge_wave = AbuUmpEdgeWave.ump_edge_clf_dump(orders_pd_train)\n", "edge_wave.fiter.df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4. 综合边裁\n", "\n", "综合边裁AbuUmpEdgeFull为综合多种类型特征的裁判，如下所示："]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["please wait! dump_pickle....: /Users/<USER>/abu/data/ump/ump_edge_umpfullfiter\n"]}, {"data": {"text/html": ["<div>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>profit</th>\n", "      <th>profit_cg</th>\n", "      <th>buy_deg_ang42</th>\n", "      <th>buy_deg_ang252</th>\n", "      <th>buy_deg_ang60</th>\n", "      <th>buy_deg_ang21</th>\n", "      <th>buy_price_rank120</th>\n", "      <th>buy_price_rank90</th>\n", "      <th>buy_price_rank60</th>\n", "      <th>buy_price_rank252</th>\n", "      <th>buy_wave_score1</th>\n", "      <th>buy_wave_score2</th>\n", "      <th>buy_wave_score3</th>\n", "      <th>buy_atr_std</th>\n", "      <th>p_rk_cg</th>\n", "      <th>rk</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2014-09-25</th>\n", "      <td>4368.00</td>\n", "      <td>0.0271</td>\n", "      <td>7.168</td>\n", "      <td>-3.708</td>\n", "      <td>4.342</td>\n", "      <td>2.255</td>\n", "      <td>1.000</td>\n", "      <td>1.000</td>\n", "      <td>1.000</td>\n", "      <td>0.857</td>\n", "      <td>0.496</td>\n", "      <td>0.454</td>\n", "      <td>0.441</td>\n", "      <td>1.159</td>\n", "      <td>99.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-10-09</th>\n", "      <td>-11707.50</td>\n", "      <td>-0.0588</td>\n", "      <td>-0.567</td>\n", "      <td>-6.527</td>\n", "      <td>1.309</td>\n", "      <td>1.837</td>\n", "      <td>0.992</td>\n", "      <td>0.989</td>\n", "      <td>0.983</td>\n", "      <td>0.798</td>\n", "      <td>0.230</td>\n", "      <td>0.072</td>\n", "      <td>-0.001</td>\n", "      <td>1.281</td>\n", "      <td>47.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-10-17</th>\n", "      <td>23360.00</td>\n", "      <td>0.1170</td>\n", "      <td>2.328</td>\n", "      <td>4.764</td>\n", "      <td>2.096</td>\n", "      <td>2.357</td>\n", "      <td>1.000</td>\n", "      <td>1.000</td>\n", "      <td>1.000</td>\n", "      <td>1.000</td>\n", "      <td>0.743</td>\n", "      <td>0.374</td>\n", "      <td>0.232</td>\n", "      <td>2.033</td>\n", "      <td>142.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-10-24</th>\n", "      <td>20410.88</td>\n", "      <td>0.1021</td>\n", "      <td>-0.454</td>\n", "      <td>5.532</td>\n", "      <td>2.142</td>\n", "      <td>0.931</td>\n", "      <td>1.000</td>\n", "      <td>1.000</td>\n", "      <td>1.000</td>\n", "      <td>1.000</td>\n", "      <td>1.251</td>\n", "      <td>1.290</td>\n", "      <td>1.289</td>\n", "      <td>0.192</td>\n", "      <td>137.5</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-10-24</th>\n", "      <td>20410.88</td>\n", "      <td>0.1021</td>\n", "      <td>-0.454</td>\n", "      <td>5.532</td>\n", "      <td>2.142</td>\n", "      <td>0.931</td>\n", "      <td>1.000</td>\n", "      <td>1.000</td>\n", "      <td>1.000</td>\n", "      <td>1.000</td>\n", "      <td>1.251</td>\n", "      <td>1.290</td>\n", "      <td>1.289</td>\n", "      <td>0.192</td>\n", "      <td>137.5</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["              profit  profit_cg  buy_deg_ang42  buy_deg_ang252  buy_deg_ang60  \\\n", "2014-09-25   4368.00     0.0271          7.168          -3.708          4.342   \n", "2014-10-09 -11707.50    -0.0588         -0.567          -6.527          1.309   \n", "2014-10-17  23360.00     0.1170          2.328           4.764          2.096   \n", "2014-10-24  20410.88     0.1021         -0.454           5.532          2.142   \n", "2014-10-24  20410.88     0.1021         -0.454           5.532          2.142   \n", "\n", "            buy_deg_ang21  buy_price_rank120  buy_price_rank90  \\\n", "2014-09-25          2.255              1.000             1.000   \n", "2014-10-09          1.837              0.992             0.989   \n", "2014-10-17          2.357              1.000             1.000   \n", "2014-10-24          0.931              1.000             1.000   \n", "2014-10-24          0.931              1.000             1.000   \n", "\n", "            buy_price_rank60  buy_price_rank252  buy_wave_score1  \\\n", "2014-09-25             1.000              0.857            0.496   \n", "2014-10-09             0.983              0.798            0.230   \n", "2014-10-17             1.000              1.000            0.743   \n", "2014-10-24             1.000              1.000            1.251   \n", "2014-10-24             1.000              1.000            1.251   \n", "\n", "            buy_wave_score2  buy_wave_score3  buy_atr_std  p_rk_cg  rk  \n", "2014-09-25            0.454            0.441        1.159     99.0   0  \n", "2014-10-09            0.072           -0.001        1.281     47.0   0  \n", "2014-10-17            0.374            0.232        2.033    142.0   0  \n", "2014-10-24            1.290            1.289        0.192    137.5   0  \n", "2014-10-24            1.290            1.289        0.192    137.5   0  "]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["edge_full = AbuUmpEdgeFull.ump_edge_clf_dump(orders_pd_train)\n", "edge_full.fiter.df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 5 验证边裁是否称职\n", "\n", "\n", "边裁裁决交易是否拦截的关键代码在函数AbuUmpEdgeBase.predict()中，边裁的predict()实现相对主裁来说比较复杂，大致思路如下：\n", "\n", "1. 从输入的新交易中挑选需要的特征组成x\n", "2. 将x和之前保存的训练集数据组合concatenate()，一起做数据标准化scaler\n", "3. 使用sklearn.metrics.pairwise.pairwise_distances()度量输入特征和训练集矩阵中的距离序列\n", "4. 取pairwise_distances() TOP个作为种子，继续匹配相似度\n", "5. 相似度由大到小排序，保留大于保留阀值的相似度交易数据做为最终有投票权利的\n", "6. 保留的交易认为是与新交易最相似的交易，保留的交易使用之前非均衡的rk对新交易进行投票\n", "7. 最后的判断需要大于一定比例才被结果认可，即再次启动非均衡\n", "\n", "\n", "本节开始时已介绍过边裁的裁决方式多次使用非均衡技术，对最后的结果概率进行干预，目的是使最终的裁决正确率达成非均衡的目标。\n", "这里重复已达重视，即：\n", "**非均衡技术思想是量化中很很重要的一种设计思路，因为我们量化的目标结果就是非均衡，我们想要赢的钱比输的多**。\n", "\n", "\n", "具体代码实现请阅读AbuUmpEdgeBase.predict函数\n", "\n", "备注：关于距离度量的应用阅读：第十四节 量化相关性分析应用)\n", "\n", "首先和上一节一样使用训练集不使用边裁拦截情况下进行回测，如下所示："]}, {"cell_type": "code", "execution_count": 12, "metadata": {"collapsed": true}, "outputs": [], "source": ["# 不使用边裁拦截\n", "abupy.env.g_enable_ump_edge_deg_block = False\n", "abupy.env.g_enable_ump_edge_full_block = False\n", "abupy.env.g_enable_ump_edge_price_block = False\n", "abupy.env.g_enable_ump_edge_wave_block = False\n", "\n", "abu_result_tuple_test, _ = abu.run_loop_back(read_cash,\n", "                                                   buy_factors,\n", "                                                   sell_factors,\n", "                                                   start='2014-07-26',\n", "                                                   end='2016-07-26',\n", "                                                   choice_symbols=test_choice_symbols)\n", "ABuProgress.clear_output()"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["买入后卖出的交易数量:247\n", "胜率:41.2955%\n", "平均获利期望:9.7208%\n", "平均亏损期望:-4.8754%\n", "盈亏比:1.3725\n", "所有交易收益比例和:2.8459 \n", "所有交易总盈亏和:428259.7700 \n"]}], "source": ["AbuMetricsBase.show_general(*abu_result_tuple_test, returns_cmp=True, only_info=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["在abu系统中开启边裁进行拦截前，先分别量化一下每个裁判的拦截正确率，以及总拦截率。\n", "\n", "下面编写apply_ml_features_edge()函数通过参数传入边裁，分别将四个边裁作为参数传入并对裁决结果进行记录："]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["please wait! load_pickle....: /Users/<USER>/abu/data/ump/ump_edge_umpdegfiter\n", "please wait! load_pickle....: /Users/<USER>/abu/data/ump/ump_edge_umppricefiter\n", "please wait! load_pickle....: /Users/<USER>/abu/data/ump/ump_edge_umpfullfiter\n", "please wait! load_pickle....: /Users/<USER>/abu/data/ump/ump_edge_umpwavefiter\n"]}], "source": ["import ast\n", "def apply_ml_features_edge(order, predicter):\n", "    if not isinstance(order.ml_features, dict):\n", "        # 低版本pandas dict对象取出来会成为str\n", "        ml_features = ast.literal_eval(order.ml_features)\n", "    else:\n", "        ml_features = order.ml_features\n", "    # 边裁进行裁决\n", "    edge = predicter.predict(**ml_features)\n", "    return edge.value\n", "\n", "# 选取有交易结果的数据order_has_result\n", "order_has_result = abu_result_tuple_test.orders_pd[abu_result_tuple_test.orders_pd.result != 0]\n", "# 角度边裁开始裁决\n", "order_has_result['edge_deg'] = order_has_result.apply(\n", "    apply_ml_features_edge, axis=1, args=(edge_deg,))\n", "\n", "# 价格边裁开始裁决\n", "order_has_result['edge_price'] = order_has_result.apply(\n", "    apply_ml_features_edge, axis=1, args=(edge_price,))\n", "\n", "# 综合边裁开始裁决\n", "order_has_result['edge_full'] = order_has_result.apply(\n", "    apply_ml_features_edge, axis=1, args=(edge_full,))\n", "\n", "# 波动边裁开始裁决\n", "order_has_result['edge_wave'] = order_has_result.apply(\n", "    apply_ml_features_edge, axis=1, args=(edge_wave,))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["下面统计一下四个边裁总拦截数量以及拦截率："]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["四个边裁拦截交易总数120， 拦截率48.58%\n"]}, {"data": {"text/html": ["<div>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>edge_deg</th>\n", "      <th>edge_price</th>\n", "      <th>edge_full</th>\n", "      <th>edge_wave</th>\n", "      <th>edge_block</th>\n", "      <th>result</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2014-09-25</th>\n", "      <td>0</td>\n", "      <td>-1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-10-10</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-10-30</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-10-30</th>\n", "      <td>0</td>\n", "      <td>-1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-11-04</th>\n", "      <td>0</td>\n", "      <td>-1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            edge_deg  edge_price  edge_full  edge_wave  edge_block  result\n", "2014-09-25         0          -1          0          0          -1      -1\n", "2014-10-10         0           0          0         -1          -1      -1\n", "2014-10-30         0           0          0         -1          -1      -1\n", "2014-10-30         0          -1          0          1          -1      -1\n", "2014-11-04         0          -1          0          1          -1      -1"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["block_pd = order_has_result.filter(regex='^edge_*')\n", "\"\"\"\n", "    由于predict返回的结果中1代表win top\n", "    但是我们只需要知道loss_top，所以只保留-1, 其他1转换为0。\n", "\"\"\"\n", "block_pd['edge_block'] = \\\n", "    np.where(np.min(block_pd, axis=1) == -1, -1, 0)\n", "\n", "# 拿出真实的交易结果\n", "block_pd['result'] = order_has_result['result']\n", "# 拿出-1的结果，即判定loss_top的\n", "block_pd = block_pd[block_pd.edge_block == -1]\n", "\n", "print('四个边裁拦截交易总数{}， 拦截率{:.2f}%'.format(\n", "    block_pd.shape[0],\n", "    block_pd.shape[0] / order_has_result.shape[0] * 100))\n", "block_pd.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["下面统计每一个边裁拦截正确率以及拦截数量："]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["角度边裁拦截正确率55.56%, 拦截交易数量18\n", "综合边裁拦截正确率0.00%, 拦截交易数量0\n", "波动边裁拦截正确率61.67%, 拦截交易数量60\n", "价格边裁拦截正确率63.89%, 拦截交易数量72\n"]}], "source": ["from sklearn import metrics\n", "def sub_edge_show(edge_name):\n", "    sub_edge_block_pd = order_has_result[(order_has_result[edge_name] == -1)]\n", "    accuracy = 0\n", "    if sub_edge_block_pd.shape[0] > 0:\n", "        accuracy = metrics.accuracy_score(sub_edge_block_pd[edge_name], sub_edge_block_pd.result) * 100\n", "    return accuracy, sub_edge_block_pd.shape[0]\n", "\n", "print('角度边裁拦截正确率{0:.2f}%, 拦截交易数量{1:}'.format(*sub_edge_show('edge_deg')))\n", "print('综合边裁拦截正确率{0:.2f}%, 拦截交易数量{1:}'.format(*sub_edge_show('edge_full')))\n", "print('波动边裁拦截正确率{0:.2f}%, 拦截交易数量{1:}'.format(*sub_edge_show('edge_wave')))\n", "print('价格边裁拦截正确率{0:.2f}%, 拦截交易数量{1:}'.format(*sub_edge_show('edge_price')))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["上面结果可以看到综合边裁没有能够拦截到一个交易，这是因为综合边裁使用的特征很多，但是测试集的交易数量太少，没有能在测试集中找到类似的交易特征描述。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 6. 在abu系统中开启边裁拦截模式"]}, {"cell_type": "markdown", "metadata": {}, "source": ["验证边裁后就可以在回测或者实盘模块中开启边裁拦截，针对策略产生的交易信号进行买入拦截, 在之前针对测试集开启主裁拦截代码的基础上，我们开启边裁拦截开关："]}, {"cell_type": "code", "execution_count": 17, "metadata": {"collapsed": true}, "outputs": [], "source": ["# 开启边裁\n", "abupy.env.g_enable_ump_edge_deg_block = True\n", "abupy.env.g_enable_ump_edge_full_block = True\n", "abupy.env.g_enable_ump_edge_price_block = True\n", "abupy.env.g_enable_ump_edge_wave_block = True\n", "\n", "abu_result_tuple_test_edge, _ = abu.run_loop_back(read_cash,\n", "                                                   buy_factors,\n", "                                                   sell_factors,\n", "                                                   start='2014-07-26',\n", "                                                   end='2016-07-26',\n", "                                                   choice_symbols=test_choice_symbols)\n", "ABuProgress.clear_output()"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["买入后卖出的交易数量:127\n", "胜率:44.8819%\n", "平均获利期望:10.0347%\n", "平均亏损期望:-5.1565%\n", "盈亏比:1.4254\n", "所有交易收益比例和:2.1103 \n", "所有交易总盈亏和:245799.1100 \n"]}], "source": ["AbuMetricsBase.show_general(*abu_result_tuple_test_edge, returns_cmp=True, only_info=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["对比未开启边裁拦截的情况："]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["买入后卖出的交易数量:247\n", "胜率:41.2955%\n", "平均获利期望:9.7208%\n", "平均亏损期望:-4.8754%\n", "盈亏比:1.3725\n", "所有交易收益比例和:2.8459 \n", "所有交易总盈亏和:428259.7700 \n"]}], "source": ["AbuMetricsBase.show_general(*abu_result_tuple_test, returns_cmp=True, only_info=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["如上所示使用边裁进行交易策略的拦截胜率，盈亏比相较上一节的主裁拦截都有所提高，且边裁拦截了大量的交易可以节省佣金，更重大的意义在于边裁避免了重大的风险，因为边裁的拦截目标是训练集top loss。\n", "\n", "在实盘中往往根据资金量，策略并行数量等等因素计算出每日最佳交易数量，通过选股模块 ＋ ump模块使得每天实际交易数量逼近每日最佳交易数量，在之后的章节中会完整示例。\n", "\n", "边裁的效果提升的方法与主裁类似，最为重要的依然是：**训练更多交易数据，更多策略来提升边裁的拦截水平及拦截认知范围，给每一个裁判看更多的比赛录像，提高比赛录像水准，如从多个不同视角录制比赛，这样裁判才能在实际比赛中做出最正确的判断。**"]}, {"cell_type": "markdown", "metadata": {}, "source": ["既然裁判都已经准备好了，那么就可以准备开始比赛了，[一定要赢得这场胜利，即使一切都不存在！](https://y.qq.com/n/yqq/song/0032b8613iuFFI.html)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["小结：本节的示例与《量化交易之路》中讲解的边裁部分内容互为补充，请对照阅读。"]}, {"cell_type": "markdown", "metadata": {"collapsed": true}, "source": ["#### abu量化文档目录章节\n", "\n", "1. [择时策略的开发](http://www.abuquant.com/lecture/lecture_1.html)\n", "2. [择时策略的优化](http://www.abuquant.com/lecture/lecture_2.html)\n", "3. [滑点策略与交易手续费](http://www.abuquant.com/lecture/lecture_3.html)\n", "4. [多支股票择时回测与仓位管理](http://www.abuquant.com/lecture/lecture_4.html)\n", "5. [选股策略的开发](http://www.abuquant.com/lecture/lecture_5.html)\n", "6. [回测结果的度量](http://www.abuquant.com/lecture/lecture_6.html)\n", "7. [寻找策略最优参数和评分](http://www.abuquant.com/lecture/lecture_7.html)\n", "8. [A股市场的回测](http://www.abuquant.com/lecture/lecture_8.html)\n", "9. [港股市场的回测](http://www.abuquant.com/lecture/lecture_9.html)\n", "10. [比特币，莱特币的回测](http://www.abuquant.com/lecture/lecture_10.html)\n", "11. [期货市场的回测](http://www.abuquant.com/lecture/lecture_11.html)\n", "12. [机器学习与比特币示例](http://www.abuquant.com/lecture/lecture_12.html)\n", "13. [量化技术分析应用](http://www.abuquant.com/lecture/lecture_13.html)\n", "14. [量化相关性分析应用](http://www.abuquant.com/lecture/lecture_14.html)\n", "15. [量化交易和搜索引擎](http://www.abuquant.com/lecture/lecture_15.html)\n", "16. [UMP主裁交易决策](http://www.abuquant.com/lecture/lecture_16.html)\n", "17. [UMP边裁交易决策](http://www.abuquant.com/lecture/lecture_17.html)\n", "18. [自定义裁判决策交易](http://www.abuquant.com/lecture/lecture_18.html)\n", "19. [数据源](http://www.abuquant.com/lecture/lecture_19.html)\n", "20. [A股全市场回测](http://www.abuquant.com/lecture/lecture_20.html)\n", "21. [A股UMP决策](http://www.abuquant.com/lecture/lecture_21.html)\n", "22. [美股全市场回测](http://www.abuquant.com/lecture/lecture_22.html)\n", "23. [美股UMP决策](http://www.abuquant.com/lecture/lecture_23.html)\n", "\n", "abu量化系统文档教程持续更新中，请关注公众号中的更新提醒。\n", "\n", "#### 《量化交易之路》目录章节及随书代码地址\n", "\n", "1. [第二章 量化语言——Python](https://github.com/bbfamily/abu/tree/master/ipython/第二章-量化语言——Python.ipynb)\n", "2. [第三章 量化工具——NumPy](https://github.com/bbfamily/abu/tree/master/ipython/第三章-量化工具——NumPy.ipynb)\n", "3. [第四章 量化工具——pandas](https://github.com/bbfamily/abu/tree/master/ipython/第四章-量化工具——pandas.ipynb)\n", "4. [第五章 量化工具——可视化](https://github.com/bbfamily/abu/tree/master/ipython/第五章-量化工具——可视化.ipynb)\n", "5. [第六章 量化工具——数学：你一生的追求到底能带来多少幸福](https://github.com/bbfamily/abu/tree/master/ipython/第六章-量化工具——数学.ipynb)\n", "6. [第七章 量化系统——入门：三只小猪股票投资的故事](https://github.com/bbfamily/abu/tree/master/ipython/第七章-量化系统——入门.ipynb)\n", "7. [第八章 量化系统——开发](https://github.com/bbfamily/abu/tree/master/ipython/第八章-量化系统——开发.ipynb)\n", "8. [第九章 量化系统——度量与优化](https://github.com/bbfamily/abu/tree/master/ipython/第九章-量化系统——度量与优化.ipynb)\n", "9. [第十章 量化系统——机器学习•猪老三](https://github.com/bbfamily/abu/tree/master/ipython/第十章-量化系统——机器学习•猪老三.ipynb)\n", "10. [第十一章 量化系统——机器学习•ABU](https://github.com/bbfamily/abu/tree/master/ipython/第十一章-量化系统——机器学习•ABU.ipynb)\n", "11. [附录A 量化环境部署](https://github.com/bbfamily/abu/tree/master/ipython/附录A-量化环境部署.ipynb)\n", "12. [附录B 量化相关性分析](https://github.com/bbfamily/abu/tree/master/ipython/附录B-量化相关性分析.ipynb)\n", "13. [附录C 量化统计分析及指标应用](https://github.com/bbfamily/abu/tree/master/ipython/附录C-量化统计分析及指标应用.ipynb)\n", "\n", "[更多阿布量化量化技术文章](http://www.abuquant.com/article)\n", "\n", "\n", "更多关于量化交易相关请阅读[《量化交易之路》](http://www.abuquant.com/books/quantify-trading-road.html)\n", "\n", "更多关于量化交易与机器学习相关请阅读[《机器学习之路》](http://www.abuquant.com/books/machine-learning-road.html)\n", "\n", "更多关于abu量化系统请关注微信公众号: abu_quant\n", "\n", "![](./image/qrcode.jpg)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.2"}}, "nbformat": 4, "nbformat_minor": 2}