#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
abupy PickStockBu模块精准勘探

勘探目标：
1. 列出所有开箱即用的选股因子类
2. 详细说明每个选股因子的参数配置
3. 提供完整的stock_picks字典列表示例
"""

import os
import sys

# 添加abupy路径
sys.path.insert(0, os.path.abspath('.'))

def analyze_pickstock_module():
    """分析PickStockBu模块结构"""
    print("="*80)
    print("🔍 abupy PickStockBu模块精准勘探")
    print("="*80)
    
    module_info = {
        "module_path": "abupy/PickStockBu",
        "base_class": "AbuPickStockBase",
        "available_factors": [
            "AbuPickRegressAngMinMax",
            "AbuPickStockPriceMinMax", 
            "AbuPickSimilarNTop",
            "AbuPickStockShiftDistance",
            "AbuPickStockNTop"
        ],
        "total_count": 5
    }
    
    print(f"📁 模块路径: {module_info['module_path']}")
    print(f"🏗️  基类: {module_info['base_class']}")
    print(f"📊 可用选股因子总数: {module_info['total_count']}")
    
    print(f"\n📋 所有可用选股因子:")
    for i, factor in enumerate(module_info['available_factors'], 1):
        print(f"   {i}. {factor}")
    
    return module_info


def analyze_base_class_parameters():
    """分析基类通用参数"""
    print("\n" + "="*80)
    print("🏗️  AbuPickStockBase基类通用参数")
    print("="*80)
    
    base_params = {
        "common_parameters": [
            {
                "name": "reversed",
                "type": "bool",
                "default": "False",
                "description": "是否反转选股结果，True表示选择不符合条件的股票"
            },
            {
                "name": "xd",
                "type": "int", 
                "default": "ABuEnv.g_market_trade_year (约252个交易日)",
                "description": "选股分析周期，即分析多少个交易日的数据"
            },
            {
                "name": "min_xd",
                "type": "int",
                "default": "xd / 2",
                "description": "最小选股周期，数据不足时的最小要求"
            }
        ]
    }
    
    print("📋 基类通用参数 (所有选股因子都支持):")
    for param in base_params["common_parameters"]:
        print(f"\n   • {param['name']} ({param['type']})")
        print(f"     默认值: {param['default']}")
        print(f"     描述: {param['description']}")
    
    return base_params


def analyze_individual_factors():
    """分析每个选股因子的详细参数"""
    print("\n" + "="*80)
    print("📊 各选股因子详细参数分析")
    print("="*80)
    
    factors_detail = {
        "AbuPickRegressAngMinMax": {
            "friendly_name": "价格拟合角度选股因子",
            "description": "基于价格走势的线性拟合角度进行选股，适用于趋势分析",
            "file_path": "abupy/PickStockBu/ABuPickRegressAngMinMax.py",
            "specific_parameters": [
                {
                    "name": "threshold_ang_min",
                    "type": "float",
                    "default": "-np.inf",
                    "description": "最小角度阈值，角度大于此值才被选中"
                },
                {
                    "name": "threshold_ang_max", 
                    "type": "float",
                    "default": "np.inf",
                    "description": "最大角度阈值，角度小于此值才被选中"
                }
            ],
            "use_case": "选择上升趋势或下降趋势的股票",
            "example_config": {
                "class": "AbuPickRegressAngMinMax",
                "threshold_ang_min": 0.0,
                "threshold_ang_max": 45.0,
                "reversed": False
            }
        },
        
        "AbuPickStockPriceMinMax": {
            "friendly_name": "价格区间选股因子",
            "description": "基于股票价格区间进行选股，过滤价格过高或过低的股票",
            "file_path": "abupy/PickStockBu/ABuPickStockPriceMinMax.py",
            "specific_parameters": [
                {
                    "name": "threshold_price_min",
                    "type": "float",
                    "default": "-np.inf",
                    "description": "最小价格阈值，股票最低价必须大于此值"
                },
                {
                    "name": "threshold_price_max",
                    "type": "float", 
                    "default": "np.inf",
                    "description": "最大价格阈值，股票最高价必须小于此值"
                }
            ],
            "use_case": "选择特定价格区间的股票，避免仙股或高价股",
            "example_config": {
                "class": "AbuPickStockPriceMinMax",
                "threshold_price_min": 10.0,
                "threshold_price_max": 200.0,
                "reversed": False
            }
        },
        
        "AbuPickSimilarNTop": {
            "friendly_name": "相似度选股因子",
            "description": "基于与目标股票的相似度进行选股，选择走势相似的股票",
            "file_path": "abupy/PickStockBu/ABuPickSimilarNTop.py",
            "specific_parameters": [
                {
                    "name": "similar_stock",
                    "type": "str",
                    "default": "无默认值(必需)",
                    "description": "目标相似度股票代码，作为比较基准"
                },
                {
                    "name": "n_top",
                    "type": "int",
                    "default": "100",
                    "description": "选取相似度最高的前n_top个股票"
                },
                {
                    "name": "threshold_similar_min",
                    "type": "float",
                    "default": "-np.inf", 
                    "description": "最小相似度阈值"
                },
                {
                    "name": "threshold_similar_max",
                    "type": "float",
                    "default": "np.inf",
                    "description": "最大相似度阈值"
                },
                {
                    "name": "rolling",
                    "type": "bool",
                    "default": "False",
                    "description": "是否使用时间加权计算相似度"
                },
                {
                    "name": "corr_type",
                    "type": "ECoreCorrType",
                    "default": "E_CORE_TYPE_PEARS",
                    "description": "相似度计算算法类型"
                }
            ],
            "use_case": "选择与某只标杆股票走势相似的股票",
            "example_config": {
                "class": "AbuPickSimilarNTop",
                "similar_stock": "usAAPL",
                "n_top": 50,
                "rolling": False
            }
        },
        
        "AbuPickStockShiftDistance": {
            "friendly_name": "位移路程比选股因子",
            "description": "基于价格位移路程比进行选股，分析价格波动特征",
            "file_path": "abupy/PickStockBu/ABuPickStockDemo.py",
            "specific_parameters": [
                {
                    "name": "threshold_sd",
                    "type": "float",
                    "default": "2.0",
                    "description": "位移路程比阈值"
                },
                {
                    "name": "threshold_max_cnt",
                    "type": "int",
                    "default": "4",
                    "description": "超过阈值的最大次数"
                },
                {
                    "name": "threshold_min_cnt",
                    "type": "int",
                    "default": "1",
                    "description": "超过阈值的最小次数"
                }
            ],
            "use_case": "选择价格波动特征符合要求的股票",
            "example_config": {
                "class": "AbuPickStockShiftDistance",
                "threshold_sd": 2.5,
                "threshold_max_cnt": 3,
                "threshold_min_cnt": 1
            }
        },
        
        "AbuPickStockNTop": {
            "friendly_name": "涨跌幅排名选股因子",
            "description": "基于一段时间内的涨跌幅排名进行选股，选择表现最好或最差的股票",
            "file_path": "abupy/PickStockBu/ABuPickStockDemo.py",
            "specific_parameters": [
                {
                    "name": "symbol_pool",
                    "type": "List[str]",
                    "default": "[]",
                    "description": "参与排名比较的股票池"
                },
                {
                    "name": "n_top",
                    "type": "int",
                    "default": "3",
                    "description": "选取排名前n_top的股票数量"
                },
                {
                    "name": "direction_top",
                    "type": "int",
                    "default": "1",
                    "description": "选择方向，1选涨幅最大，-1选跌幅最大"
                }
            ],
            "use_case": "选择涨幅或跌幅排名靠前的股票，适用于动量或反转策略",
            "example_config": {
                "class": "AbuPickStockNTop",
                "symbol_pool": ["usTSLA", "usAAPL", "usGOOG", "usMSFT"],
                "n_top": 2,
                "direction_top": 1
            }
        }
    }
    
    for factor_name, info in factors_detail.items():
        print(f"\n🔸 {factor_name}")
        print(f"   📝 友好名称: {info['friendly_name']}")
        print(f"   📄 功能描述: {info['description']}")
        print(f"   📁 文件位置: {info['file_path']}")
        print(f"   🎯 应用场景: {info['use_case']}")
        
        print(f"   ⚙️  特定参数:")
        for param in info['specific_parameters']:
            print(f"      • {param['name']} ({param['type']})")
            print(f"        默认: {param['default']}")
            print(f"        描述: {param['description']}")
        
        print(f"   📋 配置示例:")
        print(f"      {info['example_config']}")
        print()
    
    return factors_detail


def generate_complete_examples():
    """生成完整的stock_picks配置示例"""
    print("\n" + "="*80)
    print("📋 完整的stock_picks配置示例")
    print("="*80)
    
    examples = {
        "example_1": {
            "name": "趋势+价格双重筛选",
            "description": "选择上升趋势且价格在合理区间的股票",
            "config": [
                {
                    'class': 'AbuPickRegressAngMinMax',
                    'threshold_ang_min': 0.0,
                    'threshold_ang_max': 45.0,
                    'reversed': False
                },
                {
                    'class': 'AbuPickStockPriceMinMax',
                    'threshold_price_min': 10.0,
                    'threshold_price_max': 200.0,
                    'reversed': False
                }
            ]
        },
        
        "example_2": {
            "name": "相似度+涨幅排名组合",
            "description": "选择与AAPL相似且涨幅排名靠前的股票",
            "config": [
                {
                    'class': 'AbuPickSimilarNTop',
                    'similar_stock': 'usAAPL',
                    'n_top': 30,
                    'rolling': False
                },
                {
                    'class': 'AbuPickStockNTop',
                    'symbol_pool': ['usTSLA', 'usAAPL', 'usGOOG', 'usMSFT', 'usAMZN'],
                    'n_top': 3,
                    'direction_top': 1,
                    'xd': 60
                }
            ]
        },
        
        "example_3": {
            "name": "反转策略选股",
            "description": "选择下跌趋势的股票，用于反转策略",
            "config": [
                {
                    'class': 'AbuPickRegressAngMinMax',
                    'threshold_ang_min': -45.0,
                    'threshold_ang_max': 0.0,
                    'reversed': False
                },
                {
                    'class': 'AbuPickStockNTop',
                    'symbol_pool': ['002230', '300104', '300059', '601766', '600085'],
                    'n_top': 2,
                    'direction_top': -1,  # 选择跌幅最大的
                    'xd': 30
                }
            ]
        },
        
        "example_4": {
            "name": "波动特征筛选",
            "description": "基于价格波动特征和位移路程比进行选股",
            "config": [
                {
                    'class': 'AbuPickStockShiftDistance',
                    'threshold_sd': 2.5,
                    'threshold_max_cnt': 3,
                    'threshold_min_cnt': 1,
                    'xd': 120
                },
                {
                    'class': 'AbuPickStockPriceMinMax',
                    'threshold_price_min': 5.0,
                    'threshold_price_max': 100.0,
                    'reversed': False
                }
            ]
        }
    }
    
    print("🎯 四个不同策略的完整配置示例:")
    
    for example_key, example_info in examples.items():
        print(f"\n📌 示例{example_key[-1]}: {example_info['name']}")
        print(f"   📝 策略描述: {example_info['description']}")
        print(f"   ⚙️  配置代码:")
        print(f"   stock_picks = {example_info['config']}")
        print()
    
    return examples


def generate_usage_guide():
    """生成选股因子使用指南"""
    print("\n" + "="*80)
    print("📖 选股因子使用指南")
    print("="*80)
    
    usage_guide = {
        "basic_usage": {
            "title": "基础使用方式",
            "steps": [
                "1. 在run_loop_back函数中传入stock_picks参数",
                "2. stock_picks是一个字典列表，每个字典代表一个选股因子",
                "3. 每个字典必须包含'class'键，指定选股因子类",
                "4. 其他键值对作为该选股因子的参数"
            ],
            "code_example": """
# 基础使用示例
import abupy
from abupy import AbuPickRegressAngMinMax, AbuPickStockPriceMinMax

stock_picks = [
    {
        'class': AbuPickRegressAngMinMax,
        'threshold_ang_min': 0.0,
        'reversed': False
    },
    {
        'class': AbuPickStockPriceMinMax,
        'threshold_price_min': 10.0,
        'threshold_price_max': 200.0,
        'reversed': False
    }
]

# 在回测中使用
abu_result_tuple, kl_pd_manager = abupy.run_loop_back(
    read_cash=1000000,
    buy_factors=buy_factors,
    sell_factors=sell_factors,
    stock_picks=stock_picks,  # 选股因子配置
    choice_symbols=None  # None表示全市场选股
)
"""
        },
        
        "advanced_usage": {
            "title": "高级使用技巧",
            "tips": [
                "• 多个选股因子采用'与'逻辑，即股票必须通过所有选股因子的筛选",
                "• 使用reversed=True可以反转选股结果，选择不符合条件的股票",
                "• xd参数控制选股分析的时间周期，不同策略可能需要不同周期",
                "• 选股因子可以与买入因子的stock_pickers参数配合使用，实现动态选股",
                "• 相似度选股因子计算量较大，建议在股票池较小时使用"
            ]
        },
        
        "performance_considerations": {
            "title": "性能优化建议",
            "recommendations": [
                "• 选股因子的执行顺序会影响性能，建议将筛选力度大的因子放在前面",
                "• 使用AbuPickStockMaster.do_pick_stock_with_process()进行并行选股",
                "• 相似度计算和位移路程比计算较为耗时，谨慎使用",
                "• 合理设置xd和min_xd参数，避免数据不足导致的选股失败"
            ]
        }
    }
    
    for section_key, section_info in usage_guide.items():
        print(f"\n📋 {section_info['title']}")
        print("-" * 40)
        
        if 'steps' in section_info:
            for step in section_info['steps']:
                print(f"   {step}")
        
        if 'tips' in section_info:
            for tip in section_info['tips']:
                print(f"   {tip}")
        
        if 'recommendations' in section_info:
            for rec in section_info['recommendations']:
                print(f"   {rec}")
        
        if 'code_example' in section_info:
            print(f"\n   代码示例:")
            print(section_info['code_example'])
    
    return usage_guide


def main():
    """主勘探函数"""
    print("🚀 abupy PickStockBu模块精准勘探")
    print("为'选股器'页面设计提供核心技术依据")
    print("="*80)
    
    # 执行各项勘探
    results = []
    
    # 勘探1: 模块结构分析
    module_info = analyze_pickstock_module()
    results.append(("模块结构分析", True))
    
    # 勘探2: 基类参数分析
    base_params = analyze_base_class_parameters()
    results.append(("基类参数分析", True))
    
    # 勘探3: 各因子详细分析
    factors_detail = analyze_individual_factors()
    results.append(("各因子详细分析", True))
    
    # 勘探4: 完整配置示例
    examples = generate_complete_examples()
    results.append(("完整配置示例", True))
    
    # 勘探5: 使用指南
    usage_guide = generate_usage_guide()
    results.append(("使用指南", True))
    
    # 汇总结果
    print("\n" + "="*80)
    print("📊 勘探结果汇总")
    print("="*80)
    
    print("✅ 完成的勘探项目:")
    for analysis_name, status in results:
        print(f"   • {analysis_name}")
    
    print(f"\n🎯 关键发现:")
    print("   1. ✅ abupy提供5个开箱即用的选股因子")
    print("   2. ✅ 所有选股因子都支持3个基础参数(reversed, xd, min_xd)")
    print("   3. ✅ 选股因子采用'与'逻辑组合，必须全部通过")
    print("   4. ✅ 支持趋势、价格、相似度、波动等多种选股策略")
    print("   5. ✅ 提供了4个完整的多因子组合配置示例")
    
    print(f"\n📝 对'选股器'页面设计的建议:")
    print("   - 按功能分类展示选股因子(趋势类、价格类、相似度类等)")
    print("   - 为每个因子提供参数配置界面和说明文档")
    print("   - 支持多因子组合配置和预览功能")
    print("   - 提供常用配置模板和示例")
    print("   - 添加参数验证和性能提示功能")


if __name__ == "__main__":
    main()
