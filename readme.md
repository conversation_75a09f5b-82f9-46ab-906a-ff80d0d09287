![](./img/head.png)

### 索引

| 内容 | 位置 | 
| ------| ------ | 
| 阿布量化系统源代码 | abupy目录 |
| 阿布量化使用教程 | abupy_lecture目录 |
| 阿布量化非编程界面操作 | abupy_ui目录 |
| 《量化交易之路》示例代码 | ipython／python目录| 
| 《机器学习之路》示例代码 | https://github.com/maxmon/abu_ml | 


###  🏆 [览器访问网址: https://www.abuquant.com](https://www.abuquant.com)

1. 🇨🇳 [沪深市场量化示例分析列表:](https://www.abuquant.com/cn1/cn_symbol_list.html)

	* 🇨🇳 [上证指数周报示例量化分析:](https://www.abuquant.com/abu_context/output_cn_week1/report/sh000001/index.html)
	* 🇨🇳 [上证指数日报示例量化分析:](https://www.abuquant.com/abu_context/output_cn_day1/report/sh000001/index.html)


2. 🇺🇸 [美股市场量化示例分析列表:](https://www.abuquant.com/us1/us_symbol_list.html)

	* 🇺🇸 [阿里巴巴-量化研究报告:](https://www.abuquant.com/abu_context/output_us_week1/report/usBABA/index.html)

3. 🚩 [港股市场量化示例分析列表:](https://www.abuquant.com/hk1/hk_symbol_list.html)
	* 🚩 [小米集团-量化研究报告:](https://www.abuquant.com/abu_context/output_hk_week1/report/hk01810/index.html)



4. 📡 [特征交织信号策略:](https://www.abuquant.com/find_itl.html)


	* 📐[上升三角整理向上突破:](https://www.abuquant.com/itl_feature1/pattern_90triangle_up_up_break_symbol_list.html)
	* 🌊[波浪理论回调浪c点:](https://www.abuquant.com/itl_feature1/dn_5wr_c_revert_symbol_list.html)

	* ✂️[升趋势向下破位风险:](https://www.abuquant.com/itl_feature1/trend_breakthrough_risk_symbol_list.html)

	* 🐶[c点将上涨到d点完成降赛福:](https://www.abuquant.com/itl_feature1/dn_cypher_xabc_predict_symbol_list.html)
	* ☯[缠论中继❸买向上:](https://www.abuquant.com/itl_feature1/dn_right_tangle_three_symbol_list.html)


	* 🚩[降楔型整理向上突破:](https://www.abuquant.com/itl_feature1/pattern_wedge_dn_break_symbol_list.html)
	* 🦇[蝙蝠到达升蝙蝠d点:](https://www.abuquant.com/itl_feature1/up_bat_abcd_regular_symbol_list.html)

	* ☯️[缠论中继❷买向上:](https://www.abuquant.com/itl_feature1/dn_right_tangle_two_symbol_list.html)

	* 🌊[波浪理论回调浪反转:](https://www.abuquant.com/itl_feature1/up_5wr_c_revert_symbol_list.html)
	
	* ⚔️[趋势线突破机会:](https://www.abuquant.com/itl_feature1/trend_breakthrough_chance_symbol_list.html)



	* ☯️[缠论中继❸卖向下:](https://www.abuquant.com/itl_feature1/up_right_tangle_three_symbol_list.html)

	* 🐶[降赛福d点完成:](https://www.abuquant.com/itl_feature1/dn_cypher_xabc_predict_symbol_list.html)

	* ▴▲▴[头肩顶向下突破:](https://www.abuquant.com/itl_feature1/trend_support_encounter_dn_symbol_list.html)
	* ✂️[升趋势强支撑破位可能:](https://www.abuquant.com/itl_feature1/pattern_head_top_break_symbol_list.html)

	* 🌊[上涨回调浪中继b:](https://www.abuquant.com/itl_feature1/dn_5wr_b_pos_symbol_list.html)

	* 🦊[降伽利d点完成:](https://www.abuquant.com/itl_feature1/dn_gartley_abcd_predict_symbol_list.html)

	* ☯️[缠论中继❷卖向下:](https://www.abuquant.com/itl_feature1/up_right_tangle_two_symbol_list.html)
	* ✂️[升趋势强支撑破位可能:](https://www.abuquant.com/itl_feature1/pattern_head_top_break_symbol_list.html)

	* ❐[下跌矩形达成下跌目标:](https://www.abuquant.com/itl_feature1/pattern_rect_dn_up_target_symbol_list.html)

	* 🌊[波浪理论下跌中继c:](https://www.abuquant.com/itl_feature1/up_5wr_c_regular_symbol_list.html)


	* 🦀️[升螃蟹d点完成:](https://www.abuquant.com/itl_feature1/up_crab_abcd_predict_symbol_list.html)
	* ⚑[降旗形整理向上突破:](https://www.abuquant.com/itl_feature1/pattern_flag_dn_break_symbol_list.html)

	* ☯[缠论反转❶买向上:](https://www.abuquant.com/itl_feature1/dn_lr_tg_one_symbol_list.html)

	* 🌊[波浪理论上涨中继c:](https://www.abuquant.com/itl_feature1/dn_5wr_c_regular_symbol_list.html)


	* 🦇[升蝙蝠第3反弹目标:](https://www.abuquant.com/itl_feature1/up_bat_hm_t3_symbol_list.html)
	* 🚩[升楔型达成整理下跌目标:](https://www.abuquant.com/itl_feature1/pattern_wedge_up_target_symbol_list.html)

	* ☯[缠论中枢b刚完成:](https://www.abuquant.com/itl_feature1/up_left_ds_large_tg_one_symbol_list.html)

	
5. 🅚[K线复合信号策略:](https://www.abuquant.com/find_kc.html)


	* [上涨Pinbar组合:](https://www.abuquant.com/kc_feature1/up_pinbar_symbol_list.html)
	* [倾盆大雨:](https://www.abuquant.com/kc_feature1/dn_down_pour_symbol_list.html)
	* [上涨Pinbar组合:](https://www.abuquant.com/kc_feature1/up_pinbar_symbol_list.html)
	* [低档五阳线:](https://www.abuquant.com/kc_feature1/up_lp_5yang_symbol_list.html)

	* [上涨身怀六甲:](https://www.abuquant.com/kc_feature1/up_pregnant_6m_symbol_list.html)
	* [定海神针:](https://www.abuquant.com/kc_feature1/up_1needle_symbol_list.html)

### 特点

* 使用多种机器学习技术智能优化策略
* 在实盘中指导策略进行交易，提高策略的实盘效果，战胜市场

### 支持的投资市场:

* 美股，A股，港股
* 期货，期权
* 比特币，莱特币

### 工程设计目标：

* 分离基础策略和策略优化监督模块
* 提高灵活度和适配性

* 量化系统

阿布量化综合AI大数据系统, K线形态系统, 缠论，波浪理论，谐波理论，突破，整理形态分析（头肩形态，三头，三角，旗形，楔形，矩形），
经典指标系统, 走势趋势分析系统, 时间序列维度系统, 统计概率系统, 传统均线系统对投资品种进行深度量化分析, 彻底跨越用户复杂的代码量化阶段, 更适合普通人群使用, 迈向量化2.0时代.

* 量化模型

上述系统中结合上百种子量化模型, 如: 金融时间序列损耗模型, 深度形态质量评估模型, 多空形态组合评定模型, 多头形态止损策略模型, 空头形态回补策略模型, 大数据K线形态历史组合拟合模型, 交易持仓心态模型, 多巴胺量化模型, 惯性残存阻力支撑模型, 多空互换报复概率模型, 强弱对抗模型, 趋势角度变化率模型, 联动分析模型, 时间序列的过激反应模型, 迟钝报复反应模型, 趋势启动速度模型, 配对对冲模型等.

* AI量化

阿布量化针对AI人工智能从底层开发算法, 构建适合量化体系的人工智能AI系统, 训练了数个从不同角度识别量化特征的评分模型，整体上分为三个系别：物理模型组、多巴胺生物模型组、量化形态模型组。不同系别模型群从不同角度(主要物理交易实体分析、人群心理、图表等三个方向)评估走势，系别的模型群是由若干个独有的识别算法和参数遗传淘汰，组成族群，加权投票评分. 

* 量化策略

阿布量化结合了传统基于代码策的量化系统, 对未来择时信号发出时机的预判, 系统基于数百种简单种子交易策略，衍生出更多的量化交易策略新策略在这些种子基础上不断自我学习、自我成长，不断分裂，适者生存，淘汰选择机制下繁衍，目前应用的量化买入卖出信号策略共计18496种。

* 量化应用

阿布量化结合多种量化分析数据构建了数百种量化应用, 
如: AI高能预警, AI高光时刻, 智能预测涨跌幅, 下跌五浪量化, 上涨五浪量化, 缠论，波浪理论，谐波理论，突破，整理形态分析（头肩形态，三头，三角，旗形，楔形，矩形），
阻力支撑强度分析, 上升三角形突破, 下降三角形, 三重底 (头肩底), 三重顶 (头肩顶), 圆弧顶, 圆弧底, 乌云盖顶形态, 上升三部曲形态, 好友反攻形态, 单针探底形态, 射击之星形态, 多方炮形态, 上涨镊子线, 向上突破箱体, 跳空突破缺口, 黄金分割线量化, 趋势跟踪信号, 均值回复信号, 止损风险控制量化, 止盈利润保护量化, 综合指标分析等.


## 安装

### 部署

推荐使用Anaconda部署Python环境，详见 [量化环境部署](https://github.com/bbfamily/abu/blob/master/abupy_lecture/0-abupy%E9%87%8F%E5%8C%96%E7%8E%AF%E5%A2%83%E9%83%A8%E7%BD%B2(ABU%E9%87%8F%E5%8C%96%E4%BD%BF%E7%94%A8%E6%96%87%E6%A1%A3).ipynb)

### 测试

```python
import abupy
```

## 使用文档

### 1：择时策略的开发

[第一节界面操作教程视频播放地址](https://v.qq.com/x/page/g0555b9k6ge.html)

择时策略决定什么时候买入投资品，回测告诉我们这种策略在历史数据中的模拟收益如何。

1. 买入择时因子的编写
2. 分解模式一步一步对策略进行回测
3. 卖出择时因子的实现

>在对的时间，遇见对的人(股票)，是一种幸福 
>
>在对的时间，遇见错的人(股票)，是一种悲伤 
>
>在错的时间，遇见对的人(股票)，是一声叹息 
>
>在错的时间，遇见错的人(股票)，是一种无奈 

[详细阅读](https://github.com/bbfamily/abu/blob/master/abupy_lecture/1-%E6%8B%A9%E6%97%B6%E7%AD%96%E7%95%A5%E7%9A%84%E5%BC%80%E5%8F%91(ABU%E9%87%8F%E5%8C%96%E4%BD%BF%E7%94%A8%E6%96%87%E6%A1%A3).ipynb)

### 2: 择时策略的优化

通过止盈止损保护策略产生的利润，控制风险。

1. 基本止盈止损策略
2. 风险控制止损策略
3. 利润保护止盈策略

![](./img/img1.png)

[详细阅读](https://github.com/bbfamily/abu/blob/master/abupy_lecture/2-%E6%8B%A9%E6%97%B6%E7%AD%96%E7%95%A5%E7%9A%84%E4%BC%98%E5%8C%96(ABU%E9%87%8F%E5%8C%96%E4%BD%BF%E7%94%A8%E6%96%87%E6%A1%A3).ipynb)

### 3: 滑点策略与交易手续费

考虑应用交易策略时产生的成交价格偏差及手续费。

1. 滑点买入卖出价格确定及策略实现
2. 交易手续费的计算以及自定义手续费

| type | date | symbol | commission |
| ------| ------ | ------ | ------ |
| buy | 20150423 | usTSLA | 8.22 |
| buy | 20150428 | usTSLA | 7.53 |
| sell | 20150622 | usTSLA | 8.22 |
| buy | 20150624 | usTSLA | 7.53 |
| sell | 20150706 | usTSLA | 7.53 |
| sell | 20150708 | usTSLA | 7.53 |
| buy | 20151230 | usTSLA | 7.22 |
| sell | 20160105 | usTSLA | 7.22 |
| buy | 20160315 | usTSLA | 5.57 |
| sell | 20160429 | usTSLA | 5.57 |

[详细阅读](https://github.com/bbfamily/abu/blob/master/abupy_lecture/3-%E6%BB%91%E7%82%B9%E7%AD%96%E7%95%A5%E4%B8%8E%E4%BA%A4%E6%98%93%E6%89%8B%E7%BB%AD%E8%B4%B9(ABU%E9%87%8F%E5%8C%96%E4%BD%BF%E7%94%A8%E6%96%87%E6%A1%A3).ipynb)

### 4: 多支股票择时回测与仓位管理

针对多支股票实现择时策略，通过仓位管理策略控制风险。

1. 多支股票使用相同的因子进行择时
2. 自定义仓位管理策略的实现
3. 多支股票使用不同的因子进行择时 
4. 使用并行来提升择时运行效率

![](./img/img3.png)

[详细阅读](https://github.com/bbfamily/abu/blob/master/abupy_lecture/4-%E5%A4%9A%E6%94%AF%E8%82%A1%E7%A5%A8%E6%8B%A9%E6%97%B6%E5%9B%9E%E6%B5%8B%E4%B8%8E%E4%BB%93%E4%BD%8D%E7%AE%A1%E7%90%86(ABU%E9%87%8F%E5%8C%96%E4%BD%BF%E7%94%A8%E6%96%87%E6%A1%A3).ipynb)

### 5: 选股策略的开发

一个好的策略需要一个好的标的。

1. 选股因子的编写
2. 多个选股因子并行执行
3. 使用并行来提升选股运行效率

[详细阅读](https://github.com/bbfamily/abu/blob/master/abupy_lecture/5-%E9%80%89%E8%82%A1%E7%AD%96%E7%95%A5%E7%9A%84%E5%BC%80%E5%8F%91(ABU%E9%87%8F%E5%8C%96%E4%BD%BF%E7%94%A8%E6%96%87%E6%A1%A3).ipynb)

### 6: 回测结果的度量

正确的度量引领着正确的前进方向。

1. 度量的基本使用方法
2. 度量的可视化
3. 扩展自定义度量类

[详细阅读](https://github.com/bbfamily/abu/blob/master/abupy_lecture/6-%E5%9B%9E%E6%B5%8B%E7%BB%93%E6%9E%9C%E7%9A%84%E5%BA%A6%E9%87%8F(ABU%E9%87%8F%E5%8C%96%E4%BD%BF%E7%94%A8%E6%96%87%E6%A1%A3).ipynb)

### 7: 寻找策略最优参数和评分

通过定制的评分机制，寻找一个策略最合理的参数，比如：应该考虑多少天的均线？

1. 参数取值范围
2. Grid Search寻找最优参数
3. 度量结果的评分
4. 不同权重的评分
5. 自定义评分类的实现

[详细阅读](https://github.com/bbfamily/abu/blob/master/abupy_lecture/7-%E5%AF%BB%E6%89%BE%E7%AD%96%E7%95%A5%E6%9C%80%E4%BC%98%E5%8F%82%E6%95%B0%E5%92%8C%E8%AF%84%E5%88%86(ABU%E9%87%8F%E5%8C%96%E4%BD%BF%E7%94%A8%E6%96%87%E6%A1%A3).ipynb)

### 8: A股市场的回测

1. A股市场的回测示例
2. 涨跌停的特殊处理
3. 对多组交易结果进行分析

[详细阅读](https://github.com/bbfamily/abu/blob/master/abupy_lecture/8-A%E8%82%A1%E5%B8%82%E5%9C%BA%E7%9A%84%E5%9B%9E%E6%B5%8B(ABU%E9%87%8F%E5%8C%96%E4%BD%BF%E7%94%A8%E6%96%87%E6%A1%A3).ipynb)

### 9: 港股市场的回测

1. 港股市场的回测示例
2. 优化策略，提高系统的稳定性
3. 将优化策略的'策略'做为类装饰器进行封装

[详细阅读](https://github.com/bbfamily/abu/blob/master/abupy_lecture/9-%E6%B8%AF%E8%82%A1%E5%B8%82%E5%9C%BA%E7%9A%84%E5%9B%9E%E6%B5%8B(ABU%E9%87%8F%E5%8C%96%E4%BD%BF%E7%94%A8%E6%96%87%E6%A1%A3).ipynb)

### 10: 比特币, 莱特币的回测

1. 比特币, 莱特币的走势数据分析
2. 比特币, 莱特币的走势可视化分析
3. 比特币，莱特币市场的回测

[详细阅读](https://github.com/bbfamily/abu/blob/master/abupy_lecture/10-%E6%AF%94%E7%89%B9%E5%B8%81%E8%8E%B1%E7%89%B9%E5%B8%81%E7%9A%84%E5%9B%9E%E6%B5%8B(ABU%E9%87%8F%E5%8C%96%E4%BD%BF%E7%94%A8%E6%96%87%E6%A1%A3).ipynb)

### 11: 期货市场的回测

1. 期货市场的特点
2. 看涨合约的回测
3. 看跌合约的回测
4. 位移路程比优化策略

[详细阅读](https://github.com/bbfamily/abu/blob/master/abupy_lecture/11-%E6%9C%9F%E8%B4%A7%E5%B8%82%E5%9C%BA%E7%9A%84%E5%9B%9E%E6%B5%8B(ABU%E9%87%8F%E5%8C%96%E4%BD%BF%E7%94%A8%E6%96%87%E6%A1%A3).ipynb)

### 12: 机器学习与比特币示例

如何在投资品的量化交易中正确使用机器学习技术？

1. 比特币特征的提取
2. abu中内置机器学习模块的使用
3. 测试集的验证与非均衡技术
4. 继承AbuMLPd对数据处理进行封装

[详细阅读](https://github.com/bbfamily/abu/blob/master/abupy_lecture/12-%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E4%B8%8E%E6%AF%94%E7%89%B9%E5%B8%81%E7%A4%BA%E4%BE%8B(ABU%E9%87%8F%E5%8C%96%E4%BD%BF%E7%94%A8%E6%96%87%E6%A1%A3).ipynb)

### 13: 量化技术分析应用

技术分析三大假设：市场行为涵盖一切；价格沿趋势移动；历史会重演。

1. 阻力线，支撑线自动绘制
2. 跳空技术分析
3. 传统技术指标技术分析

[详细阅读](https://github.com/bbfamily/abu/blob/master/abupy_lecture/13-%E9%87%8F%E5%8C%96%E6%8A%80%E6%9C%AF%E5%88%86%E6%9E%90%E5%BA%94%E7%94%A8(ABU%E9%87%8F%E5%8C%96%E4%BD%BF%E7%94%A8%E6%96%87%E6%A1%A3).ipynb)

### 14: 量化相关性分析应用

相似的投资品数据的背后，往往是相似行为模式的投资人群。

1. 相关相似度的度量
2. 距离的度量与相似度
3. 相似相关接口的应用
4. 自然相关性

[详细阅读](https://github.com/bbfamily/abu/blob/master/abupy_lecture/14-%E9%87%8F%E5%8C%96%E7%9B%B8%E5%85%B3%E6%80%A7%E5%88%86%E6%9E%90%E5%BA%94%E7%94%A8(ABU%E9%87%8F%E5%8C%96%E4%BD%BF%E7%94%A8%E6%96%87%E6%A1%A3).ipynb)

### 15: 量化交易和搜索引擎

搜索策略生成的失败交易，由裁判拦截住冲动的交易者。

1. 切分训练集交易的回测
2. 对交易进行人工分析
3. 主裁系统原理
4. 角度主裁
5. 赋予宏观上合理的解释
6. 最优分类簇筛选

![](./img/img13.png)

[详细阅读](https://github.com/bbfamily/abu/blob/master/abupy_lecture/15-%E9%87%8F%E5%8C%96%E4%BA%A4%E6%98%93%E5%92%8C%E6%90%9C%E7%B4%A2%E5%BC%95%E6%93%8E(ABU%E9%87%8F%E5%8C%96%E4%BD%BF%E7%94%A8%E6%96%87%E6%A1%A3).ipynb)

### 19: 数据源

abu支持股票、期货、数字货币等多种金融投资品的行情和交易，并具有高度可定制性。

1. 数据模式的切换
2. 数据存储的切换
3. 数据源的切换
4. 全市场数据的更新
5. 接入外部数据源，股票数据源
6. 接入外部数据源，期货数据源
7. 接入外部数据源，比特币，莱特币数据源

[详细阅读](https://github.com/bbfamily/abu/blob/master/abupy_lecture/19-%E6%95%B0%E6%8D%AE%E6%BA%90(ABU%E9%87%8F%E5%8C%96%E4%BD%BF%E7%94%A8%E6%96%87%E6%A1%A3).ipynb)

关注阿布量化微信公众号: abu_quant

![](./img/qrcode.jpg)

### License
[GPL](./LICENSE)
