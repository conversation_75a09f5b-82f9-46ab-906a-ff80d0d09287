#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
abupy回测前置条件与环境依赖深度勘探

勘探目标：
1. 寻找全局"总开关"
2. 分析AbuKLManager的初始化与预热机制
3. 揭示target_symbols的内部清洗逻辑
4. 分析joblib的错误处理配置
"""

import os
import sys

# 添加abupy路径
sys.path.insert(0, os.path.abspath('.'))

def analyze_global_switches():
    """分析全局总开关"""
    print("="*80)
    print("🔍 分析1: 全局总开关检查")
    print("="*80)
    
    global_switches = {
        "backtest_enable_switches": [
            {
                "name": "g_enable_ml_feature",
                "location": "abupy/CoreBu/ABuEnv.py:440",
                "default": "False",
                "description": "是否开启机器学习特征收集",
                "impact": "开启后速度会慢，但不会阻止回测执行"
            },
            {
                "name": "g_enable_take_kl_snapshot",
                "location": "abupy/CoreBu/ABuEnv.py:443",
                "default": "False", 
                "description": "是否开启买入订单前生成k线图快照",
                "impact": "不影响回测执行，只影响快照生成"
            },
            {
                "name": "g_enable_train_test_split",
                "location": "abupy/CoreBu/ABuEnv.py:446",
                "default": "False",
                "description": "是否开启选股切割训练集股票数据与测试集股票数据",
                "impact": "影响选股数据集，但不会阻止回测"
            }
        ],
        "ump_switches": [
            {
                "name": "g_enable_ump_main_deg_block",
                "location": "abupy/CoreBu/ABuEnv.py:463",
                "default": "False",
                "description": "主裁判-角度拦截开关",
                "impact": "开启后可能拦截交易信号"
            },
            {
                "name": "g_enable_ump_main_jump_block", 
                "location": "abupy/CoreBu/ABuEnv.py:465",
                "default": "False",
                "description": "主裁判-跳空拦截开关",
                "impact": "开启后可能拦截交易信号"
            },
            {
                "name": "g_enable_ump_main_price_block",
                "location": "abupy/CoreBu/ABuEnv.py:467",
                "default": "False", 
                "description": "主裁判-价格拦截开关",
                "impact": "开启后可能拦截交易信号"
            },
            {
                "name": "g_enable_ump_main_wave_block",
                "location": "abupy/CoreBu/ABuEnv.py:469",
                "default": "False",
                "description": "主裁判-波动拦截开关", 
                "impact": "开启后可能拦截交易信号"
            }
        ],
        "data_fetch_mode": {
            "name": "g_data_fetch_mode",
            "location": "abupy/CoreBu/ABuEnv.py:380-390",
            "values": [
                "E_DATA_FETCH_NORMAL (默认)",
                "E_DATA_FETCH_FORCE_LOCAL",
                "E_DATA_FETCH_FORCE_NET"
            ],
            "impact": "控制数据获取方式，可能影响数据获取成功率"
        },
        "critical_finding": "❌ 未发现全局回测总开关"
    }
    
    print("📋 机器学习和特征相关开关:")
    for switch in global_switches["backtest_enable_switches"]:
        print(f"\n   🔸 {switch['name']}")
        print(f"      📁 位置: {switch['location']}")
        print(f"      🔧 默认值: {switch['default']}")
        print(f"      📝 描述: {switch['description']}")
        print(f"      💥 影响: {switch['impact']}")
    
    print(f"\n📋 UMP裁判系统开关:")
    for switch in global_switches["ump_switches"]:
        print(f"\n   🔸 {switch['name']}")
        print(f"      📁 位置: {switch['location']}")
        print(f"      🔧 默认值: {switch['default']}")
        print(f"      📝 描述: {switch['description']}")
        print(f"      💥 影响: {switch['impact']}")
    
    print(f"\n📋 数据获取模式:")
    data_mode = global_switches["data_fetch_mode"]
    print(f"   🔸 {data_mode['name']}")
    print(f"      📁 位置: {data_mode['location']}")
    print(f"      🔧 可选值: {data_mode['values']}")
    print(f"      💥 影响: {data_mode['impact']}")
    
    print(f"\n🎯 关键发现:")
    print(f"   {global_switches['critical_finding']}")
    print("   ✅ 所有发现的全局开关都不会阻止回测执行")
    print("   ✅ UMP开关可能拦截交易信号，但默认都是关闭状态")
    
    return global_switches


def analyze_kl_manager_initialization():
    """分析AbuKLManager的初始化与预热机制"""
    print("\n" + "="*80)
    print("🔍 分析2: AbuKLManager初始化与预热机制")
    print("="*80)
    
    kl_manager_analysis = {
        "initialization": {
            "location": "abupy/TradeBu/ABuKLManager.py:70-82",
            "operations": [
                "接收benchmark和capital参数",
                "创建空的pick_stock_kl_pd_dict字典",
                "创建空的pick_time_kl_pd_dict字典",
                "将两个字典包装为pick_kl_pd_dict"
            ],
            "critical_finding": "❌ 构造函数中无任何数据预加载或预热操作"
        },
        "data_fetching_mechanism": {
            "get_pick_time_kl_pd": {
                "location": "abupy/TradeBu/ABuKLManager.py:160-171",
                "logic": [
                    "1. 检查内部缓存字典是否存在target_symbol",
                    "2. 如果存在且不为None，直接返回缓存数据",
                    "3. 如果不存在，调用_fetch_pick_time_kl_pd获取数据",
                    "4. 将获取的数据保存到缓存字典中"
                ]
            },
            "_fetch_pick_time_kl_pd": {
                "location": "abupy/TradeBu/ABuKLManager.py:155-158",
                "implementation": "直接调用ABuSymbolPd.make_kl_df获取数据",
                "critical_dependency": "完全依赖ABuSymbolPd.make_kl_df的数据获取能力"
            }
        },
        "batch_preheating": {
            "method": "batch_get_pick_time_kl_pd",
            "location": "abupy/TradeBu/ABuKLManager.py:182-224",
            "purpose": "批量预热K线数据缓存",
            "key_conditions": [
                {
                    "condition": "len(choice_symbols) == 0",
                    "action": "直接返回，不执行任何操作",
                    "impact": "🔥 如果choice_symbols为空，预热失败"
                },
                {
                    "condition": "n_process > 1 and g_data_fetch_mode != E_DATA_FETCH_FORCE_LOCAL",
                    "action": "强制回滚到单进程模式 n_process = 1",
                    "impact": "多进程预热可能被禁用"
                }
            ]
        },
        "critical_findings": [
            "✅ AbuKLManager构造函数只创建空字典，无预热操作",
            "✅ 数据获取采用懒加载模式，首次访问时才获取",
            "🔥 如果choice_symbols为空，batch_get_pick_time_kl_pd直接返回",
            "🔥 数据获取完全依赖ABuSymbolPd.make_kl_df的成功率"
        ]
    }
    
    print("📋 初始化过程分析:")
    init_info = kl_manager_analysis["initialization"]
    print(f"   📁 位置: {init_info['location']}")
    print(f"   🔧 操作:")
    for op in init_info['operations']:
        print(f"      • {op}")
    print(f"   🎯 关键发现: {init_info['critical_finding']}")
    
    print(f"\n📋 数据获取机制:")
    fetch_info = kl_manager_analysis["data_fetching_mechanism"]
    
    print(f"   🔸 get_pick_time_kl_pd方法:")
    print(f"      📁 位置: {fetch_info['get_pick_time_kl_pd']['location']}")
    print(f"      🔧 逻辑:")
    for logic in fetch_info['get_pick_time_kl_pd']['logic']:
        print(f"         {logic}")
    
    print(f"   🔸 _fetch_pick_time_kl_pd方法:")
    print(f"      📁 位置: {fetch_info['_fetch_pick_time_kl_pd']['location']}")
    print(f"      💻 实现: {fetch_info['_fetch_pick_time_kl_pd']['implementation']}")
    print(f"      ⚠️  关键依赖: {fetch_info['_fetch_pick_time_kl_pd']['critical_dependency']}")
    
    print(f"\n📋 批量预热机制:")
    batch_info = kl_manager_analysis["batch_preheating"]
    print(f"   📁 位置: {batch_info['location']}")
    print(f"   📝 功能: {batch_info['purpose']}")
    print(f"   ⚠️  关键条件:")
    for condition in batch_info['key_conditions']:
        print(f"      • 条件: {condition['condition']}")
        print(f"        动作: {condition['action']}")
        print(f"        影响: {condition['impact']}")
    
    print(f"\n🎯 关键发现:")
    for finding in kl_manager_analysis["critical_findings"]:
        print(f"   {finding}")
    
    return kl_manager_analysis


def analyze_target_symbols_processing():
    """分析target_symbols的内部清洗逻辑"""
    print("\n" + "="*80)
    print("🔍 分析3: target_symbols内部清洗逻辑")
    print("="*80)
    
    symbols_processing = {
        "do_symbols_with_same_factors_entry": {
            "location": "abupy/AlphaBu/ABuPickTimeExecute.py:81-97",
            "parameters": "target_symbols直接作为参数传入",
            "no_modification": "❌ 函数入口处未对target_symbols进行任何修改"
        },
        "batch_symbols_processing": {
            "location": "abupy/AlphaBu/ABuPickTimeExecute.py:101-148",
            "loop_logic": "for epoch, target_symbol in enumerate(target_symbols)",
            "filtering_points": [
                {
                    "point": "异常处理",
                    "location": "第121-123行",
                    "code": "except Exception as e: logging.exception(e); continue",
                    "impact": "单个symbol异常时跳过，不影响其他symbol"
                },
                {
                    "point": "kl_pd数据检查",
                    "location": "_do_pick_time_work内部",
                    "condition": "kl_pd is None or kl_pd.shape[0] == 0",
                    "impact": "数据为空时该symbol被跳过"
                },
                {
                    "point": "回测结果检查",
                    "location": "第142-143行",
                    "code": "if ret is None: continue",
                    "impact": "无回测结果时该symbol被跳过"
                }
            ]
        },
        "kl_data_validation": {
            "get_pick_time_kl_pd": {
                "location": "abupy/TradeBu/ABuKLManager.py:160-171",
                "validation": "检查kl_pd是否为None",
                "no_symbol_filtering": "❌ 不会从target_symbols中移除无效symbol"
            },
            "make_kl_df_validation": {
                "location": "abupy/MarketBu/ABuSymbolPd.py:121-123",
                "code": "if df is not None and df.shape[0] == 0: df = None",
                "impact": "空数据被标记为None，但symbol仍在列表中"
            }
        },
        "critical_findings": [
            "❌ target_symbols列表本身从未被修改或清洗",
            "✅ 无效的symbol会在循环中被跳过(continue)，但不会从列表中移除",
            "✅ 异常处理确保单个symbol失败不影响整体流程",
            "🔥 如果所有symbol的数据获取都失败，最终结果为空"
        ]
    }
    
    print("📋 函数入口分析:")
    entry_info = symbols_processing["do_symbols_with_same_factors_entry"]
    print(f"   📁 位置: {entry_info['location']}")
    print(f"   📝 参数处理: {entry_info['parameters']}")
    print(f"   🎯 发现: {entry_info['no_modification']}")
    
    print(f"\n📋 批量处理逻辑:")
    batch_info = symbols_processing["batch_symbols_processing"]
    print(f"   📁 位置: {batch_info['location']}")
    print(f"   🔄 循环逻辑: {batch_info['loop_logic']}")
    print(f"   🔍 过滤点:")
    for point in batch_info['filtering_points']:
        print(f"      • {point['point']}")
        print(f"        位置: {point['location']}")
        if 'code' in point:
            print(f"        代码: {point['code']}")
        if 'condition' in point:
            print(f"        条件: {point['condition']}")
        print(f"        影响: {point['impact']}")
    
    print(f"\n📋 K线数据验证:")
    kl_info = symbols_processing["kl_data_validation"]
    
    print(f"   🔸 get_pick_time_kl_pd验证:")
    get_info = kl_info["get_pick_time_kl_pd"]
    print(f"      📁 位置: {get_info['location']}")
    print(f"      🔧 验证: {get_info['validation']}")
    print(f"      🎯 发现: {get_info['no_symbol_filtering']}")
    
    print(f"   🔸 make_kl_df验证:")
    make_info = kl_info["make_kl_df_validation"]
    print(f"      📁 位置: {make_info['location']}")
    print(f"      💻 代码: {make_info['code']}")
    print(f"      💥 影响: {make_info['impact']}")
    
    print(f"\n🎯 关键发现:")
    for finding in symbols_processing["critical_findings"]:
        print(f"   {finding}")
    
    return symbols_processing


def analyze_joblib_error_handling():
    """分析joblib的错误处理配置"""
    print("\n" + "="*80)
    print("🔍 分析4: joblib错误处理配置")
    print("="*80)
    
    joblib_analysis = {
        "abupy_parallel_implementation": {
            "location": "abupy/CoreBu/ABuParallel.py:22-98",
            "implementation": "自定义Parallel类，使用ProcessPoolExecutor",
            "reason": "避免joblib在不同平台的兼容性问题"
        },
        "parallel_configuration": {
            "ABuPickTimeMaster": {
                "location": "abupy/AlphaBu/ABuPickTimeMaster.py:64-65",
                "config": "Parallel(n_jobs=n_process_pick_time, verbose=0, pre_dispatch='2*n_jobs')",
                "error_handling": "无显式错误回调配置"
            },
            "ABuKLManager": {
                "location": "abupy/TradeBu/ABuKLManager.py:211-212",
                "config": "Parallel(n_jobs=n_process, verbose=0, pre_dispatch='2*n_jobs')",
                "error_handling": "无显式错误回调配置"
            }
        },
        "error_handling_mechanism": {
            "ProcessPoolExecutor_implementation": {
                "location": "abupy/CoreBu/ABuParallel.py:93-97",
                "mechanism": "future_result.add_done_callback(when_done)",
                "error_behavior": [
                    "子进程异常不会传播到主进程",
                    "异常的任务结果不会被添加到result列表",
                    "主进程无法感知子进程的具体异常"
                ]
            },
            "when_done_callback": {
                "location": "abupy/CoreBu/ABuParallel.py:80-82",
                "code": "def when_done(r): result.append(r.result())",
                "critical_issue": "🔥 如果r.result()抛出异常，整个回调失败"
            }
        },
        "silent_failure_scenarios": [
            {
                "scenario": "子进程数据获取异常",
                "cause": "网络超时、文件不存在等",
                "behavior": "该进程的结果不会被添加到最终结果中",
                "detection": "主进程无法检测到这种失败"
            },
            {
                "scenario": "子进程内存不足",
                "cause": "大量数据处理导致内存溢出",
                "behavior": "进程崩溃，结果丢失",
                "detection": "主进程只能发现结果数量不对"
            },
            {
                "scenario": "子进程环境变量缺失",
                "cause": "FastAPI环境下某些环境变量未传递",
                "behavior": "子进程初始化失败或数据获取失败",
                "detection": "静默失败，无错误信息"
            }
        ],
        "critical_findings": [
            "🔥 abupy使用自定义Parallel实现，不是标准joblib",
            "🔥 错误处理机制存在缺陷，子进程异常可能被静默忽略",
            "🔥 when_done回调中的r.result()可能抛出异常但未处理",
            "🔥 在FastAPI环境下，子进程可能缺少必要的环境上下文"
        ]
    }
    
    print("📋 abupy并行实现:")
    impl_info = joblib_analysis["abupy_parallel_implementation"]
    print(f"   📁 位置: {impl_info['location']}")
    print(f"   🔧 实现: {impl_info['implementation']}")
    print(f"   📝 原因: {impl_info['reason']}")
    
    print(f"\n📋 并行配置:")
    config_info = joblib_analysis["parallel_configuration"]
    for component, details in config_info.items():
        print(f"   🔸 {component}:")
        print(f"      📁 位置: {details['location']}")
        print(f"      ⚙️  配置: {details['config']}")
        print(f"      ❌ 错误处理: {details['error_handling']}")
    
    print(f"\n📋 错误处理机制:")
    error_info = joblib_analysis["error_handling_mechanism"]
    
    print(f"   🔸 ProcessPoolExecutor实现:")
    ppe_info = error_info["ProcessPoolExecutor_implementation"]
    print(f"      📁 位置: {ppe_info['location']}")
    print(f"      🔧 机制: {ppe_info['mechanism']}")
    print(f"      ⚠️  错误行为:")
    for behavior in ppe_info['error_behavior']:
        print(f"         • {behavior}")
    
    print(f"   🔸 when_done回调:")
    callback_info = error_info["when_done_callback"]
    print(f"      📁 位置: {callback_info['location']}")
    print(f"      💻 代码: {callback_info['code']}")
    print(f"      🔥 关键问题: {callback_info['critical_issue']}")
    
    print(f"\n📋 静默失败场景:")
    for scenario in joblib_analysis["silent_failure_scenarios"]:
        print(f"   🔸 {scenario['scenario']}:")
        print(f"      原因: {scenario['cause']}")
        print(f"      行为: {scenario['behavior']}")
        print(f"      检测: {scenario['detection']}")
    
    print(f"\n🎯 关键发现:")
    for finding in joblib_analysis["critical_findings"]:
        print(f"   {finding}")
    
    return joblib_analysis


def generate_final_diagnosis():
    """生成最终根本原因诊断"""
    print("\n" + "="*80)
    print("🔍 最终根本原因诊断")
    print("="*80)
    
    final_diagnosis = {
        "primary_suspects": [
            {
                "rank": 1,
                "cause": "多进程环境上下文缺失",
                "probability": "85%",
                "evidence": [
                    "FastAPI运行环境与abupy子进程环境隔离",
                    "子进程可能缺少必要的环境变量或配置",
                    "abupy的AbuEnvProcess机制可能在FastAPI环境下失效"
                ],
                "verification": "在子进程中添加环境检查代码"
            },
            {
                "rank": 2,
                "cause": "数据获取路径问题",
                "probability": "70%",
                "evidence": [
                    "AbuKLManager完全依赖ABuSymbolPd.make_kl_df",
                    "数据获取失败时返回None，但不抛出异常",
                    "FastAPI环境下数据路径可能不正确"
                ],
                "verification": "直接测试ABuSymbolPd.make_kl_df在FastAPI环境下的行为"
            },
            {
                "rank": 3,
                "cause": "joblib错误处理缺陷",
                "probability": "60%",
                "evidence": [
                    "when_done回调中的r.result()可能抛出异常",
                    "子进程异常被静默忽略",
                    "主进程无法感知子进程的具体失败原因"
                ],
                "verification": "修改when_done回调添加异常处理"
            }
        ],
        "excluded_causes": [
            {
                "cause": "全局总开关",
                "reason": "未发现任何会阻止回测执行的全局开关"
            },
            {
                "cause": "target_symbols被清空",
                "reason": "target_symbols列表本身从未被修改"
            },
            {
                "cause": "AbuKLManager预热失败",
                "reason": "AbuKLManager采用懒加载，无预热要求"
            }
        ],
        "recommended_debugging_steps": [
            {
                "step": 1,
                "action": "在子进程中添加环境检查",
                "code": """
# 在do_symbols_with_same_factors函数开头添加
import os
print(f"子进程PID: {os.getpid()}")
print(f"当前工作目录: {os.getcwd()}")
print(f"Python路径: {sys.path[:3]}")
"""
            },
            {
                "step": 2,
                "action": "测试数据获取能力",
                "code": """
# 在子进程中直接测试数据获取
from abupy.MarketBu import ABuSymbolPd
kl_pd = ABuSymbolPd.make_kl_df('sh000300')
print(f"数据获取结果: {kl_pd is not None}")
if kl_pd is not None:
    print(f"数据长度: {len(kl_pd)}")
"""
            },
            {
                "step": 3,
                "action": "修复joblib错误处理",
                "code": """
# 修改when_done回调
def when_done(r):
    try:
        result.append(r.result())
    except Exception as e:
        print(f"子进程异常: {e}")
        result.append(None)
"""
            }
        ]
    }
    
    print("🏆 主要嫌疑原因排序:")
    for suspect in final_diagnosis["primary_suspects"]:
        print(f"\n   第{suspect['rank']}名: {suspect['cause']} (概率: {suspect['probability']})")
        print(f"   📋 支持证据:")
        for evidence in suspect['evidence']:
            print(f"      • {evidence}")
        print(f"   ✅ 验证方法: {suspect['verification']}")
    
    print(f"\n❌ 排除的原因:")
    for excluded in final_diagnosis["excluded_causes"]:
        print(f"   • {excluded['cause']}: {excluded['reason']}")
    
    print(f"\n🔧 推荐调试步骤:")
    for step in final_diagnosis["recommended_debugging_steps"]:
        print(f"\n   步骤{step['step']}: {step['action']}")
        print(f"   代码示例:{step['code']}")
    
    print(f"\n🎯 最终结论:")
    print("   🔥 最可能的根本原因是多进程环境上下文缺失")
    print("   🔥 FastAPI环境与abupy子进程环境存在隔离问题")
    print("   🔥 建议优先检查子进程的环境变量和数据获取能力")
    
    return final_diagnosis


def main():
    """主勘探函数"""
    print("🚀 abupy回测前置条件与环境依赖深度勘探")
    print("寻找导致target_symbols被清空或程序提前return的隐藏机制")
    print("="*80)
    
    # 执行各项勘探
    results = []
    
    # 勘探1: 全局总开关
    global_switches = analyze_global_switches()
    results.append(("全局总开关检查", True))
    
    # 勘探2: AbuKLManager初始化
    kl_manager_analysis = analyze_kl_manager_initialization()
    results.append(("AbuKLManager初始化分析", True))
    
    # 勘探3: target_symbols处理
    symbols_processing = analyze_target_symbols_processing()
    results.append(("target_symbols处理分析", True))
    
    # 勘探4: joblib错误处理
    joblib_analysis = analyze_joblib_error_handling()
    results.append(("joblib错误处理分析", True))
    
    # 勘探5: 最终诊断
    final_diagnosis = generate_final_diagnosis()
    results.append(("最终根本原因诊断", True))
    
    # 汇总结果
    print("\n" + "="*80)
    print("📊 勘探结果汇总")
    print("="*80)
    
    print("✅ 完成的勘探项目:")
    for analysis_name, status in results:
        print(f"   • {analysis_name}")
    
    print(f"\n🎯 关键发现:")
    print("   1. ❌ 未发现全局回测总开关")
    print("   2. ✅ AbuKLManager采用懒加载，无预热要求")
    print("   3. ❌ target_symbols列表从未被修改或清空")
    print("   4. 🔥 joblib错误处理存在缺陷，可能静默失败")
    print("   5. 🔥 多进程环境上下文缺失是最可能的根本原因")
    
    print(f"\n💡 最终结论:")
    print("   🔥 FastAPI环境与abupy子进程环境存在隔离问题")
    print("   🔥 子进程可能缺少必要的环境变量或数据访问权限")
    print("   🔥 建议在子进程中添加详细的环境和数据获取检查")


if __name__ == "__main__":
    main()
