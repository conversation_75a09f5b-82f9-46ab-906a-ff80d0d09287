{"cells": [{"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["enable example env will only read RomDataBu/df_kl.h5\n"]}], "source": ["from __future__ import print_function\n", "# 使用本地的abu\n", "import os\n", "import sys\n", "# 使用insert 0即只使用github，避免交叉使用了pip安装的abupy，导致的版本不一致问题\n", "sys.path.insert(0, os.path.abspath('../'))\n", "\n", "import abupy\n", "from abupy import six, xrange, range, reduce, map, filter, partial\n", "# 使用沙盒数据，目的是和书中一样的数据环境\n", "abupy.env.enable_example_env_ipython()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3.6.0 |Anaconda 4.3.1 (x86_64)| (default, Dec 23 2016, 13:19:00) \n", "[GCC 4.2.1 Compatible Apple LLVM 6.0 (clang-600.0.57)]\n"]}], "source": ["print(sys.version)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 第2章 量化语言-Python\n", "\n", "[abu量化系统github地址](https://github.com/bbfamily/abu) (您的star是我的动力！)\n", "\n", "[abu量化文档教程ipython notebook](https://github.com/bbfamily/abu/tree/master/abupy_lecture)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"collapsed": true}, "outputs": [], "source": ["price_str = '30.14, 29.58, 26.36, 32.56, 32.82'"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["str"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["type(price_str)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["raise, try except\n"]}], "source": ["if not isinstance(price_str, str):\n", "    # not代表逻辑‘非’， 如果不是字符串，转换为字符串\n", "    price_str = str(price_str)\n", "if isinstance(price_str, int) and price_str > 0:\n", "    # and 代表逻辑‘与’，如果是int类型且是正数\n", "    price_str += 1\n", "elif isinstance(price_str, float) or float(price_str[:4]) < 0:\n", "    # or 代表逻辑‘或’，如果是float或者小于0\n", "    price_str += 1.0\n", "else:\n", "    try:\n", "        raise TypeError('price_str is str type!')\n", "    except TypeError:\n", "        print('raise, try except')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.1.2 字符串和容器"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["旧的price_str id= 4708834976\n", "新的price_str id= 4709008528\n"]}, {"data": {"text/plain": ["'30.14,29.58,26.36,32.56,32.82'"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["print('旧的price_str id= {}'.format(id(price_str)))\n", "price_str = price_str.replace(' ', '')\n", "print('新的price_str id= {}'.format(id(price_str)))\n", "price_str"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['30.14', '29.58', '26.36', '32.56', '32.82']\n", "['30.14', '29.58', '26.36', '32.56', '32.82', '32.82']\n"]}], "source": ["# split以逗号分割字符串，返回数组price_array\n", "price_array = price_str.split(',')\n", "print(price_array)\n", "# price_array尾部append一个重复的32.82\n", "price_array.append('32.82')\n", "print(price_array)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'26.36', '29.58', '30.14', '32.56', '32.82'}"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["set(price_array)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["['30.14', '29.58', '26.36', '32.56', '32.82']"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["price_array.remove('32.82')\n", "price_array"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["['20170118', '20170119', '20170120', '20170121', '20170122']"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["date_array = []\n", "date_base = 20170118\n", "# 这里用for只是为了计数，无用的变量python建议使用'_'声明\n", "for _ in xrange(0, len(price_array)):\n", "    # 本节只是简单示例，不考虑日期的进位\n", "    date_array.append(str(date_base))\n", "    date_base += 1\n", "date_array"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": ["['20170118', '20170119', '20170120', '20170121']"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["date_array = []\n", "date_base = 20170118\n", "price_cnt = len(price_array) - 1\n", "while price_cnt > 0:\n", "    date_array.append(str(date_base))\n", "    date_base += 1\n", "    price_cnt -= 1\n", "date_array"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["['20170118', '20170119', '20170120', '20170121', '20170122']"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["date_base = 20170118\n", "date_array = [str(date_base + ind) for ind, _ in enumerate(price_array)] \n", "date_array"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["20170119日价格：29.58\n"]}, {"data": {"text/plain": ["[('20170118', '30.14'),\n", " ('20170119', '29.58'),\n", " ('20170120', '26.36'),\n", " ('20170121', '32.56'),\n", " ('20170122', '32.82')]"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["stock_tuple_list = [(date, price) for date, price in zip(date_array, price_array)]\n", "# tuple访问使用索引\n", "print('20170119日价格：{}'.format(stock_tuple_list[1][1]))\n", "stock_tuple_list"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["20170119日价格：29.58\n"]}, {"data": {"text/plain": ["[stock(date='20170118', price='30.14'),\n", " stock(date='20170119', price='29.58'),\n", " stock(date='20170120', price='26.36'),\n", " stock(date='20170121', price='32.56'),\n", " stock(date='20170122', price='32.82')]"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["from collections import namedtuple\n", "\n", "stock_namedtuple = namedtuple('stock', ('date', 'price'))\n", "stock_namedtuple_list = [stock_namedtuple(date, price) for date, price in zip(date_array, price_array)]\n", "# namedtuple访问使用price\n", "print('20170119日价格：{}'.format(stock_namedtuple_list[1].price))\n", "stock_namedtuple_list"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["20170119日价格：29.58\n"]}, {"data": {"text/plain": ["{'20170118': '30.14',\n", " '20170119': '29.58',\n", " '20170120': '26.36',\n", " '20170121': '32.56',\n", " '20170122': '32.82'}"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["# 字典推导式：{key: value for in}\n", "stock_dict = {date:price for date, price in zip(date_array, price_array)}\n", "print('20170119日价格：{}'.format(stock_dict['20170119']))\n", "stock_dict"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/plain": ["(dict_keys(['20170118', '20170119', '20170120', '20170121', '20170122']),\n", " dict_values(['30.14', '29.58', '26.36', '32.56', '32.82']))"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["stock_dict.keys(), stock_dict.values()"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/plain": ["odict_keys(['20170118', '20170119', '20170120', '20170121', '20170122'])"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["from collections import OrderedDict\n", "stock_dict = OrderedDict((date, price) for date, price in zip(date_array, price_array))\n", "stock_dict.keys()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["如上所示使用OrderedDict构造的dict返回的keys是按照插入顺序返回的"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2.2 函数"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.2.1 函数的使用和定义"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"text/plain": ["'20170118'"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["min(stock_dict)"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/plain": ["('26.36', '20170120')"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["min(zip(stock_dict.values(), stock_dict.keys()))"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["('32.56', '20170121')\n"]}], "source": ["def find_second_max(dict_array):\n", "    # 对传入的dict sorted排序\n", "    stock_prices_sorted = sorted(zip(dict_array.values(), dict_array.keys()))\n", "    # 第二大的也就是倒数第二个\n", "    return stock_prices_sorted[-2]\n", "\n", "# 系统函数callable验证是否为一个可call的函数\n", "if callable(find_second_max):\n", "    print(find_second_max(stock_dict))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.2.2 lambda函数"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"text/plain": ["('32.56', '20170121')"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["find_second_max_lambda = lambda dict_array: sorted(zip(dict_array.values(), dict_array.keys()))[-2]\n", "find_second_max_lambda(stock_dict)"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"text/plain": ["(('26.36', '20170120'), ('32.82', '20170122'))"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["def find_max_and_min(dict_array):\n", "    # 对传入的dict sorted排序\n", "    stock_prices_sorted = sorted(zip(dict_array.values(), dict_array.keys()))\n", "    return stock_prices_sorted[0], stock_prices_sorted[-1]\n", "\n", "find_max_and_min(stock_dict)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.2.3 高阶函数"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"data": {"text/plain": ["[(30.14, 29.58), (29.58, 26.36), (26.36, 32.56), (32.56, 32.82)]"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["# 将字符串的的价格通过列表推导式显示转换为float类型\n", "# 由于stock_dict是OrderedDict所以才可以直接\n", "# 使用stock_dict.values()获取有序日期的收盘价格\n", "price_float_array = [float(price_str) for price_str in stock_dict.values()]\n", "# 通过将时间平移形成两个错开的收盘价序列，通过zip打包成为一个新的序列，\n", "# 通过[:-1]:从第0个到倒数第二个，[1:]：从第一个到最后一个 错开形成相邻\n", "# 组成的序列每个元素为相邻的两个收盘价格\n", "pp_array = [(price1, price2) for price1, price2 in zip(price_float_array[:-1], price_float_array[1:])]\n", "pp_array"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"text/plain": ["[0, -0.019, -0.109, 0.235, 0.008]"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["# list(map) 配合 six.moves.map for python3\n", "change_array = list(map(lambda pp: reduce(lambda a, b: round((b - a) / a, 3), pp), pp_array))\n", "# list insert插入数据，将第一天的涨跌幅设置为0\n", "change_array.insert(0, 0)\n", "change_array"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"text/plain": ["OrderedDict([('20170118', stock(date='20170118', price='30.14', change=0)),\n", "             ('20170119',\n", "              stock(date='20170119', price='29.58', change=-0.019)),\n", "             ('20170120',\n", "              stock(date='20170120', price='26.36', change=-0.109)),\n", "             ('20170121', stock(date='20170121', price='32.56', change=0.235)),\n", "             ('20170122',\n", "              stock(date='20170122', price='32.82', change=0.008))])"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["# 使用namedtuple重新构建数据结构\n", "stock_namedtuple = namedtuple('stock', ('date', 'price', 'change'))\n", "# 通过zip分别从date_array，price_array，change_array拿数据组成\n", "# stock_namedtuple然后以date做为key组成OrderedDict\n", "stock_dict = OrderedDict((date, stock_namedtuple(date, price, change)) for date, price, change in zip(date_array, price_array, change_array))\n", "stock_dict"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"text/plain": ["[stock(date='20170121', price='32.56', change=0.235),\n", " stock(date='20170122', price='32.82', change=0.008)]"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["# list(filter) for python3 配合 six.moves.filter for python3\n", "up_days = list(filter(lambda day: day.change > 0, stock_dict.values()))\n", "up_days"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["所有上涨的交易日：[stock(date='20170121', price='32.56', change=0.235), stock(date='20170122', price='32.82', change=0.008)]\n", "所有下跌的交易日：[stock(date='20170119', price='29.58', change=-0.019), stock(date='20170120', price='26.36', change=-0.109)]\n", "所有上涨交易日的涨幅和：0.243\n", "所有下跌交易日的跌幅和：-0.128\n"]}], "source": ["def filter_stock(stock_array_dict, want_up=True, want_calc_sum=False):\n", "    if not isinstance(stock_array_dict, OrderedDict):\n", "        raise TypeError('stock_array_dict must be OrderedDict!')\n", "    \n", "    # python中的三目表达式的写法\n", "    filter_func = (lambda day: day.change > 0) if want_up else (lambda day: day.change < 0)\n", "    # 使用filter_func做筛选函数\n", "    want_days = list(filter(filter_func, stock_array_dict.values()))\n", "    \n", "    if not want_calc_sum:\n", "        return want_days\n", "    \n", "    # 需要计算涨跌幅和\n", "    change_sum = 0.0\n", "    for day in want_days:\n", "        change_sum += day.change\n", "    return change_sum\n", "\n", "# 全部使用默认参数\n", "print('所有上涨的交易日：{}'.format(filter_stock(stock_dict)))\n", "# want_up=False\n", "print('所有下跌的交易日：{}'.format(filter_stock(stock_dict, want_up=False)))\n", "# 计算所有上涨的总会\n", "print('所有上涨交易日的涨幅和：{}'.format(filter_stock(stock_dict, want_calc_sum=True)))\n", "# 计算所有下跌的总会\n", "print('所有下跌交易日的跌幅和：{}'.format(filter_stock(stock_dict, want_up=False, want_calc_sum=True)))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.2.4 偏函数"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["所有上涨的交易日：[stock(date='20170121', price='32.56', change=0.235), stock(date='20170122', price='32.82', change=0.008)]\n", "所有下跌的交易日：[stock(date='20170119', price='29.58', change=-0.019), stock(date='20170120', price='26.36', change=-0.109)]\n", "所有上涨交易日的涨幅和：0.243\n", "所有下跌交易日的跌幅和：-0.128\n"]}], "source": ["filter_stock_up_days = partial(filter_stock, want_up=True, want_calc_sum=False)\n", "filter_stock_down_days = partial(filter_stock, want_up=False, want_calc_sum=False)\n", "filter_stock_up_sums = partial(filter_stock, want_up=True, want_calc_sum=True)\n", "filter_stock_down_sums = partial(filter_stock, want_up=False, want_calc_sum=True)\n", "\n", "\n", "print('所有上涨的交易日：{}'.format(filter_stock_up_days(stock_dict)))\n", "print('所有下跌的交易日：{}'.format(filter_stock_down_days(stock_dict)))\n", "print('所有上涨交易日的涨幅和：{}'.format(filter_stock_up_sums(stock_dict)))\n", "print('所有下跌交易日的跌幅和：{}'.format(filter_stock_down_sums(stock_dict)))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2.3 面向对象"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.3.1 类的封装"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"collapsed": true}, "outputs": [], "source": ["from collections import namedtuple\n", "from collections import OrderedDict\n", "\n", "class StockTradeDays(object):\n", "    def __init__(self, price_array, start_date, date_array=None):\n", "        # 私有价格序列\n", "        self.__price_array = price_array\n", "        # 私有日期序列\n", "        self.__date_array = self._init_days(start_date, date_array)\n", "        # 私有涨跌幅序列\n", "        self.__change_array = self.__init_change()\n", "        # 进行OrderedDict的组装\n", "        self.stock_dict = self._init_stock_dict()\n", "\n", "    def __init_change(self):\n", "        \"\"\"\n", "        从price_array生成change_array\n", "        :return:\n", "        \"\"\"\n", "        price_float_array = [float(price_str) for price_str in\n", "                             self.__price_array]\n", "        # 通过将时间平移形成两个错开的收盘价序列，通过zip打包成为一个新的序列\n", "        # 每个元素为相邻的两个收盘价格\n", "        pp_array = [(price1, price2) for price1, price2 in\n", "                    zip(price_float_array[:-1], price_float_array[1:])]\n", "        change_array = list(map(lambda pp: reduce(lambda a, b: round((b - a) / a, 3), pp), pp_array))\n", "        # list insert插入数据，将第一天的涨跌幅设置为0\n", "        change_array.insert(0, 0)\n", "        return change_array\n", "\n", "    def _init_days(self, start_date, date_array):\n", "        \"\"\"\n", "        protect方法，\n", "        :param start_date: 初始日期\n", "        :param date_array: 给定日期序列\n", "        :return:\n", "        \"\"\"\n", "        if date_array is None:\n", "            # 由start_date和self.__price_array来确定日期序列\n", "            date_array = [str(start_date + ind) for ind, _ in\n", "                          enumerate(self.__price_array)]\n", "        else:\n", "            # 稍后的内容会使用外部直接设置的方式\n", "            # 如果外面设置了date_array，就直接转换str类型组成新date_array\n", "            date_array = [str(date) for date in date_array]\n", "        return date_array\n", "\n", "    def _init_stock_dict(self):\n", "        \"\"\"\n", "        使用namedtuple，OrderedDict将结果合并\n", "        :return:\n", "        \"\"\"\n", "        stock_namedtuple = namedtuple('stock',\n", "                                      ('date', 'price', 'change'))\n", "\n", "        # 使用以被赋值的__date_array等进行OrderedDict的组装\n", "        stock_dict = OrderedDict(\n", "            (date, stock_namedtuple(date, price, change))\n", "            for date, price, change in\n", "            zip(self.__date_array, self.__price_array,\n", "                self.__change_array))\n", "        return stock_dict\n", "\n", "    def filter_stock(self, want_up=True, want_calc_sum=False):\n", "        \"\"\"\n", "        筛选结果子集\n", "        :param want_up: 是否筛选上涨\n", "        :param want_calc_sum: 是否计算涨跌和\n", "        :return:\n", "        \"\"\"\n", "        # Python中的三目表达式的写法\n", "        filter_func = (lambda p_day: p_day.change > 0) if want_up else (\n", "            lambda p_day: p_day.change < 0)\n", "        # 使用filter_func做筛选函数\n", "        want_days = list(filter(filter_func, self.stock_dict.values()))\n", "\n", "        if not want_calc_sum:\n", "            return want_days\n", "\n", "        # 需要计算涨跌幅和\n", "        change_sum = 0.0\n", "        for day in want_days:\n", "            change_sum += day.change\n", "        return change_sum\n", "\n", "    \"\"\"\n", "        下面的__str__，__iter__， __getitem__， __len__稍后会详细讲解作\n", "    \"\"\"\n", "\n", "    def __str__(self):\n", "        return str(self.stock_dict)\n", "\n", "    __repr__ = __str__\n", "\n", "    def __iter__(self):\n", "        \"\"\"\n", "        通过代理stock_dict的跌倒，yield元素\n", "        :return:\n", "        \"\"\"\n", "        for key in self.stock_dict:\n", "            yield self.stock_dict[key]\n", "\n", "    def __getitem__(self, ind):\n", "        date_key = self.__date_array[ind]\n", "        return self.stock_dict[date_key]\n", "\n", "    def __len__(self):\n", "        return len(self.stock_dict)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": ["OrderedDict([('20170118', stock(date='20170118', price='30.14', change=0)), ('20170119', stock(date='20170119', price='29.58', change=-0.019)), ('20170120', stock(date='20170120', price='26.36', change=-0.109)), ('20170121', stock(date='20170121', price='32.56', change=0.235)), ('20170122', stock(date='20170122', price='32.82', change=0.008))])"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["price_array = '30.14,29.58,26.36,32.56,32.82'.split(',')\n", "date_base = 20170118\n", "# 从StockTradeDays类初始化一个实例对象trade_days，内部会调用__init__\n", "trade_days = StockTradeDays(price_array, date_base)\n", "# 打印对象信息\n", "trade_days"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["trade_days对象长度为: 5\n"]}], "source": ["print('trade_days对象长度为: {}'.format(len(trade_days)))"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["stock(date='20170118', price='30.14', change=0)\n", "stock(date='20170119', price='29.58', change=-0.019)\n", "stock(date='20170120', price='26.36', change=-0.109)\n", "stock(date='20170121', price='32.56', change=0.235)\n", "stock(date='20170122', price='32.82', change=0.008)\n"]}], "source": ["from collections import Iterable\n", "# 如果是trade_days是可迭代对象，依次打印出\n", "if isinstance(trade_days, Iterable) :\n", "    for day in trade_days:\n", "        print(day)"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"data": {"text/plain": ["[stock(date='20170121', price='32.56', change=0.235),\n", " stock(date='20170122', price='32.82', change=0.008)]"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["trade_days.filter_stock()"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["([222.49, 223.54, 223.57, 224.82, 225.01],\n", " [20140723, 20140724, 20140725, 20140728, 20140729])"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["from abupy import ABuSymbolPd\n", "# 两年的TSLA收盘数据 to list\n", "price_array = ABuSymbolPd.make_kl_df('TSLA', n_folds=2).close.tolist()\n", "# 两年的TSLA收盘日期 to list，这里的写法不考虑效率，只做演示使用\n", "date_array = ABuSymbolPd.make_kl_df('TSLA', n_folds=2).date.tolist()\n", "price_array[:5], date_array[:5]"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["trade_days对象长度为: 504\n", "最后一天交易数据为：stock(date='20160726', price=225.93, change=-0.018)\n"]}], "source": ["trade_days = StockTradeDays(price_array, date_base, date_array) \n", "print('trade_days对象长度为: {}'.format(len(trade_days)))\n", "print('最后一天交易数据为：{}'.format(trade_days[-1]))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.3.2 继承和多态"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"collapsed": true}, "outputs": [], "source": ["import six\n", "from abc import ABCMeta, abstractmethod\n", "\n", "\n", "class TradeStrategyBase(six.with_metaclass(ABCMeta, object)):\n", "    \"\"\"\n", "        交易策略抽象基类\n", "    \"\"\"\n", "\n", "    @abstractmethod\n", "    def buy_strategy(self, *args, **kwargs):\n", "        # 买入策略基类\n", "        pass\n", "\n", "    @abstractmethod\n", "    def sell_strategy(self, *args, **kwargs):\n", "        # 卖出策略基类\n", "        pass\n", "\n", "class TradeStrategy1(TradeStrategyBase):\n", "    \"\"\"\n", "        交易策略1: 追涨策略，当股价上涨一个阀值默认为7%时\n", "        买入股票并持有s_keep_stock_threshold（20）天\n", "    \"\"\"\n", "    s_keep_stock_threshold = 20\n", "\n", "    def __init__(self):\n", "        self.keep_stock_day = 0\n", "        # 7%上涨幅度作为买入策略阀值\n", "        self.__buy_change_threshold = 0.07\n", "\n", "    def buy_strategy(self, trade_ind, trade_day, trade_days):\n", "        if self.keep_stock_day == 0 and \\\n", "                        trade_day.change > self.__buy_change_threshold:\n", "\n", "            # 当没有持有股票的时候self.keep_stock_day == 0 并且\n", "            # 符合买入条件上涨一个阀值，买入\n", "            self.keep_stock_day += 1\n", "        elif self.keep_stock_day > 0:\n", "            # self.keep_stock_day > 0代表持有股票，持有股票天数递增\n", "            self.keep_stock_day += 1\n", "\n", "    def sell_strategy(self, trade_ind, trade_day, trade_days):\n", "        if self.keep_stock_day >= \\\n", "                TradeStrategy1.s_keep_stock_threshold:\n", "            # 当持有股票天数超过阀值s_keep_stock_threshold，卖出股票\n", "            self.keep_stock_day = 0\n", "\n", "    \"\"\"\n", "        property属性稍后会讲到\n", "    \"\"\"\n", "    @property\n", "    def buy_change_threshold(self):\n", "        return self.__buy_change_threshold\n", "\n", "    @buy_change_threshold.setter\n", "    def buy_change_threshold(self, buy_change_threshold):\n", "        if not isinstance(buy_change_threshold, float):\n", "            \"\"\"\n", "                上涨阀值需要为float类型\n", "            \"\"\"\n", "            raise TypeError('buy_change_threshold must be float!')\n", "        # 上涨阀值只取小数点后两位\n", "        self.__buy_change_threshold = round(buy_change_threshold, 2)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"collapsed": true}, "outputs": [], "source": ["class TradeLoopBack(object):\n", "    \"\"\"\n", "        交易回测系统\n", "    \"\"\"\n", "\n", "    def __init__(self, trade_days, trade_strategy):\n", "        \"\"\"\n", "        使用上一节封装的StockTradeDays类和本节编写的交易策略类\n", "        TradeStrategyBase类初始化交易系统\n", "        :param trade_days: StockTradeDays交易数据序列\n", "        :param trade_strategy: TradeStrategyBase交易策略\n", "        \"\"\"\n", "        self.trade_days = trade_days\n", "        self.trade_strategy = trade_strategy\n", "        # 交易盈亏结果序列\n", "        self.profit_array = []\n", "\n", "    def execute_trade(self):\n", "        \"\"\"\n", "        执行交易回测\n", "        :return:\n", "        \"\"\"\n", "        for ind, day in enumerate(self.trade_days):\n", "            \"\"\"\n", "                以时间驱动，完成交易回测\n", "            \"\"\"\n", "            if self.trade_strategy.keep_stock_day > 0:\n", "                # 如果有持有股票，加入交易盈亏结果序列\n", "                self.profit_array.append(day.change)\n", "\n", "            # hasattr: 用来查询对象有没有实现某个方法\n", "            if hasattr(self.trade_strategy, 'buy_strategy'):\n", "                # 买入策略执行\n", "                self.trade_strategy.buy_strategy(ind, day,\n", "                                                 self.trade_days)\n", "                \n", "            if hasattr(self.trade_strategy, 'sell_strategy'):\n", "                # 卖出策略执行\n", "                self.trade_strategy.sell_strategy(ind, day,\n", "                                                  self.trade_days)"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["回测策略1 总盈亏为：37.60000000000001%\n"]}], "source": ["trade_loop_back = TradeLoopBack(trade_days, TradeStrategy1())\n", "trade_loop_back.execute_trade()\n", "print('回测策略1 总盈亏为：{}%'.format(reduce(lambda a, b: a + b, trade_loop_back.profit_array) * 100))"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAzAAAAGaCAYAAADQEKa6AAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nIiIiIiISM5TAiIiIiIhIzPj/YyRPQQ8YtggAAAAASUVORK5CYII=\n", "text/plain": ["<matplotlib.figure.Figure at 0x11331f2b0>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import seaborn as sns\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "%matplotlib inline\n", "sns.set_context(rc={'figure.figsize': (14, 7)} )\n", "plt.plot(np.array(trade_loop_back.profit_array).cumsum());"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.3.3 静态方法，类方法与property属性"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["回测策略1 总盈亏为：0.8000000000000004%\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAzwAAAGaCAYAAADKLEY0AAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\n", "text/plain": ["<matplotlib.figure.Figure at 0x118f185f8>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["trade_strategy1 = TradeStrategy1()\n", "# 买入阀值从0.07上升到0.1\n", "trade_strategy1.buy_change_threshold = 0.1\n", "trade_loop_back = TradeLoopBack(trade_days, trade_strategy1)\n", "trade_loop_back.execute_trade()\n", "print('回测策略1 总盈亏为：{}%'.format(reduce(lambda a, b: a + b, trade_loop_back.profit_array) * 100))\n", "# 可视化profit_array\n", "plt.plot(np.array(trade_loop_back.profit_array).cumsum());"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"collapsed": true}, "outputs": [], "source": ["class TradeStrategy2(TradeStrategyBase):\n", "    \"\"\"\n", "        交易策略2: 均值回复策略，当股价连续两个交易日下跌，\n", "        且下跌幅度超过阀值默认s_buy_change_threshold(-10%)，\n", "        买入股票并持有s_keep_stock_threshold（10）天\n", "    \"\"\"\n", "    # 买入后持有天数\n", "    s_keep_stock_threshold = 10\n", "    # 下跌买入阀值\n", "    s_buy_change_threshold = -0.10\n", "\n", "    def __init__(self):\n", "        self.keep_stock_day = 0\n", "\n", "    def buy_strategy(self, trade_ind, trade_day, trade_days):\n", "        if self.keep_stock_day == 0 and trade_ind >= 1:\n", "            \"\"\"\n", "                当没有持有股票的时候self.keep_stock_day == 0 并且\n", "                trade_ind >= 1, 不是交易开始的第一天，因为需要yesterday数据\n", "            \"\"\"\n", "            # trade_day.change < 0 bool：今天是否股价下跌\n", "            today_down = trade_day.change < 0\n", "            # 昨天是否股价下跌\n", "            yesterday_down = trade_days[trade_ind - 1].change < 0\n", "            # 两天总跌幅\n", "            down_rate = trade_day.change + \\\n", "                        trade_days[trade_ind - 1].change\n", "            if today_down and yesterday_down and down_rate < \\\n", "                    TradeStrategy2.s_buy_change_threshold:\n", "                # 买入条件成立：连跌两天，跌幅超过s_buy_change_threshold\n", "                self.keep_stock_day += 1\n", "        elif self.keep_stock_day > 0:\n", "            # self.keep_stock_day > 0代表持有股票，持有股票天数递增\n", "            self.keep_stock_day += 1\n", "\n", "    def sell_strategy(self, trade_ind, trade_day, trade_days):\n", "        if self.keep_stock_day >= \\\n", "                TradeStrategy2.s_keep_stock_threshold:\n", "            # 当持有股票天数超过阀值s_keep_stock_threshold，卖出股票\n", "            self.keep_stock_day = 0\n", "\n", "    \"\"\"\n", "        稍后会详细讲解classmethod，staticmethod\n", "    \"\"\"\n", "\n", "    @classmethod\n", "    def set_keep_stock_threshold(cls, keep_stock_threshold):\n", "        cls.s_keep_stock_threshold = keep_stock_threshold\n", "\n", "    @staticmethod\n", "    def set_buy_change_threshold(buy_change_threshold):\n", "        TradeStrategy2.s_buy_change_threshold = buy_change_threshold"]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["回测策略2 总盈亏为：31.900000000000006%\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAzYAAAGaCAYAAADdDtb9AAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nIlL0FNiIiIiIiEjR+/8Bf4D8+TWoVuoAAAAASUVORK5CYII=\n", "text/plain": ["<matplotlib.figure.Figure at 0x11cc3cc88>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["trade_strategy2 = TradeStrategy2()\n", "trade_loop_back = TradeLoopBack(trade_days, trade_strategy2)\n", "trade_loop_back.execute_trade()\n", "print('回测策略2 总盈亏为：{}%'.format(reduce(lambda a, b: a + b, trade_loop_back.profit_array) * 100))\n", "plt.plot(np.array(trade_loop_back.profit_array).cumsum());"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["回测策略2 总盈亏为：31.900000000000006%\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAzYAAAGaCAYAAADdDtb9AAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nIlL0FNiIiIiIiEjR+/8Bf4D8+TWoVuoAAAAASUVORK5CYII=\n", "text/plain": ["<matplotlib.figure.Figure at 0x11ca61c50>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 实例化一个新的TradeStrategy2类对象\n", "trade_strategy2 = TradeStrategy2()\n", "# 修改为买入后持有股票20天，默认为10天\n", "TradeStrategy2.set_keep_stock_threshold(20)\n", "# 修改股价下跌买入阀值为-0.08（下跌8%），默认为-0.10（下跌10%）\n", "TradeStrategy2.set_buy_change_threshold(-0.08)\n", "# 实例化新的回测对象trade_loop_back\n", "trade_loop_back = TradeLoopBack(trade_days, trade_strategy2)\n", "# 执行回测\n", "trade_loop_back.execute_trade()\n", "print('回测策略2 总盈亏为：{}%'.format(reduce(lambda a, b: a + b, trade_loop_back.profit_array) * 100))\n", "# 可视化回测结果\n", "plt.plot(np.array(trade_loop_back.profit_array).cumsum());"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2.4 性能效率"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##  2.4.1 itertools的使用"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"collapsed": true}, "outputs": [], "source": ["import itertools"]}, {"cell_type": "markdown", "metadata": {}, "source": ["permutations: 考虑顺序组合元素"]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(1, 2, 3)\n", "(1, 3, 2)\n", "(2, 1, 3)\n", "(2, 3, 1)\n", "(3, 1, 2)\n", "(3, 2, 1)\n"]}], "source": ["items = [1, 2, 3]\n", "for item in itertools.permutations(items):\n", "    print(item)"]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(1, 2)\n", "(1, 3)\n", "(2, 3)\n"]}], "source": ["for item in itertools.combinations(items, 2):\n", "    print(item)"]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(1, 1)\n", "(1, 2)\n", "(1, 3)\n", "(2, 2)\n", "(2, 3)\n", "(3, 3)\n"]}], "source": ["for item in itertools.combinations_with_replacement(items, 2):\n", "    print(item)"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["('a', 'c')\n", "('a', 'd')\n", "('b', 'c')\n", "('b', 'd')\n"]}], "source": ["ab = ['a', 'b']\n", "cd = ['c', 'd']\n", "\n", "# 针对ab，cd两个集合进行排列组合\n", "for item in itertools.product(ab, cd):\n", "    print(item)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/plain": ["(0.31900000000000006, 20, -0.08)"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["def calc(keep_stock_threshold, buy_change_threshold):\n", "    \"\"\"\n", "    :param keep_stock_threshold: 持股天数\n", "    :param buy_change_threshold: 下跌买入阀值\n", "    :return: 盈亏情况，输入的持股天数, 输入的下跌买入阀值\n", "    \"\"\"\n", "    # 实例化TradeStrategy2\n", "    trade_strategy2 = TradeStrategy2()\n", "    # 通过类方法设置买入后持股天数\n", "    TradeStrategy2.set_keep_stock_threshold(keep_stock_threshold)\n", "    # 通过类方法设置下跌买入阀值\n", "    TradeStrategy2.set_buy_change_threshold(buy_change_threshold)\n", "    # 进行回测\n", "    trade_loop_back = TradeLoopBack(trade_days, trade_strategy2)\n", "    trade_loop_back.execute_trade()\n", "    # 计算回测结果的最终盈亏值profit\n", "    profit = 0.0 if len(trade_loop_back.profit_array) == 0 else \\\n", "        reduce(lambda a, b: a + b, trade_loop_back.profit_array)\n", "    # 返回值profit和函数的两个输入参数\n", "    return profit, keep_stock_threshold, buy_change_threshold\n", "\n", "# 测试，使用上一节使用的参数\n", "calc(20, -0.08)"]}, {"cell_type": "code", "execution_count": 66, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["持股天数参数组：[2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28]\n", "下跌阀值参数组：[-0.05, -0.06, -0.07, -0.08, -0.09, -0.1, -0.11, -0.12, -0.13, -0.14, -0.15]\n"]}], "source": ["# range集合：买入后持股天数从2天－30天，间隔两天\n", "keep_stock_list = list(range(2, 30, 2))\n", "print('持股天数参数组：{}'.format(keep_stock_list))\n", "# 下跌买入阀值从-0.05到-0.15，即从下跌5%到15%\n", "buy_change_list = [buy_change / 100.0 for buy_change in xrange(-5, -16, -1)]\n", "print('下跌阀值参数组：{}'.format(buy_change_list))"]}, {"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["笛卡尔积参数集合总共结果为：154个\n"]}], "source": ["result = []\n", "for keep_stock_threshold, buy_change_threshold in itertools.product(\n", "        keep_stock_list, buy_change_list):\n", "    # 使用calc计算参数对应的最终盈利，结果加入result序列\n", "    result.append(calc(keep_stock_threshold, buy_change_threshold))\n", "print('笛卡尔积参数集合总共结果为：{}个'.format(len(result)))"]}, {"cell_type": "code", "execution_count": 59, "metadata": {}, "outputs": [{"data": {"text/plain": ["[(0.5790000000000001, 28, -0.1),\n", " (0.519, 26, -0.1),\n", " (0.5019999999999999, 28, -0.05),\n", " (0.4770000000000001, 24, -0.1),\n", " (0.4660000000000001, 22, -0.1),\n", " (0.45100000000000007, 16, -0.09),\n", " (0.44999999999999996, 20, -0.06),\n", " (0.44800000000000006, 28, -0.07),\n", " (0.437, 28, -0.13),\n", " (0.437, 28, -0.14)]"]}, "execution_count": 59, "metadata": {}, "output_type": "execute_result"}], "source": ["# [::-1]将整个排序结果反转，反转后盈亏收益从最高向低排序\n", "# [:10]取出收益最高的前10个组合查看\n", "sorted(result)[::-1][:10]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##  2.4.2 多进程 vs 多线程"]}, {"cell_type": "code", "execution_count": 61, "metadata": {"collapsed": true}, "outputs": [], "source": ["if abupy.env.g_is_mac_os:\n", "    from concurrent.futures import ProcessPoolExecutor\n", "    # mac下面执行多进程\n", "    result = []\n", "\n", "    # 回调函数，通过add_done_callback任务完成后调用\n", "    def when_done(r):\n", "        # when_done在主进程中运行\n", "        result.append(r.result())\n", "\n", "    \"\"\"\n", "        with class_a() as a: 上下文管理器：稍后会具体讲解\n", "    \"\"\"\n", "    with ProcessPoolExecutor() as pool:\n", "        for keep_stock_threshold, buy_change_threshold in \\\n", "                itertools.product(keep_stock_list, buy_change_list):\n", "\n", "            \"\"\"\n", "                submit提交任务：使用calc函数和的参数通过submit提交到独立进程\n", "                提交的任务必须是简单函数，进程并行不支持类方法、闭包等\n", "                函数参数和返回值必须兼容pickle序列化，进程间的通信需要\n", "            \"\"\"\n", "            future_result = pool.submit(calc, keep_stock_threshold,\n", "                                        buy_change_threshold)\n", "            # 当进程完成任务即calc运行结束后的回调函数\n", "            future_result.add_done_callback(when_done)\n", "    print(sorted(result)[::-1][:10])\n", "else:\n", "    print('在windows系统中使用ipython notebook进行多进程调度有系统bug。\\n\\\n", "          子进程的结果回不到notebook中的主进程。\\n\\\n", "          windows用户请使用python文件夹中本章对应的c2.py中的sample_242方法运行本例\\n\\\n", "          感谢读者\\'宪\\'提出发现本问题！')"]}, {"cell_type": "code", "execution_count": 63, "metadata": {"collapsed": true}, "outputs": [], "source": ["from concurrent.futures import ThreadPoolExecutor\n", "\n", "result = []\n", "def when_done(r):\n", "    result.append(r.result())\n", "\n", "with ThreadPoolExecutor(max_workers=8) as pool:\n", "    for keep_stock_threshold, buy_change_threshold in \\\n", "            itertools.product(keep_stock_list, buy_change_list):\n", "        future_result = pool.submit(calc, keep_stock_threshold,\n", "                                    buy_change_threshold)\n", "        future_result.add_done_callback(when_done)"]}, {"cell_type": "code", "execution_count": 64, "metadata": {}, "outputs": [{"data": {"text/plain": ["[(0.5790000000000001, 28, -0.1),\n", " (0.56, 24, -0.12),\n", " (0.5459999999999999, 28, -0.11),\n", " (0.519, 18, -0.1),\n", " (0.5019999999999999, 28, -0.05),\n", " (0.4770000000000001, 24, -0.1),\n", " (0.4660000000000001, 22, -0.1),\n", " (0.45, 20, -0.05),\n", " (0.44999999999999996, 20, -0.06),\n", " (0.44800000000000006, 28, -0.07)]"]}, "execution_count": 64, "metadata": {}, "output_type": "execute_result"}], "source": ["sorted(result)[::-1][:10]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##  2.4.3 使用编译库提高性能"]}, {"cell_type": "code", "execution_count": 65, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["笛卡尔积参数集合总共结果为：49797个\n", "CPU times: user 1min 1s, sys: 257 ms, total: 1min 2s\n", "Wall time: 1min 2s\n"]}], "source": ["# 买入后持股天数放大寻找范围 1 - 503 天, 间隔1天\n", "keep_stock_list = list(range(1, 504, 1))\n", "# 下跌买入阀值寻找范围 -0.01 - -0.99 共99个\n", "buy_change_list = [buy_change/100.0 for buy_change in xrange(-1, -100, -1)]\n", "def do_single_task():\n", "    task_list = list(itertools.product(keep_stock_list, buy_change_list))\n", "    print('笛卡尔积参数集合总共结果为：{}个'.format(len(task_list)))\n", "    for keep_stock_threshold, buy_change_threshold in task_list:\n", "        calc(keep_stock_threshold, buy_change_threshold)\n", "# %time ipython magic code 详情查阅附录章关于ipython的使用\n", "%time do_single_task()"]}, {"cell_type": "code", "execution_count": 60, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/anaconda2/lib/python2.7/site-packages/numba/dataflow.py:297: RuntimeWarning: Python2 style print partially supported.  Please use Python3 style print.\n", "  \"Python3 style print.\", RuntimeWarning)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["笛卡尔积参数集合总共结果为：49797个\n", "\n", "CPU times: user 1min 14s, sys: 280 ms, total: 1min 15s\n", "Wall time: 1min 15s\n"]}], "source": ["import numba as nb\n", "do_single_task_nb = nb.jit(do_single_task)\n", "%time do_single_task_nb()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2.5 代码调试"]}, {"cell_type": "code", "execution_count": 67, "metadata": {"collapsed": true}, "outputs": [], "source": ["def gen_buy_change_list():\n", "    buy_change_list = []\n", "    # 下跌买入阀值从-0.05到-0.15，即从下跌5%到15%\n", "    for buy_change in xrange(-5, -16, -1):\n", "        buy_change = buy_change/100\n", "        buy_change_list.append(buy_change)\n", "    return buy_change_list"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 书中本示例针对python3不适用，因为python3默认的除法就是小数"]}, {"cell_type": "code", "execution_count": 68, "metadata": {}, "outputs": [{"data": {"text/plain": ["[-0.05, -0.06, -0.07, -0.08, -0.09, -0.1, -0.11, -0.12, -0.13, -0.14, -0.15]"]}, "execution_count": 68, "metadata": {}, "output_type": "execute_result"}], "source": ["gen_buy_change_list()"]}, {"cell_type": "code", "execution_count": 95, "metadata": {"collapsed": true}, "outputs": [], "source": ["def gen_buy_change_list():\n", "    buy_change_list = []\n", "    for buy_change in xrange(-5, -16, -1):\n", "        # 1. 原始buy_change\n", "        print(buy_change)\n", "        buy_change = buy_change/100\n", "        # 2. buy_change/100\n", "        print(buy_change)\n", "        buy_change_list.append(buy_change)\n", "    return buy_change_list"]}, {"cell_type": "code", "execution_count": 69, "metadata": {}, "outputs": [{"data": {"text/plain": ["[-0.05, -0.06, -0.07, -0.08, -0.09, -0.1, -0.11, -0.12, -0.13, -0.14, -0.15]"]}, "execution_count": 69, "metadata": {}, "output_type": "execute_result"}], "source": ["# 2. 导入future库的division`from __future__ import division`\n", "from __future__ import division\n", "\n", "def gen_buy_change_list():\n", "    buy_change_list = []\n", "    for buy_change in xrange(-5, -16, -1):\n", "        # 1. 除数或者被除数其中一个是float类型\n", "        buy_change = buy_change/100.0\n", "        buy_change_list.append(buy_change)\n", "    return buy_change_list\n", "gen_buy_change_list()"]}, {"cell_type": "code", "execution_count": 70, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["gen_buy_change_list begin\n", "gen_buy_change_list end\n"]}], "source": ["import logging\n", "\n", "# 设置日志级别为info\n", "logging.basicConfig(level=logging.INFO)\n", "\n", "\n", "def gen_buy_change_list():\n", "    # 会打印出来，因为info >= level=logging.INFO\n", "    logging.info(\"gen_buy_change_list begin\")\n", "\n", "    buy_change_list = []\n", "    for buy_change in xrange(-5, -16, -1):\n", "        # 不会打印出来，debug < level=logging.INFO\n", "        logging.debug(buy_change)\n", "        buy_change = buy_change / 100\n", "        # 不会打印出来，debug < level=logging.INFO\n", "        logging.debug(buy_change)\n", "        buy_change_list.append(buy_change)\n", "    # 会打印出来，因为info >= level=logging.INFO\n", "    logging.info(\"gen_buy_change_list end\")\n", "    return buy_change_list\n", "\n", "\n", "_ = gen_buy_change_list()"]}, {"cell_type": "code", "execution_count": 72, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["> <ipython-input-72-de0eaf28a9db>(12)gen_buy_change_list()\n", "-> buy_change = buy_change / 100\n", "(Pdb) c\n", "--Return--\n", "> <ipython-input-72-de0eaf28a9db>(22)<module>()->None\n", "-> pdb.set_trace()\n", "(Pdb) c\n"]}], "source": ["import pdb\n", "\n", "\n", "def gen_buy_change_list():\n", "    buy_change_list = []\n", "    for buy_change in xrange(-5, -16, -1):\n", "        # 只针对循环执行到buy_change == -10，中断开始调试\n", "        if buy_change == -10:\n", "            # 打断点，通过set_trace\n", "            pdb.set_trace()\n", "\n", "        buy_change = buy_change / 100\n", "        buy_change_list.append(buy_change)\n", "    # 故意向外抛出异常\n", "    raise RuntimeError('debug for pdb')\n", "    return buy_change_list\n", "\n", "try:\n", "    _ = gen_buy_change_list()\n", "except Exception as e:\n", "    # 从捕获异常的地方开始调试，经常使用的调试技巧\n", "    pdb.set_trace()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true}, "outputs": [], "source": []}], "metadata": {"anaconda-cloud": {}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.1"}}, "nbformat": 4, "nbformat_minor": 1}