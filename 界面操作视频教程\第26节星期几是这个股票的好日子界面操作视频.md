
# ABU量化界面操作示例 


-----------------

## 第26节 星期几是这个股票的‘好日子’

本节将示例一个与周期相关的短线择时策略，本节的内容是为《量化交易之路》中的一个小节做的完整策略实例补充。


[视频教程播放地址](https://v.qq.com/x/page/l0556oqgemk.html)


[项目github地址](https://github.com/bbfamily/abu)

------------------------------------------

很多刚接触交易的人总喜欢把交易看成一种有固定收入的工作，比如他们有自己的规矩，周五一定要把所有股票都卖了，安安心心过周末，周一看情况一切良好再把股票买回来。

还有一些人有着很奇怪的癖好认为周三是他的幸运日，在周三买入他选中的股票，有些人每个月第一个周五发工资，市场就是由这些各种各样的人组成的。

------------------------------------------

某一个股票上的活跃用户在一段短时间内变化并不大，也就是说这些习惯周五卖周一买的人会反复在一支股票上交易，普通投资者普遍的投资方式是针对一支股票不断的进行买卖，他们不会长期持有这支股票，但也不会远离这支股票很长时间，我认为有两点促成了以上事实。

1. 贪欲：贪欲在这中间起到了很大的作用，当一个人第一次买入一支股票并且持有到有一定利润的时候，他选择卖出这支股票，因为他认为涨的已经很多了，该适当的回调了，之后股价的走势只有两种可能：第一按照他的预期下跌，这样的话他可能选择跌到某种程度再次进场买入这支股票；第二就是继续上涨，这种情况下他会选择不断‘诅咒’这支股票，直到有一天股价上涨到让他无法忍受，从此由‘黑转粉’。 

2. 时间成本与懒惰：一个人类的时间和精力都是有限的，它无法获取市场中所有股票的信息，每次获取熟悉一支股票的时间成本在他看来也是非常巨大，他反复的盯着自己最频繁买卖的那几支股票。

下面首先针对沙盒数据中的美股分析一年内的‘周几'涨跌概率

<br />

## 交易日涨跌概率分析

一个一个观察每一个股票的周期涨跌概率，可以发现：

1. 特斯拉在周四上涨的概率最大59%
2. 诺亚财富也在周四上涨的概率最大65%
3. 百度在周五上涨概率达到60%
4. 苹果在周三上涨概率达到56%

<br />

## 日胜率均值回复策略

策略大概原理如下：

策略的性质属于：**均值回复**

1. 默认以40天为周期(8周)结合涨跌阀值计算周几适合买入
2. 回测运行中每一个月重新计算一次上述的周几适合买入
3. 在策略日任务中买入信号为：昨天下跌，今天开盘也下跌，且明天是计算出来的上涨概率大的'周几'

下面开始分市场使用策略进行历史回测：

1. 使用沙盒数据中的美股symbol作为买入目标
2. 买入策略使用：日胜率均值回复策略
3. 卖出策略使用：持有N天后卖出策略（n=1）持有一天后当天卖出

<br />
_____

可以看到上面的回测中胜率超过了50%，交易单中可以看到所有交易都只持有了一天

上面的策略中计算'周几'上涨概率最大的交易周期默认为40天周期(8周)，这个周期长度不能太长也不能太短，因为某一个股票上的活跃用户只是在一段短时间内变化不大，但是**一个市场中的参与者随着时间的流逝，也在慢慢不断变化，不断新老交替，就像我们人类，每7年我们就是一个全新的自己，所有细胞血液都将完全更新一遍**。

下面使用这个策略对**比特币，莱特币**进行回测

<br />
____
## 日胜率均值回复策略A股市场回测
下面使用这个沙盒中**A股市场**symbol进行回测:
<br />

____

小结：

上面的回测交易由于使用沙盒数据，数据量少，所以实际上的回测效果一般，即如果一个策略有56%的胜率，那么一天只执行10次交易，你的胜率有各种可能，不一定达到56%，但是如果你能一天执行10000次以上，那么你的胜率如果不是56%，不管是更多或者更少，都代表你计算胜率的方式有问题。

所以针对上面这个策略，确定你拥有交易概率优势，只要一天内可以从不同市场、不同股票、不同时段内找到足够多的交易机会，执行足够多的次数，那你最后一定是盈利的，统计套利的核心思想就是这个，不只是要单纯追求胜率，更应该关注大数定律，寻找多元化的交易机会，最终达成理想的胜率。

在之后的章节中会示例不使用沙盒数据，在各个市场中通过这个策略寻找交易进行进行回测，请关注公众号中的代码示例更新

<br />
<br />
<br />