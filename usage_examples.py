#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
abupy参数提取器使用示例
演示各种常见的使用场景
"""

from universal_abupy_param_extractor import UniversalAbuFactorParamExtractor
import json

def example_basic_usage():
    """示例1: 基本使用"""
    print("=== 示例1: 基本使用 ===")
    
    # 创建提取器
    extractor = UniversalAbuFactorParamExtractor()
    
    # 检查环境
    print(f"abupy路径: {extractor.abupy_path}")
    print(f"可导入abupy: {extractor.abupy_available}")
    
    # 提取因子结构
    factor_structure = extractor.extract_complete_factor_structure()
    
    # 显示统计信息
    print(f"买入因子: {len(factor_structure['buy_factors'])}个")
    print(f"卖出因子: {len(factor_structure['sell_factors'])}个")
    print(f"仓位管理: {len(factor_structure['position_classes'])}个")
    
    return factor_structure

def example_api_schema_generation():
    """示例2: 生成API Schema"""
    print("\n=== 示例2: 生成API Schema ===")
    
    extractor = UniversalAbuFactorParamExtractor()
    factor_structure = extractor.extract_complete_factor_structure()
    
    # 生成API Schema
    api_schema = extractor.generate_api_schema(factor_structure)
    
    # 保存Schema
    with open('example_api_schema.json', 'w', encoding='utf-8') as f:
        json.dump(api_schema, f, indent=2, ensure_ascii=False)
    
    print("✅ API Schema已保存到 example_api_schema.json")
    
    # 显示Schema结构
    strategy_create = api_schema['StrategyCreate']
    properties = strategy_create['properties']
    
    print(f"Schema包含字段: {list(properties.keys())}")
    print(f"必需字段: {strategy_create.get('required', [])}")
    
    return api_schema

def example_parameter_analysis():
    """示例3: 参数分析"""
    print("\n=== 示例3: 参数分析 ===")
    
    extractor = UniversalAbuFactorParamExtractor()
    factor_structure = extractor.extract_complete_factor_structure()
    
    # 分析买入因子参数
    print("\n📊 买入因子参数分析:")
    all_buy_params = {}
    
    for factor_name, factor_info in factor_structure['buy_factors'].items():
        params = factor_info.get('parameters', {})
        print(f"  {factor_name}: {len(params)}个参数")
        
        for param_name, param_info in params.items():
            if param_name not in all_buy_params:
                all_buy_params[param_name] = {
                    'type': param_info['type'],
                    'factors': []
                }
            all_buy_params[param_name]['factors'].append(factor_name)
    
    # 显示最常用的参数
    print(f"\n🔥 最常用的买入因子参数:")
    sorted_params = sorted(all_buy_params.items(), 
                          key=lambda x: len(x[1]['factors']), 
                          reverse=True)
    
    for param_name, param_info in sorted_params[:5]:
        factor_count = len(param_info['factors'])
        print(f"  {param_name} ({param_info['type']}): {factor_count}个因子使用")
    
    # 分析仓位管理参数
    print(f"\n💰 仓位管理参数:")
    for pos_class, pos_info in factor_structure['position_classes'].items():
        params = pos_info.get('parameters', {})
        print(f"  {pos_class}: {list(params.keys())}")
    
    return all_buy_params

def example_strategy_validation():
    """示例4: 策略配置验证"""
    print("\n=== 示例4: 策略配置验证 ===")
    
    extractor = UniversalAbuFactorParamExtractor()
    factor_structure = extractor.extract_complete_factor_structure()
    
    # 示例策略配置
    strategy_config = {
        "name": "测试策略",
        "description": "双均线策略示例",
        "read_cash": 100000,
        "buy_factors": [
            {
                "class_name": "AbuDoubleMaBuy",
                "parameters": {
                    "fast": 5,
                    "slow": 20,
                    "position": {
                        "class": "AbuAtrPosition",
                        "atr_base_price": 18,
                        "atr_pos_base": 0.15
                    }
                }
            }
        ],
        "sell_factors": [
            {
                "class_name": "AbuFactorAtrNStop",
                "parameters": {
                    "stop_loss_n": 2.0
                }
            }
        ]
    }
    
    # 验证策略配置
    validation_result = validate_strategy_config(strategy_config, factor_structure)
    
    if validation_result['valid']:
        print("✅ 策略配置验证通过")
    else:
        print("❌ 策略配置验证失败:")
        for error in validation_result['errors']:
            print(f"  - {error}")
    
    return validation_result

def validate_strategy_config(config, factor_structure):
    """验证策略配置的有效性"""
    errors = []
    
    # 验证买入因子
    for factor in config.get('buy_factors', []):
        class_name = factor.get('class_name')
        
        if class_name not in factor_structure['buy_factors']:
            errors.append(f"未知的买入因子: {class_name}")
            continue
        
        # 验证参数
        factor_params = factor_structure['buy_factors'][class_name]['parameters']
        config_params = factor.get('parameters', {})
        
        # 检查必需参数
        for param_name, param_info in factor_params.items():
            if param_info.get('required', False) and param_name not in config_params:
                errors.append(f"{class_name}缺少必需参数: {param_name}")
        
        # 验证仓位管理配置
        position_config = config_params.get('position', {})
        if position_config:
            pos_class = position_config.get('class')
            if pos_class and pos_class not in factor_structure['position_classes']:
                errors.append(f"未知的仓位管理类: {pos_class}")
    
    # 验证卖出因子
    for factor in config.get('sell_factors', []):
        class_name = factor.get('class_name')
        
        if class_name not in factor_structure['sell_factors']:
            errors.append(f"未知的卖出因子: {class_name}")
    
    return {
        'valid': len(errors) == 0,
        'errors': errors
    }

def example_frontend_integration():
    """示例5: 前端集成"""
    print("\n=== 示例5: 前端集成 ===")
    
    extractor = UniversalAbuFactorParamExtractor()
    factor_structure = extractor.extract_complete_factor_structure()
    
    # 生成前端需要的因子选项
    frontend_data = {
        'buy_factor_options': [],
        'sell_factor_options': [],
        'position_class_options': []
    }
    
    # 买入因子选项
    for factor_name, factor_info in factor_structure['buy_factors'].items():
        option = {
            'value': factor_name,
            'label': factor_name,
            'parameters': []
        }
        
        for param_name, param_info in factor_info['parameters'].items():
            param_option = {
                'name': param_name,
                'type': param_info['type'],
                'required': param_info.get('required', False),
                'default': param_info.get('default')
            }
            option['parameters'].append(param_option)
        
        frontend_data['buy_factor_options'].append(option)
    
    # 仓位管理选项
    for pos_class, pos_info in factor_structure['position_classes'].items():
        option = {
            'value': pos_class,
            'label': pos_class,
            'parameters': []
        }
        
        for param_name, param_info in pos_info['parameters'].items():
            param_option = {
                'name': param_name,
                'type': param_info['type'],
                'default': param_info.get('default'),
                'description': param_info.get('description', '')
            }
            option['parameters'].append(param_option)
        
        frontend_data['position_class_options'].append(option)
    
    # 保存前端数据
    with open('frontend_factor_options.json', 'w', encoding='utf-8') as f:
        json.dump(frontend_data, f, indent=2, ensure_ascii=False)
    
    print("✅ 前端选项数据已保存到 frontend_factor_options.json")
    print(f"买入因子选项: {len(frontend_data['buy_factor_options'])}个")
    print(f"仓位管理选项: {len(frontend_data['position_class_options'])}个")
    
    return frontend_data

def example_parameter_documentation():
    """示例6: 生成参数文档"""
    print("\n=== 示例6: 生成参数文档 ===")
    
    extractor = UniversalAbuFactorParamExtractor()
    factor_structure = extractor.extract_complete_factor_structure()
    
    # 生成Markdown文档
    doc_lines = []
    doc_lines.append("# abupy因子参数文档\n")
    doc_lines.append("## 买入因子参数\n")
    
    for factor_name, factor_info in factor_structure['buy_factors'].items():
        doc_lines.append(f"### {factor_name}\n")
        
        params = factor_info.get('parameters', {})
        if params:
            doc_lines.append("| 参数名 | 类型 | 必需 | 默认值 |")
            doc_lines.append("|--------|------|------|--------|")
            
            for param_name, param_info in params.items():
                required = "是" if param_info.get('required', False) else "否"
                default = param_info.get('default', 'N/A')
                doc_lines.append(f"| {param_name} | {param_info['type']} | {required} | {default} |")
        else:
            doc_lines.append("无用户可配置参数")
        
        doc_lines.append("")
    
    # 保存文档
    with open('parameter_documentation.md', 'w', encoding='utf-8') as f:
        f.write('\n'.join(doc_lines))
    
    print("✅ 参数文档已保存到 parameter_documentation.md")
    
    return doc_lines

def main():
    """运行所有示例"""
    print("🚀 abupy参数提取器使用示例\n")
    
    try:
        # 运行各个示例
        factor_structure = example_basic_usage()
        api_schema = example_api_schema_generation()
        param_analysis = example_parameter_analysis()
        validation_result = example_strategy_validation()
        frontend_data = example_frontend_integration()
        documentation = example_parameter_documentation()
        
        print(f"\n🎉 所有示例运行完成！")
        print(f"生成的文件:")
        print(f"  - example_api_schema.json")
        print(f"  - frontend_factor_options.json")
        print(f"  - parameter_documentation.md")
        
    except Exception as e:
        print(f"❌ 运行示例时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
