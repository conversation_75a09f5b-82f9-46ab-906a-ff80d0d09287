{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# ABU量化系统使用文档 \n", "\n", "<center>\n", "        <img src=\"./image/abu_logo.png\" alt=\"\" style=\"vertical-align:middle;padding:10px 20px;\"><font size=\"6\" color=\"black\"><b>第20节 A股全市场回测</b></font>\n", "</center>\n", "\n", "-----------------\n", "\n", "作者: 阿布\n", "\n", "阿布量化版权所有 未经允许 禁止转载\n", "\n", "[abu量化系统github地址](https://github.com/bbfamily/abu) (欢迎+star)\n", "\n", "[本节ipython notebook](https://github.com/bbfamily/abu/tree/master/abupy_lecture)\n", "\n", "在第19节‘数据源’中分别获取了各个市场的6年交易数据，本节将做A股市场全市场回测。\n", "\n", "首先导入abupy中本节使用的模块："]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["enable example env will only read RomDataBu/df_kl.h5\n"]}], "source": ["# 基础库导入\n", "from __future__ import print_function\n", "from __future__ import division\n", "\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "warnings.simplefilter('ignore')\n", "\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import ipywidgets\n", "%matplotlib inline\n", "\n", "import os\n", "import sys\n", "# 使用insert 0即只使用github，避免交叉使用了pip安装的abupy，导致的版本不一致问题\n", "sys.path.insert(0, os.path.abspath('../'))\n", "import abupy\n", "\n", "# 使用沙盒数据，目的是和书中一样的数据环境\n", "abupy.env.enable_example_env_ipython()"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["disable example env\n"]}], "source": ["from abupy import AbuFactorAtrNStop, AbuFactorPreAtrNStop, AbuFactorCloseAtrNStop, AbuFactorBuyBreak\n", "from abupy import abu, EMarketTargetType, AbuMetricsBase, ABuMarketDrawing, ABuProgress, ABuSymbolPd\n", "from abupy import EMarketTargetType, EDataCacheType, EMarketSourceType, EMarketDataFetchMode, EStoreAbu, AbuUmpMainMul\n", "from abupy import AbuUmpMainDeg, AbuUmpMainJump, AbuUmpMainPrice, AbuUmpMainWave, feature, AbuFeatureDegExtend\n", "from abupy import AbuUmpEdgeDeg, AbuUmpEdgePrice, AbuUmpEdgeWave, AbuUmpEdgeFull, AbuUmpEdgeMul, AbuUmpEegeDegExtend\n", "from abupy import AbuUmpMainDegExtend, ump\n", "# 关闭沙盒数据\n", "abupy.env.disable_example_env_ipython()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["所有数据已存放在百度云盘上，后面的章节使用的数据都是本节更新的数据，建议直接从云盘下载入库完毕的数据库，不需要从各个数据源再一个一个的下载数据进行入库，百度云地址如下：\n", "\n", "[csv格式美股，A股，港股，币类，期货6年日k数据](https://pan.baidu.com/s/1geNZgqf) 密码: gvtr\n", "\n", "下面的数据存贮格式为hdf5数据，由于hdf5文件解压后非常大，还需要区分python版本，所以如果没有足够的存贮空间\n", "特别是python2下，建议使用csv格式的缓存文件：\n", "\n", "[mac系统python3 美股，A股，港股，币类，期货6年日k数据](https://pan.baidu.com/s/1o8sldNk) 密码: ecyp\n", "\n", "[mac系统python2 A股6年日k数据: ](https://pan.baidu.com/s/1bptn25h) 密码: sid8\n", "\n", "[windows python3 美股，A股，港股，币类，期货6年日k数据](https://pan.baidu.com/s/1bzeVHO) 密码: 3cwe\n", "\n", "[windows python2 A股6年日k数据: ](https://pan.baidu.com/s/1skZOe7N) 密码: 78mb\n", "\n", "下载完毕上述数据后，hdf5解压得到df_kl.h5文件，csv解压得到csv文件夹，解压后放到下面路径下即可"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true}, "outputs": [], "source": ["if abupy.env.g_is_mac_os:\n", "    !open $abupy.env.g_project_data_dir\n", "else:\n", "    !echo $abupy.env.g_project_data_dir"]}, {"cell_type": "markdown", "metadata": {}, "source": ["如果不想通过直接下载数据文件的方式，也可运行下面的cell点击按钮后进行A股数据全市场更新，如果运行过就不要重复运行了："]}, {"cell_type": "code", "execution_count": 3, "metadata": {"collapsed": true}, "outputs": [], "source": ["def bd_cn():\n", "    abupy.env.g_market_source = EMarketSourceType.E_MARKET_SOURCE_bd\n", "    abupy.env.g_data_cache_type = EDataCacheType.E_DATA_CACHE_CSV\n", "    abu.run_kl_update(start='2011-08-08', end='2017-08-08', market=EMarketTargetType.E_MARKET_TARGET_CN, n_jobs=32)\n", "\n", "import ipywidgets\n", "# 避免使用notebook运行run all时运行不想执行的代码\n", "_ = ipywidgets.interact_manual(bd_cn)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["买入因子，卖出因子等依然使用相同的设置，如下所示："]}, {"cell_type": "code", "execution_count": 4, "metadata": {"collapsed": true}, "outputs": [], "source": ["# 初始化资金500万\n", "read_cash = 5000000\n", "\n", "# 买入因子依然延用向上突破因子\n", "buy_factors = [{'xd': 60, 'class': AbuFactorBuyBreak},\n", "               {'xd': 42, 'class': AbuFactorBuyBreak}]\n", "\n", "# 卖出因子继续使用上一节使用的因子\n", "sell_factors = [\n", "    {'stop_loss_n': 1.0, 'stop_win_n': 3.0,\n", "     'class': AbuFactorAtrNStop},\n", "    {'class': AbuFactorPreAtrNStop, 'pre_atr_n': 1.5},\n", "    {'class': AbuFactorCloseAtr<PERSON><PERSON>, 'close_atr_n': 1.5}\n", "]\n", "abupy.env.g_market_target = EMarketTargetType.E_MARKET_TARGET_CN\n", "abupy.env.g_data_fetch_mode = EMarketDataFetchMode.E_DATA_FETCH_FORCE_LOCAL"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1. A股交易训练集回测\n", "\n", "下面将回测市场设置为A股市场："]}, {"cell_type": "code", "execution_count": 3, "metadata": {"collapsed": true}, "outputs": [], "source": ["abupy.env.g_market_target = EMarketTargetType.E_MARKET_TARGET_CN"]}, {"cell_type": "markdown", "metadata": {}, "source": ["将数据读取模式设置为本地数据模式，即进行全市场回测时最合适的模式，运行效率高，且分类数据更新和交易回测。"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"collapsed": true}, "outputs": [], "source": ["abupy.env.g_data_fetch_mode = EMarketDataFetchMode.E_DATA_FETCH_FORCE_LOCAL"]}, {"cell_type": "markdown", "metadata": {}, "source": ["下面根据下载的数据缓存类型设置缓存类型，如果下载解压的是csv需要勾选use_csv，如果是hdf5不需要勾选："]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["EDataCacheType.E_DATA_CACHE_HDF5\n"]}], "source": ["def select_store_cache(use_csv):\n", "    if use_csv:\n", "        abupy.env.g_data_cache_type = EDataCacheType.E_DATA_CACHE_CSV\n", "    else:\n", "        abupy.env.g_data_cache_type = EDataCacheType.E_DATA_CACHE_HDF5\n", "    print(abupy.env.g_data_cache_type)\n", "        \n", "use_csv = ipywidgets.Checkbox(True)\n", "_ = ipywidgets.interact(select_store_cache, use_csv=use_csv)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["下面通过env中的设置将回测中的symbols切分为回测训练集与回测测试集，且打开回测生成买入时刻特征开关：\n", "\n", "详情请查询ABuMarket模块"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"collapsed": true}, "outputs": [], "source": ["# 回测生成买入时刻特征\n", "abupy.env.g_enable_ml_feature = True\n", "# 回测开始时将symbols切割分为训练集数据和测试集两份，使用训练集进行回测\n", "abupy.env.g_enable_train_test_split = True"]}, {"cell_type": "markdown", "metadata": {}, "source": ["上面切割训练集，测试集使用的切割比例参数默认为10，即切割为10份，9份做为训练，1份做为测试，也可自定义切割比例，在之后的章节示例"]}, {"cell_type": "markdown", "metadata": {}, "source": ["资金管理依然使用默认atr，每笔交易的买入基数资金设置为万分之30，这个值如果设置太大，比如初始默认的0.1的话，将会导致太多的股票由于资金不足无法买入，丧失全市场回测的意义，如果太小的话又会导致资金利用率下降，导致最终收益下降：\n", "\n", "更多资金管理请阅读相关源代码或《量化交易之路》中相关内容"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"collapsed": true}, "outputs": [], "source": ["# 每笔交易的买入基数资金设置为万分之30\n", "abupy.beta.atr.g_atr_pos_base = 0.003"]}, {"cell_type": "markdown", "metadata": {}, "source": ["下面添加‘第18节 自定义裁判决策交易‘中示例编写的10，30，50，90，120日走势拟合角度特征AbuFeatureDegExtend，做为回测时的新的视角来录制比赛（记录回测特征）"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"collapsed": true}, "outputs": [], "source": ["feature.clear_user_feature()\n", "feature.append_user_feature(AbuFeatureDegExtend)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["在上面run_kl_update中更新了从2011-08-08至2017-08-08，由于在买入时刻生成特征，所以要保留一年的数据做为特征数据回测时段，所以下面的回测start使用2012-08-08至2017-08-08，即向后推了一年做回测：\n", "\n", "下面开始回测，第一次运行select：run loop back，然后点击run select，如果已经回测过可select：load train data直接从缓存数据读取："]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true}, "outputs": [], "source": ["abu_result_tuple = None\n", "def run_loop_back():\n", "    global abu_result_tuple\n", "    abu_result_tuple, _ = abu.run_loop_back(read_cash,\n", "                                            buy_factors,\n", "                                            sell_factors,\n", "                                            choice_symbols=None,\n", "                                            start='2012-08-08', end='2017-08-08')\n", "    # 把运行的结果保存在本地，以便之后分析回测使用，保存回测结果数据代码如下所示\n", "    abu.store_abu_result_tuple(abu_result_tuple, n_folds=5, store_type=EStoreAbu.E_STORE_CUSTOM_NAME, \n", "                               custom_name='train_cn')\n", "    ABuProgress.clear_output()\n", "\n", "def run_load_train():\n", "    global abu_result_tuple\n", "    abu_result_tuple = abu.load_abu_result_tuple(n_folds=5, store_type=EStoreAbu.E_STORE_CUSTOM_NAME, \n", "                                                 custom_name='train_cn')\n", "\n", "def select(select):\n", "    if select == 'run loop back':\n", "        run_loop_back()\n", "    else:\n", "        run_load_train()\n", "\n", "_ = ipywidgets.interact_manual(select, select=['run loop back', 'load train data'])"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["买入后卖出的交易数量:55224\n", "买入后尚未卖出的交易数量:951\n", "胜率:47.1353%\n", "平均获利期望:16.5030%\n", "平均亏损期望:-7.7784%\n", "盈亏比:1.9844\n", "策略收益: 111.4373%\n", "基准收益: 52.5454%\n", "策略年化收益: 23.1320%\n", "基准年化收益: 10.9073%\n", "策略买入成交比例:31.0939%\n", "策略资金利用率比例:75.5849%\n", "策略共执行1214个交易日\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAzYAAAF/CAYAAACbh1zMAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nUB19nfwr+**************************/TICH/xi38Ozym7XpZkuiFrCrYr/LPg+DOwA9tl5q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\nIcSkJ4GNEEIIIYQQYtL7/8GAxaktKIfuAAAAAElFTkSuQmCC\n", "text/plain": ["<matplotlib.figure.Figure at 0x1382fb9b0>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["<abupy.MetricsBu.ABuMetricsBase.AbuMetricsBase at 0x120794710>"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["AbuMetricsBase.show_general(*abu_result_tuple, only_show_returns=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2. A股交易测试集回测\n", "\n", "下面通过env中设置使用刚才切分的测试集美股symbol，它使用pickle读取之前已经切割好的本地化测试集股票代码序列："]}, {"cell_type": "code", "execution_count": 45, "metadata": {"collapsed": true}, "outputs": [], "source": ["# 测试集回测时依然生成买入时刻特征\n", "abupy.env.g_enable_ml_feature = True\n", "# 回测时不重新切割训练集数据和测试集\n", "abupy.env.g_enable_train_test_split = False\n", "# 回测时使用切割好的测试数据\n", "abupy.env.g_enable_last_split_test = True\n", "\n", "\n", "# 测试集依然使用10，30，50，90，120日走势拟合角度特征AbuFeatureDegExtend，做为回测时的新的视角来录制比赛（记录回测特征）\n", "feature.clear_user_feature()\n", "feature.append_user_feature(AbuFeatureDegExtend)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["提高g_atr_pos_base为0.025（因为默认切割训练集与测试集数据比例为9:1，所以提高g_atr_pos_base为之前的大概8-10倍都可以："]}, {"cell_type": "code", "execution_count": 41, "metadata": {"collapsed": true}, "outputs": [], "source": ["abupy.beta.atr.g_atr_pos_base = 0.025"]}, {"cell_type": "markdown", "metadata": {}, "source": ["下面开始测试集回测，同训练集第一次运行select：run loop back，然后点击run select，如果已经回测过可select：load train data直接从缓存数据读取："]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["please wait! load_pickle....: /Users/<USER>/abu/data/cache/n5_test_cn_capital\n", "please wait! load_pickle....: /Users/<USER>/abu/data/cache/n5_test_cn_benchmark\n"]}], "source": ["abu_result_tuple_test = None\n", "def run_loop_back_test():\n", "    global abu_result_tuple_test\n", "    abu_result_tuple_test, _ = abu.run_loop_back(read_cash,\n", "                                                 buy_factors,\n", "                                                 sell_factors,\n", "                                                 choice_symbols=None,\n", "                                                 start='2012-08-08', end='2017-08-08')\n", "    # 把运行的结果保存在本地，以便之后分析回测使用，保存回测结果数据代码如下所示\n", "    abu.store_abu_result_tuple(abu_result_tuple_test, n_folds=5, store_type=EStoreAbu.E_STORE_CUSTOM_NAME, \n", "                               custom_name='test_cn')\n", "    ABuProgress.clear_output()\n", "\n", "def run_load_test():\n", "    global abu_result_tuple_test\n", "    abu_result_tuple_test = abu.load_abu_result_tuple(n_folds=5, store_type=EStoreAbu.E_STORE_CUSTOM_NAME, \n", "                                                 custom_name='test_cn')\n", "\n", "def select_test(select):\n", "    if select == 'run loop back':\n", "        run_loop_back_test()\n", "    else:\n", "        run_load_test()\n", "\n", "_ = ipywidgets.interact_manual(select_test, select=['run loop back', 'load test data'])"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["买入后卖出的交易数量:6321\n", "买入后尚未卖出的交易数量:124\n", "胜率:47.6507%\n", "平均获利期望:15.7211%\n", "平均亏损期望:-7.6456%\n", "盈亏比:2.0477\n", "策略收益: 102.4382%\n", "基准收益: 52.5454%\n", "策略年化收益: 21.2639%\n", "基准年化收益: 10.9073%\n", "策略买入成交比例:26.2064%\n", "策略资金利用率比例:73.9138%\n", "策略共执行1214个交易日\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAzYAAAF/CAYAAACbh1zMAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nYII=\n", "text/plain": ["<matplotlib.figure.Figure at 0x17729eda0>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["<abupy.MetricsBu.ABuMetricsBase.AbuMetricsBase at 0x127bdbfd0>"]}, "execution_count": 43, "metadata": {}, "output_type": "execute_result"}], "source": ["AbuMetricsBase.show_general(*abu_result_tuple_test, only_show_returns=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["小结：本节所有操作都非常耗时，特别是在cpu不够快的电脑上，建议睡觉的时候运行哈，下一节将使用本节回测好的数据进行ump训练优化示例。"]}, {"cell_type": "markdown", "metadata": {"collapsed": true}, "source": ["#### abu量化文档目录章节\n", "\n", "1. [择时策略的开发](http://www.abuquant.com/lecture/lecture_1.html)\n", "2. [择时策略的优化](http://www.abuquant.com/lecture/lecture_2.html)\n", "3. [滑点策略与交易手续费](http://www.abuquant.com/lecture/lecture_3.html)\n", "4. [多支股票择时回测与仓位管理](http://www.abuquant.com/lecture/lecture_4.html)\n", "5. [选股策略的开发](http://www.abuquant.com/lecture/lecture_5.html)\n", "6. [回测结果的度量](http://www.abuquant.com/lecture/lecture_6.html)\n", "7. [寻找策略最优参数和评分](http://www.abuquant.com/lecture/lecture_7.html)\n", "8. [A股市场的回测](http://www.abuquant.com/lecture/lecture_8.html)\n", "9. [港股市场的回测](http://www.abuquant.com/lecture/lecture_9.html)\n", "10. [比特币，莱特币的回测](http://www.abuquant.com/lecture/lecture_10.html)\n", "11. [期货市场的回测](http://www.abuquant.com/lecture/lecture_11.html)\n", "12. [机器学习与比特币示例](http://www.abuquant.com/lecture/lecture_12.html)\n", "13. [量化技术分析应用](http://www.abuquant.com/lecture/lecture_13.html)\n", "14. [量化相关性分析应用](http://www.abuquant.com/lecture/lecture_14.html)\n", "15. [量化交易和搜索引擎](http://www.abuquant.com/lecture/lecture_15.html)\n", "16. [UMP主裁交易决策](http://www.abuquant.com/lecture/lecture_16.html)\n", "17. [UMP边裁交易决策](http://www.abuquant.com/lecture/lecture_17.html)\n", "18. [自定义裁判决策交易](http://www.abuquant.com/lecture/lecture_18.html)\n", "19. [数据源](http://www.abuquant.com/lecture/lecture_19.html)\n", "20. [A股全市场回测](http://www.abuquant.com/lecture/lecture_20.html)\n", "21. [A股UMP决策](http://www.abuquant.com/lecture/lecture_21.html)\n", "22. [美股全市场回测](http://www.abuquant.com/lecture/lecture_22.html)\n", "23. [美股UMP决策](http://www.abuquant.com/lecture/lecture_23.html)\n", "\n", "abu量化系统文档教程持续更新中，请关注公众号中的更新提醒。\n", "\n", "#### 《量化交易之路》目录章节及随书代码地址\n", "\n", "1. [第二章 量化语言——Python](https://github.com/bbfamily/abu/tree/master/ipython/第二章-量化语言——Python.ipynb)\n", "2. [第三章 量化工具——NumPy](https://github.com/bbfamily/abu/tree/master/ipython/第三章-量化工具——NumPy.ipynb)\n", "3. [第四章 量化工具——pandas](https://github.com/bbfamily/abu/tree/master/ipython/第四章-量化工具——pandas.ipynb)\n", "4. [第五章 量化工具——可视化](https://github.com/bbfamily/abu/tree/master/ipython/第五章-量化工具——可视化.ipynb)\n", "5. [第六章 量化工具——数学：你一生的追求到底能带来多少幸福](https://github.com/bbfamily/abu/tree/master/ipython/第六章-量化工具——数学.ipynb)\n", "6. [第七章 量化系统——入门：三只小猪股票投资的故事](https://github.com/bbfamily/abu/tree/master/ipython/第七章-量化系统——入门.ipynb)\n", "7. [第八章 量化系统——开发](https://github.com/bbfamily/abu/tree/master/ipython/第八章-量化系统——开发.ipynb)\n", "8. [第九章 量化系统——度量与优化](https://github.com/bbfamily/abu/tree/master/ipython/第九章-量化系统——度量与优化.ipynb)\n", "9. [第十章 量化系统——机器学习•猪老三](https://github.com/bbfamily/abu/tree/master/ipython/第十章-量化系统——机器学习•猪老三.ipynb)\n", "10. [第十一章 量化系统——机器学习•ABU](https://github.com/bbfamily/abu/tree/master/ipython/第十一章-量化系统——机器学习•ABU.ipynb)\n", "11. [附录A 量化环境部署](https://github.com/bbfamily/abu/tree/master/ipython/附录A-量化环境部署.ipynb)\n", "12. [附录B 量化相关性分析](https://github.com/bbfamily/abu/tree/master/ipython/附录B-量化相关性分析.ipynb)\n", "13. [附录C 量化统计分析及指标应用](https://github.com/bbfamily/abu/tree/master/ipython/附录C-量化统计分析及指标应用.ipynb)\n", "\n", "[更多阿布量化量化技术文章](http://www.abuquant.com/article)\n", "\n", "\n", "更多关于量化交易相关请阅读[《量化交易之路》](http://www.abuquant.com/books/quantify-trading-road.html)\n", "\n", "更多关于量化交易与机器学习相关请阅读[《机器学习之路》](http://www.abuquant.com/books/machine-learning-road.html)\n", "\n", "更多关于abu量化系统请关注微信公众号: abu_quant\n", "\n", "![](./image/qrcode.jpg)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.2"}}, "nbformat": 4, "nbformat_minor": 2}