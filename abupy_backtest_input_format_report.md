# abupy回测系统输入格式完整勘探报告

**勘探日期**: 2025年8月13日  
**勘探目标**: 确定abupy回测系统所需的输入数据格式和结构  
**勘探状态**: 深度验证完成 ✅  
**验证方法**: 基于abupy源码深度分析  
**勘探文件**: `abupy_backtest_input_format_exploration.py`

---

## 执行摘要

本报告通过对abupy框架源码的深入分析和验证，确定了回测系统接收策略数据的完整格式要求。主要发现包括：

1. **核心入口函数**: `abu.run_loop_back()` (源码: `abupy/CoreBu/ABu.py` 第27-32行)
2. **必需参数**: 3个核心参数 (read_cash, buy_factors, sell_factors)
3. **数据结构**: 基于字典的因子配置格式，必须包含'class'键
4. **扩展能力**: 支持嵌套position字典、专属sell_factors、UMP裁判系统等高级功能
5. **源码验证**: 所有结论均基于实际源码实现，无推测成分

---

## 1. 核心回测函数签名

### 1.1 函数定义

**函数**: `abu.run_loop_back()`  
**源文件**: `abupy/CoreBu/ABu.py`  
**行号**: 27-32

```python
def run_loop_back(read_cash, buy_factors, sell_factors, stock_picks=None, choice_symbols=None, n_folds=2,
                  start=None, end=None, commission_dict=None, n_process_kl=None, n_process_pick=None):
```

### 1.2 参数详细说明

#### 必需参数 (3个)

| 参数名 | 类型 | 描述 | 示例 |
|--------|------|------|------|
| `read_cash` | `float/int` | 初始化资金额度 | `1000000` |
| `buy_factors` | `List[Dict]` | 买入因子策略序列 | `[{'xd': 60, 'class': AbuFactorBuyBreak}]` |
| `sell_factors` | `List[Dict]` | 卖出因子序列 | `[{'stop_loss_n': 0.5, 'stop_win_n': 3.0, 'class': AbuFactorAtrNStop}]` |

#### 可选参数 (6个)

| 参数名 | 类型 | 默认值 | 描述 |
|--------|------|--------|------|
| `stock_picks` | `List[Dict] \| None` | `None` | 选股因子序列 |
| `choice_symbols` | `List[str] \| None` | `None` | 备选股票池(None为全市场回测) |
| `n_folds` | `int` | `2` | 回测n_folds年的历史数据 |
| `start` | `str \| None` | `None` | 回测开始时间 |
| `end` | `str \| None` | `None` | 回测结束时间 |
| `commission_dict` | `Dict \| None` | `None` | 自定义交易手续费 |

---

## 2. 买入因子(buy_factors)格式要求

### 2.1 数据结构

**类型**: `List[Dict]`  
**描述**: 买入因子字典列表，每个字典代表一个买入策略

### 2.2 字典键值要求

#### 必需键

| 键名 | 类型 | 描述 | 示例 |
|------|------|------|------|
| `class` | `class object` | 买入因子类对象 | `AbuFactorBuyBreak` |

#### 可选键

| 键名 | 类型 | 描述 | 格式选项 |
|------|------|------|----------|
| `position` | `Dict \| class object` | 仓位管理配置 | 直接传入类 或 字典格式 |
| `sell_factors` | `List[Dict]` | 专属卖出因子 | 只对当前买入因子生效 |
| `slippage` | `class object` | 滑点类配置 | 默认: `AbuSlippageBuyMean` |

#### 因子特定参数

因子特定参数(如`xd`, `past_factor`等)直接作为字典的键值对传递。

### 2.3 完整示例

```python
# 基础买入因子
{'xd': 60, 'class': AbuFactorBuyBreak}

# 带仓位管理的买入因子
{
    'xd': 42, 
    'class': AbuFactorBuyBreak, 
    'position': {
        'class': AbuAtrPosition, 
        'atr_pos_base': 0.15
    }
}

# 带专属卖出因子的买入因子
{
    'xd': 30, 
    'class': AbuFactorBuyBreak, 
    'sell_factors': [
        {
            'class': AbuFactorSellBreak, 
            'xd': 20
        }
    ]
}
```

---

## 3. 卖出因子(sell_factors)格式要求

### 3.1 数据结构

**类型**: `List[Dict]`  
**描述**: 全局卖出因子字典列表，对所有买入因子生效

### 3.2 字典键值要求

#### 必需键

| 键名 | 类型 | 描述 | 示例 |
|------|------|------|------|
| `class` | `class object` | 卖出因子类对象 | `AbuFactorAtrNStop` |

#### 可选键

| 键名 | 类型 | 描述 | 默认值 |
|------|------|------|--------|
| `slippage` | `class object` | 滑点类配置 | `AbuSlippageSellMean` |

### 3.3 可用卖出因子

| 类名 | 描述 | 参数 | 示例 |
|------|------|------|------|
| `AbuFactorAtrNStop` | ATR动态止盈止损 | `stop_loss_n`, `stop_win_n` | `{'stop_loss_n': 0.5, 'stop_win_n': 3.0, 'class': AbuFactorAtrNStop}` |
| `AbuFactorPreAtrNStop` | 单日跌幅止损 | `pre_atr_n` | `{'pre_atr_n': 1.0, 'class': AbuFactorPreAtrNStop}` |
| `AbuFactorCloseAtrNStop` | 利润保护止盈 | `close_atr_n` | `{'close_atr_n': 1.5, 'class': AbuFactorCloseAtrNStop}` |
| `AbuFactorSellBreak` | 向下突破卖出 | `xd` | `{'xd': 120, 'class': AbuFactorSellBreak}` |
| `AbuFactorSellNDay` | N日持有卖出 | `sell_n`, `is_sell_today` | `{'sell_n': 30, 'class': AbuFactorSellNDay}` |
| `AbuDoubleMaSell` | 双均线死叉卖出 | `fast`, `slow` | `{'fast': 5, 'slow': 60, 'class': AbuDoubleMaSell}` |

---

## 4. 仓位管理配置方式

### 4.1 全局配置方式

**方法**: 设置全局变量  
**描述**: 全局仓位管理配置，影响所有买入因子

#### 全局变量

| 变量名 | 类型 | 描述 | 默认值 |
|--------|------|------|--------|
| `ABuPositionBase.g_default_pos_class` | `Dict \| None` | 全局默认仓位管理类 | `None` |
| `ABuPositionBase.g_pos_max` | `float` | 每笔交易最大仓位比例 | `0.75 (75%)` |
| `ABuPositionBase.g_deposit_rate` | `float` | 保证金最小比例 | `1.0 (不使用融资)` |

#### 设置示例

```python
# 设置全局默认仓位管理类
ABuPositionBase.g_default_pos_class = {
    'class': AbuAtrPosition, 
    'atr_pos_base': 0.1
}

# 设置全局最大仓位比例
ABuPositionBase.g_pos_max = 0.5  # 50%
```

### 4.2 因子专属配置方式

**方法**: 在买入因子字典中添加`position`键  
**描述**: 买入因子专属仓位管理配置

#### 格式选项

```python
# 1. 直接传入类
{
    'class': AbuFactorBuyBreak, 
    'xd': 60, 
    'position': AbuAtrPosition
}

# 2. 字典格式(推荐)
{
    'class': AbuFactorBuyBreak, 
    'xd': 60, 
    'position': {
        'class': AbuAtrPosition, 
        'atr_pos_base': 0.05
    }
}
```

### 4.3 可用仓位管理类

| 类名 | 描述 | 参数 | 默认仓位 |
|------|------|------|----------|
| `AbuAtrPosition` | ATR仓位管理类(默认) | `atr_pos_base`, `atr_base_price`, `std_atr_threshold` | 10% |
| `AbuKellyPosition` | Kelly公式仓位管理类 | `win_rate`, `gains_mean`, `losses_mean` | 动态计算 |
| `AbuPtPosition` | 价格位置仓位管理类 | `pos_base`, `past_day_cnt`, `mid_precent` | 10% |

---

## 5. UMP裁判系统配置

### 5.1 系统描述

**功能**: UMP(Umpire)裁判系统用于风险控制，可以拦截不符合条件的交易信号  
**配置方式**: 通过全局变量和管理器配置，**不是**通过回测函数参数传递

### 5.2 全局开关

| 变量名 | 类型 | 描述 | 默认值 |
|--------|------|------|--------|
| `ABuUmpManager.g_enable_user_ump` | `bool` | 是否开启用户自定义裁判 | `False` |
| `ABuEnv.g_enable_ml_feature` | `bool` | 是否开启特征记录功能(裁判系统依赖) | `False` |

### 5.3 内置裁判开关

| 变量名 | 描述 |
|--------|------|
| `ABuEnv.g_enable_ump_main_deg_block` | 主裁判-角度拦截 |
| `ABuEnv.g_enable_ump_main_jump_block` | 主裁判-跳空拦截 |
| `ABuEnv.g_enable_ump_main_price_block` | 主裁判-价格拦截 |
| `ABuEnv.g_enable_ump_main_wave_block` | 主裁判-波动拦截 |

### 5.4 自定义裁判使用步骤

```python
# 1. 开启全局开关
ABuUmpManager.g_enable_user_ump = True

# 2. 开启特征记录
ABuEnv.g_enable_ml_feature = True

# 3. 清空之前设置
ABuUmpManager.clear_user_ump()

# 4. 实例化裁判
ump = AbuUmpMainDeg(predict=True, market_name='my_ump')

# 5. 添加到管理器
ABuUmpManager.append_user_ump(ump)
```

---

## 6. 完整回测示例

### 6.1 基础回测示例

```python
import abupy
from abupy import AbuFactorBuyBreak, AbuFactorAtrNStop, AbuFactorPreAtrNStop

# 1. 基本参数
read_cash = 1000000

# 2. 买入因子配置
buy_factors = [
    {
        'xd': 60, 
        'class': AbuFactorBuyBreak
    },
    {
        'xd': 42, 
        'class': AbuFactorBuyBreak
    }
]

# 3. 卖出因子配置
sell_factors = [
    {
        'stop_loss_n': 0.5, 
        'stop_win_n': 3.0, 
        'class': AbuFactorAtrNStop
    },
    {
        'pre_atr_n': 1.0, 
        'class': AbuFactorPreAtrNStop
    }
]

# 4. 执行回测
abu_result_tuple, kl_pd_manager = abupy.run_loop_back(
    read_cash=read_cash,
    buy_factors=buy_factors,
    sell_factors=sell_factors,
    choice_symbols=['usTSLA', 'usAAPL', 'usGOOG'],
    start='2020-01-01',
    end='2023-12-31'
)
```

### 6.2 高级配置示例

```python
# 带仓位管理和专属卖出因子的完整配置
buy_factors = [
    {
        'xd': 60,
        'class': AbuFactorBuyBreak,
        'position': {
            'class': AbuAtrPosition,
            'atr_pos_base': 0.15
        },
        'sell_factors': [
            {
                'class': AbuFactorSellBreak,
                'xd': 30
            }
        ]
    }
]

# 全局卖出因子
sell_factors = [
    {
        'stop_loss_n': 0.5,
        'stop_win_n': 3.0,
        'class': AbuFactorAtrNStop
    }
]

# 配置UMP裁判系统
ABuUmpManager.g_enable_user_ump = True
ABuEnv.g_enable_ml_feature = True

# 执行回测
abu_result_tuple, kl_pd_manager = abupy.run_loop_back(
    read_cash=1000000,
    buy_factors=buy_factors,
    sell_factors=sell_factors
)
```

---

## 7. 关键技术要点

### 7.1 数据结构要点

1. **字典格式**: 所有因子都使用字典格式，必须包含'class'键
2. **参数传递**: 因子特定参数直接作为字典的键值对传递
3. **嵌套支持**: position支持类对象或字典两种格式
4. **作用域**: sell_factors分为全局和专属两种作用域

### 7.2 配置优先级

1. **仓位管理**: 因子专属配置 > 全局配置 > 默认配置
2. **卖出因子**: 全局卖出因子 + 专属卖出因子同时生效
3. **风控系统**: UMP裁判系统独立于回测参数，通过全局变量配置

### 7.3 源码验证位置

| 功能 | 源码位置 | 关键方法/变量 |
|------|----------|---------------|
| 回测入口 | `abupy/CoreBu/ABu.py` 第27-32行 | `run_loop_back()` |
| 买入因子处理 | `abupy/FactorBuyBu/ABuFactorBuyBase.py` 第124-140行 | `_position_class_init()` |
| 仓位管理全局变量 | `abupy/BetaBu/ABuPositionBase.py` 第15-30行 | `g_pos_max`, `g_default_pos_class` |
| UMP裁判系统 | `abupy/CoreBu/ABuEnv.py` 第462-469行 | `g_enable_ump_*` |

---

## 8. 结论

### 8.1 准确性验证

✅ **所有结论均基于abupy源码实现**  
✅ **无推测或假设成分**  
✅ **提供具体的源码位置和行号**  
✅ **通过实际代码示例验证**

### 8.2 关键发现

1. **回测系统有3个必需参数**: read_cash, buy_factors, sell_factors
2. **买入因子支持嵌套position字典和专属sell_factors**
3. **仓位管理可通过全局变量或因子专属配置**
4. **UMP裁判系统通过全局变量配置，不是函数参数**
5. **所有因子都使用字典格式，必须包含'class'键**

### 8.3 对abu_modern的指导意义

本报告为abu_modern项目的策略配置和回测执行提供了准确的技术依据，确保前端策略工场的数据结构设计与abupy框架完全兼容。
