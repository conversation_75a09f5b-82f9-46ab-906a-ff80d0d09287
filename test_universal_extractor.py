#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试通用abupy参数提取器在不同环境中的工作情况
"""

import sys
import os
from universal_abupy_param_extractor import UniversalAbuFactorParamExtractor

def test_extractor():
    """测试参数提取器"""
    
    print("=== 测试通用abupy参数提取器 ===\n")
    
    # 创建提取器实例
    extractor = UniversalAbuFactorParamExtractor()
    
    # 检查环境检测
    print("🔍 环境检测结果:")
    print(f"- abupy路径: {extractor.abupy_path}")
    print(f"- abupy可导入: {extractor.abupy_available}")
    
    if not extractor.abupy_path:
        print("❌ 无法找到abupy，测试终止")
        return False
    
    # 测试因子结构提取
    print(f"\n📊 开始提取因子结构...")
    factor_structure = extractor.extract_complete_factor_structure()
    
    if not factor_structure:
        print("❌ 因子结构提取失败")
        return False
    
    # 验证提取结果
    print(f"\n✅ 提取成功！统计结果:")
    
    buy_factors = factor_structure.get('buy_factors', {})
    sell_factors = factor_structure.get('sell_factors', {})
    position_classes = factor_structure.get('position_classes', {})
    stock_pick_classes = factor_structure.get('stock_pick_classes', {})
    umpire_params = factor_structure.get('umpire_params', {})
    
    print(f"- 买入因子: {len(buy_factors)}个")
    print(f"- 卖出因子: {len(sell_factors)}个")
    print(f"- 仓位管理类: {len(position_classes)}个")
    print(f"- 选股因子: {len(stock_pick_classes)}个")
    
    if umpire_params:
        switches = len(umpire_params.get('global_switches', {}).get('parameters', []))
        ml_features = sum(len(features) for features in umpire_params.get('ml_features', {}).get('parameters', {}).values())
        print(f"- UmpBu全局开关: {switches}个")
        print(f"- UmpBu ML特征: {ml_features}个")
    
    # 测试API Schema生成
    print(f"\n🔧 测试API Schema生成...")
    try:
        api_schema = extractor.generate_api_schema(factor_structure)
        if api_schema and 'StrategyCreate' in api_schema:
            print(f"✅ API Schema生成成功")
            
            # 检查关键字段
            strategy_create = api_schema['StrategyCreate']
            properties = strategy_create.get('properties', {})
            
            required_fields = ['name', 'read_cash', 'buy_factors', 'sell_factors']
            missing_fields = [field for field in required_fields if field not in properties]
            
            if missing_fields:
                print(f"⚠️ 缺少必需字段: {missing_fields}")
            else:
                print(f"✅ 所有必需字段都存在")
                
        else:
            print(f"❌ API Schema生成失败")
            return False
            
    except Exception as e:
        print(f"❌ API Schema生成出错: {e}")
        return False
    
    # 测试参数数量验证
    print(f"\n🎯 验证参数数量...")
    
    # 统计所有参数
    total_factor_params = 0
    for factor_info in buy_factors.values():
        total_factor_params += len(factor_info.get('parameters', {}))
    for factor_info in sell_factors.values():
        total_factor_params += len(factor_info.get('parameters', {}))
    
    total_position_params = 0
    for pos_info in position_classes.values():
        total_position_params += len(pos_info.get('parameters', {}))
    
    total_pick_params = 0
    for pick_info in stock_pick_classes.values():
        total_pick_params += len(pick_info.get('parameters', {}))
    
    total_umpire_params = 0
    if umpire_params:
        total_umpire_params += len(umpire_params.get('global_switches', {}).get('parameters', []))
        total_umpire_params += sum(len(features) for features in umpire_params.get('ml_features', {}).get('parameters', {}).values())
    
    total_params = total_factor_params + total_position_params + total_pick_params + total_umpire_params
    
    print(f"- 因子参数: {total_factor_params}个")
    print(f"- 仓位管理参数: {total_position_params}个") 
    print(f"- 选股参数: {total_pick_params}个")
    print(f"- UmpBu参数: {total_umpire_params}个")
    print(f"- 总参数数: {total_params}个")
    
    # 与预期结果对比
    expected_total = 57  # 基于修正验证的结果
    if abs(total_params - expected_total) <= 5:  # 允许小幅差异
        print(f"✅ 参数数量符合预期（预期约{expected_total}个）")
    else:
        print(f"⚠️ 参数数量与预期有较大差异（预期约{expected_total}个）")
    
    print(f"\n🎉 测试完成！")
    return True

def test_different_environments():
    """测试不同环境下的适应性"""
    
    print(f"\n=== 环境适应性测试 ===")
    
    # 测试当前Python环境
    print(f"📍 当前Python环境:")
    print(f"- Python版本: {sys.version}")
    print(f"- Python路径: {sys.executable}")
    print(f"- 工作目录: {os.getcwd()}")
    
    # 测试sys.path中是否有abupy
    abupy_in_path = False
    for path in sys.path:
        abupy_path = os.path.join(path, 'abupy')
        if os.path.exists(abupy_path):
            print(f"✅ 在sys.path中找到abupy: {abupy_path}")
            abupy_in_path = True
            break
    
    if not abupy_in_path:
        print(f"⚠️ sys.path中未找到abupy，将使用当前目录")
    
    # 测试导入能力
    try:
        import abupy
        print(f"✅ 可以导入abupy模块: {abupy.__file__}")
    except ImportError:
        print(f"⚠️ 无法导入abupy模块，将使用静态分析")
    
    return True

if __name__ == '__main__':
    print("🚀 开始测试通用abupy参数提取器\n")
    
    # 测试环境适应性
    test_different_environments()
    
    # 测试参数提取功能
    success = test_extractor()
    
    if success:
        print(f"\n🎯 总结:")
        print(f"✅ 通用参数提取器工作正常")
        print(f"✅ 支持环境自动检测")
        print(f"✅ 支持动态导入和静态分析")
        print(f"✅ 可以在不同环境中使用")
    else:
        print(f"\n❌ 测试失败，请检查环境配置")
