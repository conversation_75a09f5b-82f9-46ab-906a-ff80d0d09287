{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# ABU量化系统使用文档 \n", "\n", "<center>\n", "        <img src=\"./image/abu_logo.png\" alt=\"\" style=\"vertical-align:middle;padding:10px 20px;\"><font size=\"6\" color=\"black\"><b>第0节 abupy量化环境</b></font>\n", "</center>\n", "\n", "-----------------\n", "\n", "作者: 阿布\n", "\n", "abu能够帮助用户自动完善策略，主动分析策略产生的交易行为，智能拦截策略生成的容易失败的交易单。\n", "\n", "现阶段的量化策略还是人工编写的代码，abu量化交易系统的设计将会向着由计算机自动实现整套流程的方向迈进，包括编写量化策略本身。\n", "\n", "我们对未来的期望是：abupy用户只需要提供一些简单的种子策略，计算机在这些种子基础上不断自我学习、自我成长，创造出新的策略，并且随着时间序列数据不断智能调整策略的参数。\n", "\n", "### 特点\n", "\n", "* 使用多种机器学习技术智能优化策略\n", "* 在实盘中指导策略进行交易，提高策略的实盘效果，战胜市场\n", "\n", "### 支持的投资市场:\n", "\n", "* 美股，A股，港股\n", "* 期货，期权\n", "* 比特币，莱特币\n", "\n", "\n", "abupy基于python环境，支持python2和python3，首先安装最适合abupy的python环境。\n", "\n", "### 1. <PERSON><PERSON><PERSON>署\n", "\n", "很多操作系统已经内置了Python环境，比如Ubuntu、Centos、Mac OS，这些系统的很多功能都依赖于Python的某个版本，如果自己编写程序所使用的Python版本或Python库版本不一致时，就需要升级或着降级版本，在升级或降级后导致的不兼容问题数不胜数。为了不污染系统运行的Python环境，在这里建议使用Anaconda来管理开发的Python环境。Anaconda所建立的Python环境与系统的Python环境完全是隔离的，而且Anaconda还可以创建多套Python环境，这样就保证了开发环境和系统环境互相独立。 除了Anaconda之外，还有virtualenv等流行的开发环境管理器。Anaconda的优势在于简单的安装和集成了几乎所有的科学计算库，同时支持Linux、Mac OS、Windows主流平台。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["anaconda4.4.0百度云盘地址下载：\n", "\n", "[百度云盘下载地址 mac系统](https://pan.baidu.com/s/1c2phKFQ) 提取秘密: wp4a\n", "\n", "[百度云盘下载地址 windows系统](https://pan.baidu.com/s/1slqJgxJ) 提取秘密: 5vbk\n", "\n", "也可以使用镜像下载地址，**推荐下载anaconda4.4.0版本**\n", "\n", "[Anaconda镜像下载地址](https://mirrors.tuna.tsinghua.edu.cn/anaconda/archive/)\n", "\n", "根据所使用的操作系统下对应的版本，以及对应的python版本，abupy支持python2和python3，建议使用python3，因为python3上数据存贮空间会占的比较小。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["* Mac OS 安装\n", "\n", "MacOS提供了两种安装程序：一种是dmg格式的安装程序，就是带图形化的版本，提供了图形化的安装和管理，但是图形化的管理程序经常出现卡死的情况，因此不建议使用，另一种是直接下载sh格式命令行的安装程序，打开终端输入：\n", "\n", "    $ bash ~/Downloads/Anaconda2-4.2.0-MacOSX-x86_64.sh\n", "    \n", "* Windows安装\n", "\n", "    双击Anaconda安装程序，并按照提示安装到默认路径\n", "    \n", "安装完成之后，就拥有abupy中所使用的所以第三方库。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2. abu量化系统"]}, {"cell_type": "markdown", "metadata": {}, "source": ["[abu量化系统github地址(欢迎+star!)](https://github.com/bbfamily/abu) \n", "\n", "推荐从githubxi直接clone或者下载源代码，github上除了abupy源代码外，还有abupy使用文档说明和丰富的使用示例，以及《量化交易之路》中的所有章节源代码。\n", "\n", "\n", "也可通过pip命令下载abupy进行安装，但是没有github上相关例子及文档, pip安装命令如下：\n", "\n", "    pip install abupy\n", "\n", "注意：\n", "\n", "1. 不是必需使用pip安装abupy，相反推荐直接clone或者下载github上的工程代码，直接在工程代码下运行书中的示例以及教程 \n", "2. 下载完成github上的工程代码后并不需要运行setup.py，可以直接打开notebook运行示例或ui界面操作\n", "3. 下载完成github上的代码后请放置在非中文路径下运行"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3. 非编程界面操作\n", "\n", "对于不熟悉编程的使用者，可使用https://github.com/bbfamily/abu/tree/master/abupy_ui\n", "下的ui界面量化操作：\n", "\n", "具体使用示例请参阅：[非编程界面操作演示](https://github.com/bbfamily/abu/blob/master/abupy_ui/readme.md)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4. abu量化教程\n", "\n", "量化技术策略示例以及系统使用请参阅：[量化教程](https://github.com/bbfamily/abu/tree/master/abupy_lecture)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 5. abu量化文档目录章节\n", "\n", "[本节ipython notebook:](https://github.com/bbfamily/abu/abupy_lecture)\n", "\n", "1. [择时策略的开发](http://www.abuquant.com/lecture/lecture_1.html)\n", "2. [择时策略的优化](http://www.abuquant.com/lecture/lecture_2.html)\n", "3. [滑点策略与交易手续费](http://www.abuquant.com/lecture/lecture_3.html)\n", "4. [多支股票择时回测与仓位管理](http://www.abuquant.com/lecture/lecture_4.html)\n", "5. [选股策略的开发](http://www.abuquant.com/lecture/lecture_5.html)\n", "6. [回测结果的度量](http://www.abuquant.com/lecture/lecture_6.html)\n", "7. [寻找策略最优参数和评分](http://www.abuquant.com/lecture/lecture_7.html)\n", "8. [A股市场的回测](http://www.abuquant.com/lecture/lecture_8.html)\n", "9. [港股市场的回测](http://www.abuquant.com/lecture/lecture_9.html)\n", "10. [比特币，莱特币的回测](http://www.abuquant.com/lecture/lecture_10.html)\n", "11. [期货市场的回测](http://www.abuquant.com/lecture/lecture_11.html)\n", "12. [机器学习与比特币示例](http://www.abuquant.com/lecture/lecture_12.html)\n", "13. [量化技术分析应用](http://www.abuquant.com/lecture/lecture_13.html)\n", "14. [量化相关性分析应用](http://www.abuquant.com/lecture/lecture_14.html)\n", "15. [量化交易和搜索引擎](http://www.abuquant.com/lecture/lecture_15.html)\n", "16. [UMP主裁交易决策](http://www.abuquant.com/lecture/lecture_16.html)\n", "17. [UMP边裁交易决策](http://www.abuquant.com/lecture/lecture_17.html)\n", "18. [自定义裁判决策交易](http://www.abuquant.com/lecture/lecture_18.html)\n", "19. [数据源](http://www.abuquant.com/lecture/lecture_19.html)\n", "20. [A股全市场回测](http://www.abuquant.com/lecture/lecture_20.html)\n", "21. [A股UMP决策](http://www.abuquant.com/lecture/lecture_21.html)\n", "22. [美股全市场回测](http://www.abuquant.com/lecture/lecture_22.html)\n", "23. [美股UMP决策](http://www.abuquant.com/lecture/lecture_23.html)\n", "\n", "\n", "abu量化系统文档教程持续更新中，请关注公众号中的更新提醒。\n", "\n", "\n", "### 6. 《量化交易之路》目录章节及随书代码地址\n", "\n", "1. [第二章 量化语言——Python](https://github.com/bbfamily/abu/tree/master/ipython/第二章-量化语言——Python.ipynb)\n", "2. [第三章 量化工具——NumPy](https://github.com/bbfamily/abu/tree/master/ipython/第三章-量化工具——NumPy.ipynb)\n", "3. [第四章 量化工具——pandas](https://github.com/bbfamily/abu/tree/master/ipython/第四章-量化工具——pandas.ipynb)\n", "4. [第五章 量化工具——可视化](https://github.com/bbfamily/abu/tree/master/ipython/第五章-量化工具——可视化.ipynb)\n", "5. [第六章 量化工具——数学：你一生的追求到底能带来多少幸福](https://github.com/bbfamily/abu/tree/master/ipython/第六章-量化工具——数学.ipynb)\n", "6. [第七章 量化系统——入门：三只小猪股票投资的故事](https://github.com/bbfamily/abu/tree/master/ipython/第七章-量化系统——入门.ipynb)\n", "7. [第八章 量化系统——开发](https://github.com/bbfamily/abu/tree/master/ipython/第八章-量化系统——开发.ipynb)\n", "8. [第九章 量化系统——度量与优化](https://github.com/bbfamily/abu/tree/master/ipython/第九章-量化系统——度量与优化.ipynb)\n", "9. [第十章 量化系统——机器学习•猪老三](https://github.com/bbfamily/abu/tree/master/ipython/第十章-量化系统——机器学习•猪老三.ipynb)\n", "10. [第十一章 量化系统——机器学习•ABU](https://github.com/bbfamily/abu/tree/master/ipython/第十一章-量化系统——机器学习•ABU.ipynb)\n", "11. [附录A 量化环境部署](https://github.com/bbfamily/abu/tree/master/ipython/附录A-量化环境部署.ipynb)\n", "12. [附录B 量化相关性分析](https://github.com/bbfamily/abu/tree/master/ipython/附录B-量化相关性分析.ipynb)\n", "13. [附录C 量化统计分析及指标应用](https://github.com/bbfamily/abu/tree/master/ipython/附录C-量化统计分析及指标应用.ipynb)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["更多关于量化交易相关请阅读[《量化交易之路》](http://www.abuquant.com/books/quantify-trading-road.html)\n", "\n", "更多关于量化交易与机器学习相关请阅读[《机器学习之路》](http://www.abuquant.com/books/machine-learning-road.html))\n", "\n", "更多关于abu量化系统请关注微信公众号: abu_quant\n", "\n", "![](./image/qrcode.jpg)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.2"}}, "nbformat": 4, "nbformat_minor": 2}