{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# ABU量化系统使用文档 \n", "\n", "<center>\n", "        <img src=\"./image/abu_logo.png\" alt=\"\" style=\"vertical-align:middle;padding:10px 20px;\"><font size=\"6\" color=\"black\"><b>第16节 UMP主裁交易决策</b></font>\n", "</center>\n", "\n", "-----------------\n", "\n", "\n", "作者: 阿布\n", "\n", "阿布量化版权所有 未经允许 禁止转载\n", "\n", "[abu量化系统github地址](https://github.com/bbfamily/abu) (欢迎+star)\n", "\n", "[本节ipython notebook](https://github.com/bbfamily/abu/tree/master/abupy_lecture)\n", "\n", "上一节示例了ump角度主裁的训练分解步骤，对任何比赛一个裁判是远远不够的，本节将训练更多的裁判, 首先导入abupy中本节使用的模块："]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["enable example env will only read RomDataBu/df_kl.h5\n"]}], "source": ["# 基础库导入\n", "\n", "from __future__ import print_function\n", "from __future__ import division\n", "\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "warnings.simplefilter('ignore')\n", "\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "%matplotlib inline\n", "\n", "import os\n", "import sys\n", "# 使用insert 0即只使用github，避免交叉使用了pip安装的abupy，导致的版本不一致问题\n", "sys.path.insert(0, os.path.abspath('../'))\n", "import abupy\n", "\n", "# 使用沙盒数据，目的是和书中一样的数据环境\n", "abupy.env.enable_example_env_ipython()"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"collapsed": true}, "outputs": [], "source": ["from abupy import AbuFactorAtrNStop, AbuFactorPreAtrNStop, AbuFactorCloseAtrNStop, AbuFactorBuyBreak, ABuProgress\n", "from abupy import abu, EMarketTargetType, AbuMetricsBase, ABuMarketDrawing, AbuFuturesCn, ABuSymbolPd, AbuOrderPdProxy\n", "from abupy import AbuUmpMainDeg, AbuUmpMainJump, AbuUmpMainPrice, AbuUmpMainWave, AbuFuturesCn, EStoreAbu, AbuML\n", "from abupy import AbuUmpEdgeDeg, AbuUmpEdgePrice, AbuUmpEdgeWave, AbuUmpEdgeMul"]}, {"cell_type": "markdown", "metadata": {}, "source": ["受限于沙盒中数据限制，本节示例的相关性分析只限制在abupy内置沙盒数据中，完整示例以及代码请阅读《量化交易之路》中相关章节。\n", "\n", "和上一节一样首先将内置沙盒中美股，A股，港股, 比特币，莱特币，期货市场中的symbol都列出来，然后组成训练集和测试集，买入卖出因子等相同设置:"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"collapsed": true}, "outputs": [], "source": ["us_choice_symbols = ['usTSLA', 'usNOAH', 'usSFUN', 'usBIDU', 'usAAPL', 'usGOOG', 'usWUBA', 'usVIPS']\n", "cn_choice_symbols = ['002230', '300104', '300059', '601766', '600085', '600036', '600809', '000002', '002594']\n", "hk_choice_symbols = ['hk03333', 'hk00700', 'hk02333', 'hk01359', 'hk00656', 'hk03888', 'hk02318']\n", "tc_choice_symbols = ['btc', 'ltc']\n", "# 期货市场的直接从AbuFuturesCn().symbo中读取\n", "ft_choice_symbols = AbuFuturesCn().symbol.tolist()\n", "\n", "# 训练集：沙盒中所有美股 ＋ 沙盒中所有A股 ＋ 沙盒中所有港股 ＋ 比特币\n", "train_choice_symbols = us_choice_symbols + cn_choice_symbols +  hk_choice_symbols + tc_choice_symbols[:1]\n", "# 测试集：沙盒中所有期货 ＋ 莱特币\n", "test_choice_symbols = ft_choice_symbols + tc_choice_symbols[1:]\n", "\n", "# 设置初始资金数\n", "read_cash = 1000000\n", "# 买入因子依然延用向上突破因子\n", "buy_factors = [{'xd': 60, 'class': AbuFactorBuyBreak},\n", "               {'xd': 42, 'class': AbuFactorBuyBreak}]\n", "\n", "# 卖出因子继续使用上一节使用的因子\n", "sell_factors = [\n", "    {'stop_loss_n': 1.0, 'stop_win_n': 3.0,\n", "     'class': AbuFactorAtrNStop},\n", "    {'class': AbuFactorPreAtrNStop, 'pre_atr_n': 1.5},\n", "    {'class': AbuFactorCloseAtr<PERSON><PERSON>, 'close_atr_n': 1.5}\n", "]\n", "# 回测生成买入时刻特征\n", "abupy.env.g_enable_ml_feature = True"]}, {"cell_type": "markdown", "metadata": {}, "source": ["在运行完成第15节中相关内容后，使用load_abu_result_tuple读取上一节保存在本地的训练集数据："]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["买入后卖出的交易数量:196\n", "买入后尚未卖出的交易数量:9\n", "胜率:59.6939%\n", "平均获利期望:18.6899%\n", "平均亏损期望:-7.1235%\n", "盈亏比:4.4972\n", "所有交易收益比例和:16.2396 \n", "所有交易总盈亏和:2717948.4900 \n"]}, {"name": "stdout", "output_type": "stream", "text": ["please wait! load_pickle....: /Users/<USER>/abu/data/cache/n2_lecture_train_capital\n", "please wait! load_pickle....: /Users/<USER>/abu/data/cache/n2_lecture_train_benchmark\n"]}, {"data": {"text/plain": ["<abupy.MetricsBu.ABuMetricsBase.AbuMetricsBase at 0x10e90b5c0>"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["abu_result_tuple_train = abu.load_abu_result_tuple(n_folds=2, store_type=EStoreAbu.E_STORE_CUSTOM_NAME, \n", "                                             custom_name='lecture_train')\n", "orders_pd_train = abu_result_tuple_train.orders_pd\n", "AbuMetricsBase.show_general(*abu_result_tuple_train, returns_cmp=True, only_info=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1. 跳空主裁\n", "\n", "上一节训练角度主裁的过程比较繁琐，实际上只是为了分步演示实现步骤，本节将使用类方法ump_main_clf_dump直接完成gmm分类，分类簇最优参数，分类簇筛选，本地保存分类器等操作, 由于本节示例使用的沙盒数据交易量太少，所以效果不会很好，本节示例的目的只是以最少量的数据，便于快速运行，快速实现，理解ump主裁的使用，20节之后示例使用ump训练全市场的交易，可以看到ump在回测中将会大大提升回测效果。\n", "\n", "即实现上一节训练角度主裁的所有的操作实际上只需要运行下面这一行代码即可："]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["pid:56312 gmm fit:100.0%\n", "pid:56312 done!\n", "please wait! dump_pickle....: /Users/<USER>/abu/data/ump/ump_main_us_deg_main\n"]}], "source": ["_ = AbuUmpMainDeg.ump_main_clf_dump(orders_pd_train, p_ncs=slice(20, 40, 1))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["下面我们通过ump_main_clf_dump()函数对AbuUmpMainJump完成跳空裁判的训练保存等一系列工作："]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["pid:56312 gmm fit:100.0%\n", "pid:56312 done!\n", "please wait! dump_pickle....: /Users/<USER>/abu/data/ump/ump_main_us_jump_main\n"]}, {"data": {"text/html": ["<div>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>result</th>\n", "      <th>buy_jump_up_power</th>\n", "      <th>buy_diff_down_days</th>\n", "      <th>buy_jump_down_power</th>\n", "      <th>buy_diff_up_days</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2014-09-25</th>\n", "      <td>1</td>\n", "      <td>0.000</td>\n", "      <td>77</td>\n", "      <td>-1.369</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-10-09</th>\n", "      <td>0</td>\n", "      <td>0.000</td>\n", "      <td>0</td>\n", "      <td>0.000</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-10-17</th>\n", "      <td>1</td>\n", "      <td>1.778</td>\n", "      <td>0</td>\n", "      <td>0.000</td>\n", "      <td>41</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-10-24</th>\n", "      <td>1</td>\n", "      <td>1.038</td>\n", "      <td>136</td>\n", "      <td>-13.570</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-10-24</th>\n", "      <td>1</td>\n", "      <td>1.038</td>\n", "      <td>136</td>\n", "      <td>-13.570</td>\n", "      <td>2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            result  buy_jump_up_power  buy_diff_down_days  \\\n", "2014-09-25       1              0.000                  77   \n", "2014-10-09       0              0.000                   0   \n", "2014-10-17       1              1.778                   0   \n", "2014-10-24       1              1.038                 136   \n", "2014-10-24       1              1.038                 136   \n", "\n", "            buy_jump_down_power  buy_diff_up_days  \n", "2014-09-25               -1.369                 0  \n", "2014-10-09                0.000                 0  \n", "2014-10-17                0.000                41  \n", "2014-10-24              -13.570                 2  \n", "2014-10-24              -13.570                 2  "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["ump_jump = AbuUmpMainJump.ump_main_clf_dump(orders_pd_train, p_ncs=slice(20, 40, 1))\n", "ump_jump.fiter.df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["上面通过ump_main_clf_dump()已经完成训练，筛选，本地保存分类器等工作，我们下面还是针对AbuUmpMainJump寻找宏观上合理的分类簇拦截交易解释。\n", "AbuUmpMainJump中训练特征为：\n", "\n", "* diff_up_days：距离这次交易最近一次向上跳空买入日期时间间隔\n", "* diff_down_days：距离这次交易最近一次向下跳空买入日期时间间隔\n", "* ump_up_power： 距离这次交易最近一次向上跳空能量\n", "* jump_down_power： 距离这次交易最近一次向下跳空能量\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2. 价格主裁\n", "\n", "依然使用ump_main_clf_dump()函数完成价格主裁的训练，最优，保存等工作："]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["pid:56312 gmm fit:100.0%\n", "pid:56312 done!\n", "please wait! dump_pickle....: /Users/<USER>/abu/data/ump/ump_main_us_price_main\n"]}, {"data": {"text/html": ["<div>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>result</th>\n", "      <th>buy_price_rank120</th>\n", "      <th>buy_price_rank90</th>\n", "      <th>buy_price_rank60</th>\n", "      <th>buy_price_rank252</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2014-09-25</th>\n", "      <td>1</td>\n", "      <td>1.000</td>\n", "      <td>1.000</td>\n", "      <td>1.000</td>\n", "      <td>0.857</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-10-09</th>\n", "      <td>0</td>\n", "      <td>0.992</td>\n", "      <td>0.989</td>\n", "      <td>0.983</td>\n", "      <td>0.798</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-10-17</th>\n", "      <td>1</td>\n", "      <td>1.000</td>\n", "      <td>1.000</td>\n", "      <td>1.000</td>\n", "      <td>1.000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-10-24</th>\n", "      <td>1</td>\n", "      <td>1.000</td>\n", "      <td>1.000</td>\n", "      <td>1.000</td>\n", "      <td>1.000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-10-24</th>\n", "      <td>1</td>\n", "      <td>1.000</td>\n", "      <td>1.000</td>\n", "      <td>1.000</td>\n", "      <td>1.000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            result  buy_price_rank120  buy_price_rank90  buy_price_rank60  \\\n", "2014-09-25       1              1.000             1.000             1.000   \n", "2014-10-09       0              0.992             0.989             0.983   \n", "2014-10-17       1              1.000             1.000             1.000   \n", "2014-10-24       1              1.000             1.000             1.000   \n", "2014-10-24       1              1.000             1.000             1.000   \n", "\n", "            buy_price_rank252  \n", "2014-09-25              0.857  \n", "2014-10-09              0.798  \n", "2014-10-17              1.000  \n", "2014-10-24              1.000  \n", "2014-10-24              1.000  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["ump_price = AbuUmpMainPrice.ump_main_clf_dump(orders_pd_train, p_ncs=slice(20, 40, 1))\n", "ump_price.fiter.df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["如上显示的特征为买入当天价格相对特征周期内所有价格排序的位置值，即比如特征周期为60天，那么如果买入当天价格为60天内最高，那么price_rank60=1.0；如果买入当天价格为60天内第30高价格，那么price_rank60=0.5。\n", "\n", "可以看到值普遍比较大，这是因为使用42日、60日突破作为买入信号，这表示在短周期内大概率为rank最大值，长周期内也是大概率比较大的值"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.  波动主裁\n", "\n", "依然使用ump_main_clf_dump()函数完成波动主裁的训练，最优，保存等工作："]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["pid:56312 gmm fit:100.0%\n", "pid:56312 done!\n", "please wait! dump_pickle....: /Users/<USER>/abu/data/ump/ump_main_us_wave_main\n"]}, {"data": {"text/html": ["<div>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>result</th>\n", "      <th>buy_wave_score1</th>\n", "      <th>buy_wave_score2</th>\n", "      <th>buy_wave_score3</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2014-09-25</th>\n", "      <td>1</td>\n", "      <td>0.496</td>\n", "      <td>0.454</td>\n", "      <td>0.441</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-10-09</th>\n", "      <td>0</td>\n", "      <td>0.230</td>\n", "      <td>0.072</td>\n", "      <td>-0.001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-10-17</th>\n", "      <td>1</td>\n", "      <td>0.743</td>\n", "      <td>0.374</td>\n", "      <td>0.232</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-10-24</th>\n", "      <td>1</td>\n", "      <td>1.251</td>\n", "      <td>1.290</td>\n", "      <td>1.289</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-10-24</th>\n", "      <td>1</td>\n", "      <td>1.251</td>\n", "      <td>1.290</td>\n", "      <td>1.289</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            result  buy_wave_score1  buy_wave_score2  buy_wave_score3\n", "2014-09-25       1            0.496            0.454            0.441\n", "2014-10-09       0            0.230            0.072           -0.001\n", "2014-10-17       1            0.743            0.374            0.232\n", "2014-10-24       1            1.251            1.290            1.289\n", "2014-10-24       1            1.251            1.290            1.289"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["ump_wave = AbuUmpMainWave.ump_main_clf_dump(orders_pd_train, p_ncs=slice(20, 40, 1))\n", "ump_wave.fiter.df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["显示的特征为三个周期内的价格波动率特征。\n", "\n", "回测中生成价格波动率数值特征wave_score的代码在ABuMLFeature中，详情请自行查阅源代码，也可阅读《量化交易之路》`5.3.1 绘制股票的收益，及收益波动`章节查看代码片段\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4. 验证主裁是否称职, 在abu系统中开启主裁拦截模式"]}, {"cell_type": "markdown", "metadata": {"collapsed": true}, "source": ["首先使用测试集在不使用主裁拦截情况下进行回测，如下所示："]}, {"cell_type": "code", "execution_count": 9, "metadata": {"collapsed": true}, "outputs": [], "source": ["# 不使用主裁拦截\n", "abupy.env.g_enable_ump_main_deg_block = False\n", "abupy.env.g_enable_ump_main_jump_block = False\n", "abupy.env.g_enable_ump_main_price_block = False\n", "abupy.env.g_enable_ump_main_wave_block = False\n", "\n", "abu_result_tuple_test, _ = abu.run_loop_back(read_cash,\n", "                                                   buy_factors,\n", "                                                   sell_factors,\n", "                                                   start='2014-07-26',\n", "                                                   end='2016-07-26',\n", "                                                   choice_symbols=test_choice_symbols)\n", "ABuProgress.clear_output()"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"scrolled": true}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["买入后卖出的交易数量:247\n", "买入后尚未卖出的交易数量:23\n", "胜率:41.2955%\n", "平均获利期望:9.7208%\n", "平均亏损期望:-4.8754%\n", "盈亏比:1.3725\n", "所有交易收益比例和:2.8459 \n", "所有交易总盈亏和:428259.7700 \n"]}, {"data": {"text/plain": ["<abupy.MetricsBu.ABuMetricsBase.AbuMetricsBase at 0x1224df588>"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["AbuMetricsBase.show_general(*abu_result_tuple_test, returns_cmp=True, only_info=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["同上相同参数进行测试集回测，相同的资金及策略参数等，唯一不同点在于开启主裁拦截，代码如下所示："]}, {"cell_type": "code", "execution_count": 11, "metadata": {"collapsed": true}, "outputs": [], "source": ["abupy.env.g_enable_ump_main_deg_block = True\n", "abupy.env.g_enable_ump_main_jump_block = True\n", "abupy.env.g_enable_ump_main_price_block = True\n", "abupy.env.g_enable_ump_main_wave_block = True\n", "\n", "abu_result_tuple_test_ump, _ = abu.run_loop_back(read_cash,\n", "                                                   buy_factors,\n", "                                                   sell_factors,\n", "                                                   start='2014-07-26',\n", "                                                   end='2016-07-26',\n", "                                                   choice_symbols=test_choice_symbols)\n", "ABuProgress.clear_output()"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["买入后卖出的交易数量:154\n", "买入后尚未卖出的交易数量:18\n", "胜率:44.8052%\n", "平均获利期望:10.9532%\n", "平均亏损期望:-4.1308%\n", "盈亏比:2.1145\n", "所有交易收益比例和:4.0465 \n", "所有交易总盈亏和:617759.7700 \n"]}, {"data": {"text/plain": ["<abupy.MetricsBu.ABuMetricsBase.AbuMetricsBase at 0x122486a58>"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["AbuMetricsBase.show_general(*abu_result_tuple_test_ump, returns_cmp=True, only_info=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["使用AbuOrderPdProxy可以查看被拦截的交易:"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["正确拦截失败的交易数量60, 错误拦截的交易数量33\n"]}, {"data": {"text/html": ["<div>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>buy_date</th>\n", "      <th>buy_price</th>\n", "      <th>buy_cnt</th>\n", "      <th>buy_factor</th>\n", "      <th>symbol</th>\n", "      <th>buy_pos</th>\n", "      <th>buy_type_str</th>\n", "      <th>expect_direction</th>\n", "      <th>sell_type_extra</th>\n", "      <th>sell_date</th>\n", "      <th>...</th>\n", "      <th>sell_wave_score2</th>\n", "      <th>sell_wave_score3</th>\n", "      <th>sell_diff_up_days</th>\n", "      <th>sell_jump_down_power</th>\n", "      <th>sell_jump_up_power</th>\n", "      <th>sell_diff_down_days</th>\n", "      <th>profit_cg</th>\n", "      <th>profit_cg_hunder</th>\n", "      <th>keep_days</th>\n", "      <th>difference</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2014-10-15</th>\n", "      <td>20141015</td>\n", "      <td>4744.500</td>\n", "      <td>40.0</td>\n", "      <td>AbuFactorBuyBreak:42</td>\n", "      <td>JD0</td>\n", "      <td>AbuAtrPosition</td>\n", "      <td>call</td>\n", "      <td>1.0</td>\n", "      <td>AbuFactorPreAtrNStop:pre_atr=1.5</td>\n", "      <td>20141104</td>\n", "      <td>...</td>\n", "      <td>0.521</td>\n", "      <td>0.580</td>\n", "      <td>25.0</td>\n", "      <td>-2.135</td>\n", "      <td>2.633</td>\n", "      <td>290.0</td>\n", "      <td>-0.0317</td>\n", "      <td>-3.1721</td>\n", "      <td>20</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-10-29</th>\n", "      <td>20141029</td>\n", "      <td>2393.500</td>\n", "      <td>80.0</td>\n", "      <td>AbuFactorBuyBreak:42</td>\n", "      <td>C0</td>\n", "      <td>AbuAtrPosition</td>\n", "      <td>call</td>\n", "      <td>1.0</td>\n", "      <td>AbuFactorCloseAtrNStop:close_atr_n=1.5</td>\n", "      <td>20141215</td>\n", "      <td>...</td>\n", "      <td>0.499</td>\n", "      <td>0.627</td>\n", "      <td>59.0</td>\n", "      <td>-3.602</td>\n", "      <td>1.262</td>\n", "      <td>64.0</td>\n", "      <td>0.0025</td>\n", "      <td>0.2507</td>\n", "      <td>47</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-12-31</th>\n", "      <td>20141231</td>\n", "      <td>129.775</td>\n", "      <td>1500.0</td>\n", "      <td>AbuFactorBuyBreak:60</td>\n", "      <td>BB0</td>\n", "      <td>AbuAtrPosition</td>\n", "      <td>call</td>\n", "      <td>1.0</td>\n", "      <td>AbuFactorAtrNStop:stop_loss=1.0</td>\n", "      <td>20150115</td>\n", "      <td>...</td>\n", "      <td>1.030</td>\n", "      <td>0.850</td>\n", "      <td>0.0</td>\n", "      <td>-1.557</td>\n", "      <td>0.000</td>\n", "      <td>14.0</td>\n", "      <td>-0.1179</td>\n", "      <td>-11.7896</td>\n", "      <td>15</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-12-31</th>\n", "      <td>20141231</td>\n", "      <td>129.775</td>\n", "      <td>1500.0</td>\n", "      <td>AbuFactorBuyBreak:42</td>\n", "      <td>BB0</td>\n", "      <td>AbuAtrPosition</td>\n", "      <td>call</td>\n", "      <td>1.0</td>\n", "      <td>AbuFactorAtrNStop:stop_loss=1.0</td>\n", "      <td>20150115</td>\n", "      <td>...</td>\n", "      <td>1.030</td>\n", "      <td>0.850</td>\n", "      <td>0.0</td>\n", "      <td>-1.557</td>\n", "      <td>0.000</td>\n", "      <td>14.0</td>\n", "      <td>-0.1179</td>\n", "      <td>-11.7896</td>\n", "      <td>15</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-01-21</th>\n", "      <td>20150121</td>\n", "      <td>3853.000</td>\n", "      <td>45.0</td>\n", "      <td>AbuFactorBuyBreak:42</td>\n", "      <td>AG0</td>\n", "      <td>AbuAtrPosition</td>\n", "      <td>call</td>\n", "      <td>1.0</td>\n", "      <td>AbuFactorAtrNStop:stop_loss=1.0</td>\n", "      <td>20150202</td>\n", "      <td>...</td>\n", "      <td>1.397</td>\n", "      <td>1.417</td>\n", "      <td>106.0</td>\n", "      <td>-1.304</td>\n", "      <td>1.481</td>\n", "      <td>88.0</td>\n", "      <td>-0.0628</td>\n", "      <td>-6.2808</td>\n", "      <td>12</td>\n", "      <td>True</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 51 columns</p>\n", "</div>"], "text/plain": ["            buy_date  buy_price  buy_cnt            buy_factor symbol  \\\n", "2014-10-15  20141015   4744.500     40.0  AbuFactorBuyBreak:42    JD0   \n", "2014-10-29  20141029   2393.500     80.0  AbuFactorBuyBreak:42     C0   \n", "2014-12-31  20141231    129.775   1500.0  AbuFactorBuyBreak:60    BB0   \n", "2014-12-31  20141231    129.775   1500.0  AbuFactorBuyBreak:42    BB0   \n", "2015-01-21  20150121   3853.000     45.0  AbuFactorBuyBreak:42    AG0   \n", "\n", "                   buy_pos buy_type_str  expect_direction  \\\n", "2014-10-15  AbuAtrPosition         call               1.0   \n", "2014-10-29  AbuAtrPosition         call               1.0   \n", "2014-12-31  AbuAtrPosition         call               1.0   \n", "2014-12-31  AbuAtrPosition         call               1.0   \n", "2015-01-21  AbuAtrPosition         call               1.0   \n", "\n", "                                   sell_type_extra  sell_date     ...      \\\n", "2014-10-15        AbuFactorPreAtrNStop:pre_atr=1.5   20141104     ...       \n", "2014-10-29  AbuFactorCloseAtrNStop:close_atr_n=1.5   20141215     ...       \n", "2014-12-31         AbuFactorAtrNStop:stop_loss=1.0   20150115     ...       \n", "2014-12-31         AbuFactorAtrNStop:stop_loss=1.0   20150115     ...       \n", "2015-01-21         AbuFactorAtrNStop:stop_loss=1.0   20150202     ...       \n", "\n", "            sell_wave_score2 sell_wave_score3 sell_diff_up_days  \\\n", "2014-10-15             0.521            0.580              25.0   \n", "2014-10-29             0.499            0.627              59.0   \n", "2014-12-31             1.030            0.850               0.0   \n", "2014-12-31             1.030            0.850               0.0   \n", "2015-01-21             1.397            1.417             106.0   \n", "\n", "            sell_jump_down_power  sell_jump_up_power  sell_diff_down_days  \\\n", "2014-10-15                -2.135               2.633                290.0   \n", "2014-10-29                -3.602               1.262                 64.0   \n", "2014-12-31                -1.557               0.000                 14.0   \n", "2014-12-31                -1.557               0.000                 14.0   \n", "2015-01-21                -1.304               1.481                 88.0   \n", "\n", "            profit_cg  profit_cg_hunder  keep_days  difference  \n", "2014-10-15    -0.0317           -3.1721         20        True  \n", "2014-10-29     0.0025            0.2507         47        True  \n", "2014-12-31    -0.1179          -11.7896         15        True  \n", "2014-12-31    -0.1179          -11.7896         15        True  \n", "2015-01-21    -0.0628           -6.2808         12        True  \n", "\n", "[5 rows x 51 columns]"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["proxy = AbuOrderPdProxy(abu_result_tuple_test.orders_pd)\n", "with proxy.proxy_work(abu_result_tuple_test_ump.orders_pd) as (order1, order2):\n", "     block_order = order1 - order2\n", "print('正确拦截失败的交易数量{}, 错误拦截的交易数量{}'.format(block_order.result.value_counts()[-1], block_order.result.value_counts()[1]))\n", "block_order.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["由于本节的示例只是从沙盒数据中的为数不多的交易进行训练，且测试集交易数量也不多，所以效果确实一般（在20节后的文档会陆续讲解美股，A股，港股等全市场回测后进行ump训练，回测测试集的示例）可以从对比中发现，大约拦截了一百多笔交易，正确拦截的数量比错误拦截的要多，胜率和盈亏并不理想，拦截了大量的交易可以节省佣金，降低交易数量是最好的优化。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["想要更多的提升可以尝试如下方面：\n", "\n", "1. 编写更多的裁判，寻找更多的特征\n", "2. 在make_ump_block_decision()中组织裁判进行更复杂的综合裁决，进行裁判之间的拦截配合\n", "3. 为每个裁判通过统计赋予裁决权重，进行综合裁决\n", "4. 训练更多交易数据，更多策略来提升主裁的拦截水平及拦截认知范围\n", "\n", "第四点是最有效果且最正确的解决途径，因为依赖统计机器学习的算法当数据越充足时效果越好。\n", "\n", "第一点会在之后的章节示例编写自定义ump裁判中讲解，下面首先示例第二点。\n", "\n", "### 5. 组织裁判进行更复杂的综合裁决\n", "\n", "如下继承买入策略AbuFactorBuyBreak，覆写make_ump_block_decision方法，如下所示："]}, {"cell_type": "code", "execution_count": 14, "metadata": {"collapsed": true}, "outputs": [], "source": ["class AbuFactorBuyBreakUmpDemo(AbuFactorBuyBreak):\n", "    \"\"\"扩展AbuFactorBuyBreak组织裁判进行更复杂的综合裁决\"\"\"\n", "    \n", "    def make_ump_block_decision(self, ml_feature_dict):\n", "        ump = self.ump_manger\n", "        # 统计角度主裁对应这次交易命中的分类簇个数\n", "        deg_hit_cnt = ump.ump_main_deg.predict_hit_kwargs(**ml_feature_dict)\n", "        # 统计跳空主裁对应这次交易命中的分类簇个数\n", "        jump_hit_cnt = ump.ump_main_jump.predict_hit_kwargs(**ml_feature_dict)\n", "        # 统计波动主裁对应这次交易命中的分类簇个数\n", "        wave_hit_cnt = ump.ump_main_wave.predict_hit_kwargs(**ml_feature_dict)\n", "        # 统计价格主裁对应这次交易命中的分类簇个数\n", "        price_hit_cnt = ump.ump_main_price.predict_hit_kwargs(**ml_feature_dict)\n", "        # 进行裁判之间的拦截配合, 简单示例，只要加起来大于2个就算配合成功，拦截\n", "        if deg_hit_cnt + jump_hit_cnt + wave_hit_cnt + price_hit_cnt > 2:\n", "            return True\n", "        return False\n", "\n", "# 通过import的方式导入AbuFactorBuyBreakUmpDemo\n", "# 因为在windows系统上，启动并行后，在ipython notebook中定义的类会在子进程中无法找到\n", "from abupy import AbuFactorBuyBreakUmpDemo"]}, {"cell_type": "markdown", "metadata": {}, "source": ["上面编写的代码实现了在策略的make_ump_block_decision中组织裁判进行更复杂的综合裁决，进行裁判之间的拦截配合，实际上这里的make_ump_block_decision编写的太过简单，只是为了简单示例，具体实现请参考AbuUmpManager中ump_block函数，下面使用AbuFactorBuyBreakUmpDemo做为买入因子，参数还是60，42进行回测如下所示："]}, {"cell_type": "code", "execution_count": 15, "metadata": {"collapsed": true}, "outputs": [], "source": ["buy_factors = [{'xd': 60, 'class': AbuFactorBuyBreakUmpDemo},\n", "               {'xd': 42, 'class': AbuFactorBuyBreakUmpDemo}]\n", "\n", "abu_result_tuple_test_ump_mul, _ = abu.run_loop_back(read_cash,\n", "                                                   buy_factors,\n", "                                                   sell_factors,\n", "                                                   start='2014-07-26',\n", "                                                   end='2016-07-26',\n", "                                                   choice_symbols=test_choice_symbols)\n", "ABuProgress.clear_output()"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["买入后卖出的交易数量:167\n", "买入后尚未卖出的交易数量:18\n", "胜率:43.1138%\n", "平均获利期望:11.7044%\n", "平均亏损期望:-4.4000%\n", "盈亏比:2.0418\n", "所有交易收益比例和:4.2471 \n", "所有交易总盈亏和:673707.2700 \n"]}, {"data": {"text/plain": ["<abupy.MetricsBu.ABuMetricsBase.AbuMetricsBase at 0x1224750b8>"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["AbuMetricsBase.show_general(*abu_result_tuple_test_ump_mul, returns_cmp=True, only_info=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["下面示例上述说的第三点：为每个裁判通过统计赋予裁决权重，进行综合裁决。\n", "\n", "### 6. 让裁判自己学习怎么配合，自己做出最正确的判断\n", "\n", "即是可以再次根据裁判之间的配合数据进行训练学习，让裁判自己学习怎么配合，自己做出最正确的判断，而不是像上面的示例使用固定值2来做为裁决阀值，如下编写AbuFactorBuyBreakReocrdHitDemo类似AbuFactorBuyBreakUmpDemo但是不对交易进行决策，只是把每一个裁判的对应交易命中的分类簇个数进行记录，更新在特征数据里，如下所示："]}, {"cell_type": "code", "execution_count": 17, "metadata": {"collapsed": true}, "outputs": [], "source": ["class AbuFactorBuyBreakReocrdHitDemo(AbuFactorBuyBreak):\n", "    def make_ump_block_decision(self, ml_feature_dict):\n", "        ump = self.ump_manger\n", "        # 统计角度主裁对应这次交易命中的分类簇个数\n", "        deg_hit_cnt = ump.ump_main_deg.predict_hit_kwargs(**ml_feature_dict)\n", "        # 统计跳空主裁对应这次交易命中的分类簇个数\n", "        jump_hit_cnt = ump.ump_main_jump.predict_hit_kwargs(**ml_feature_dict)\n", "        # 统计波动主裁对应这次交易命中的分类簇个数\n", "        wave_hit_cnt = ump.ump_main_wave.predict_hit_kwargs(**ml_feature_dict)\n", "        # 统计价格主裁对应这次交易命中的分类簇个数\n", "        price_hit_cnt = ump.ump_main_price.predict_hit_kwargs(**ml_feature_dict)\n", "        \n", "        ml_feature_dict.update({'deg_hit_cnt': deg_hit_cnt, 'jump_hit_cnt': jump_hit_cnt, \n", "                                'wave_hit_cnt': wave_hit_cnt, 'price_hit_cnt': price_hit_cnt})\n", "        \n", "        return False\n", "    \n", "# 通过import的方式导入AbuFactorBuyBreakReocrdHitDemo，\n", "# 因为在windows系统上，启动并行后，在ipython notebook中定义的类会在子进程中无法找到\n", "from abupy import AbuFactorBuyBreakReocrdHitDemo"]}, {"cell_type": "markdown", "metadata": {}, "source": ["如下使用AbuFactorBuyBreakReocrdHitDemo进行回测，如下所示："]}, {"cell_type": "code", "execution_count": 18, "metadata": {"collapsed": true}, "outputs": [], "source": ["buy_factors = [{'xd': 60, 'class': AbuFactorBuyBreakReocrdHitDemo},\n", "               {'xd': 42, 'class': AbuFactorBuyBreakReocrdHitDemo}]\n", "\n", "abu_result_tuple_test_ump_record, _ = abu.run_loop_back(read_cash,\n", "                                                   buy_factors,\n", "                                                   sell_factors,\n", "                                                   start='2014-07-26',\n", "                                                   end='2016-07-26',\n", "                                                   choice_symbols=test_choice_symbols)\n", "ABuProgress.clear_output()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["下面把刚才AbuFactorBuyBreakReocrdHitDemo在回测中记录的数据和交易的最终结果result进行组合，形成hit_df，如下所示："]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>result</th>\n", "      <th>deg_hit_cnt</th>\n", "      <th>jump_hit_cnt</th>\n", "      <th>wave_hit_cnt</th>\n", "      <th>price_hit_cnt</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>-1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>-1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>-1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>-1</td>\n", "      <td>0</td>\n", "      <td>7</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>-1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   result  deg_hit_cnt  jump_hit_cnt  wave_hit_cnt  price_hit_cnt\n", "0      -1            0             0             0              0\n", "0      -1            0             0             0              0\n", "0      -1            0             0             0              0\n", "0      -1            0             7             0              0\n", "0      -1            0             0             0              0"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["hit_df = pd.DataFrame()\n", "def make_hit_df(order):\n", "    global hit_df\n", "    if order.result != 0:\n", "        hit = pd.DataFrame([order.result, order.ml_features['deg_hit_cnt'], \n", "            order.ml_features['jump_hit_cnt'], \n", "            order.ml_features['wave_hit_cnt'],\n", "            order.ml_features['price_hit_cnt']], \n", "            index=['result', 'deg_hit_cnt', 'jump_hit_cnt', 'wave_hit_cnt', 'price_hit_cnt']).T\n", "        hit_df = hit_df.append(hit)\n", "        \n", "_ = abu_result_tuple_test_ump_record.orders_pd.apply(make_hit_df, axis=1)\n", "hit_df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["上面形成的hit_df的第一列result可做为有监督学习的y，其它的列可做为x特征列，下面使用AbuML直接封装数据：\n", "\n", "备注：abupy中的机器学习模块请阅读：第十二节 机器学习与比特币示例"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"text/plain": ["SVC(C=1.0, cache_size=200, class_weight=None, coef0=0.0,\n", "  decision_function_shape=None, degree=3, gamma='auto', kernel='rbf',\n", "  max_iter=-1, probability=True, random_state=None, shrinking=True,\n", "  tol=0.001, verbose=False)"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["hd_np = hit_df.as_matrix()\n", "y = hd_np[:, 0]\n", "x = hd_np[:, 1:]\n", "hit_ml = AbuML(x, y, hit_df)\n", "hit_ml.fit()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["下面继续继承AbuFactorBuyBreak复写make_ump_block_decision，区别是使用hit_ml对几个裁判这次交易命中的分类簇个数组成矢量特征进行predict，拦截预测结果为-1的交易，代码如下所示："]}, {"cell_type": "code", "execution_count": 21, "metadata": {"collapsed": true}, "outputs": [], "source": ["class AbuFactorBuyBreakHitPredictDemo(AbuFactorBuyBreak):\n", "    \"\"\"扩展AbuFactorBuyBreak组织裁判进行更复杂的综合裁决\"\"\"\n", "\n", "    def _init_self(self, **kwargs):\n", "        \"\"\"\n", "            与AbuFactorBuyBreak基本相同，唯一区别是关键子参数中添加了通过AbuFactorBuyBreakUmpDemo记录训练好的决策器\n", "            self.hit_ml = kwargs['hit_ml']\n", "        \"\"\"\n", "        super(AbuFactorBuyBreakHitPredictDemo, self)._init_self(**kwargs)\n", "        # 添加了通过AbuFactorBuyBreakUmpDemo记录训练好的决策器\n", "        self.hit_ml = kwargs['hit_ml']\n", "\n", "    def make_ump_block_decision(self, ml_feature_dict):\n", "        ump = self.ump_manger\n", "        # 统计角度主裁对应这次交易命中的分类簇个数\n", "        deg_hit_cnt = ump.ump_main_deg.predict_hit_kwargs(**ml_feature_dict)\n", "        # 统计跳空主裁对应这次交易命中的分类簇个数\n", "        jump_hit_cnt = ump.ump_main_jump.predict_hit_kwargs(**ml_feature_dict)\n", "        # 统计波动主裁对应这次交易命中的分类簇个数\n", "        wave_hit_cnt = ump.ump_main_wave.predict_hit_kwargs(**ml_feature_dict)\n", "        # 统计价格主裁对应这次交易命中的分类簇个数\n", "        price_hit_cnt = ump.ump_main_price.predict_hit_kwargs(**ml_feature_dict)\n", "\n", "        result = self.hit_ml.predict([deg_hit_cnt, jump_hit_cnt, wave_hit_cnt, price_hit_cnt])[0]\n", "        if result == -1:\n", "            return True\n", "        return False\n", "    \n", "# 通过import的方式导入AbuFactorBuyBreakHitPredictDemo\n", "# 因为在windows系统上，启动并行后，在ipython notebook中定义的类会在子进程中无法找到\n", "from abupy import AbuFactorBuyBreakHitPredictDemo"]}, {"cell_type": "markdown", "metadata": {}, "source": ["使用AbuFactorBuyBreakHitPredictDemo进行回测，注意在构造策略字典序列的时候使用了刚刚训练好的hit_ml，如下所示："]}, {"cell_type": "code", "execution_count": 22, "metadata": {"collapsed": true}, "outputs": [], "source": ["buy_factors = [{'hit_ml':hit_ml, 'xd': 60, 'class': AbuFactorBuyBreakHitPredictDemo},\n", "               {'hit_ml':hit_ml, 'xd': 42, 'class': AbuFactorBuyBreakHitPredictDemo}]\n", "\n", "abu_result_tuple_test_ump_predict, _ = abu.run_loop_back(read_cash,\n", "                                                   buy_factors,\n", "                                                   sell_factors,\n", "                                                   start='2014-07-26',\n", "                                                   end='2016-07-26',\n", "                                                   choice_symbols=test_choice_symbols)\n", "ABuProgress.clear_output()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["以下度量结果胜率，盈亏比都达到很高，因为用回测的数据进行训练后再次反过来指导回测，结果是没有意义的，这里的示例只是为了容易理解什么叫做：**让裁判自己学习怎么配合，自己做出最正确的判断**，更详细完整的示例会在之后的章节中示例讲解，请关注公众号的更新提醒。"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["买入后卖出的交易数量:8\n", "买入后尚未卖出的交易数量:3\n", "胜率:100.0000%\n", "平均获利期望:16.4870%\n", "平均亏损期望:0.0000%\n", "盈亏比:0.0000\n", "所有交易收益比例和:1.3190 \n", "所有交易总盈亏和:219657.5000 \n"]}, {"data": {"text/plain": ["<abupy.MetricsBu.ABuMetricsBase.AbuMetricsBase at 0x1223ab748>"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["AbuMetricsBase.show_general(*abu_result_tuple_test_ump_predict, returns_cmp=True, only_info=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["小结：本节的示例与《量化交易之路》中讲解的主裁部分内容互为补充，请对照阅读。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### abu量化文档目录章节\n", "\n", "1. [择时策略的开发](http://www.abuquant.com/lecture/lecture_1.html)\n", "2. [择时策略的优化](http://www.abuquant.com/lecture/lecture_2.html)\n", "3. [滑点策略与交易手续费](http://www.abuquant.com/lecture/lecture_3.html)\n", "4. [多支股票择时回测与仓位管理](http://www.abuquant.com/lecture/lecture_4.html)\n", "5. [选股策略的开发](http://www.abuquant.com/lecture/lecture_5.html)\n", "6. [回测结果的度量](http://www.abuquant.com/lecture/lecture_6.html)\n", "7. [寻找策略最优参数和评分](http://www.abuquant.com/lecture/lecture_7.html)\n", "8. [A股市场的回测](http://www.abuquant.com/lecture/lecture_8.html)\n", "9. [港股市场的回测](http://www.abuquant.com/lecture/lecture_9.html)\n", "10. [比特币，莱特币的回测](http://www.abuquant.com/lecture/lecture_10.html)\n", "11. [期货市场的回测](http://www.abuquant.com/lecture/lecture_11.html)\n", "12. [机器学习与比特币示例](http://www.abuquant.com/lecture/lecture_12.html)\n", "13. [量化技术分析应用](http://www.abuquant.com/lecture/lecture_13.html)\n", "14. [量化相关性分析应用](http://www.abuquant.com/lecture/lecture_14.html)\n", "15. [量化交易和搜索引擎](http://www.abuquant.com/lecture/lecture_15.html)\n", "16. [UMP主裁交易决策](http://www.abuquant.com/lecture/lecture_16.html)\n", "17. [UMP边裁交易决策](http://www.abuquant.com/lecture/lecture_17.html)\n", "18. [自定义裁判决策交易](http://www.abuquant.com/lecture/lecture_18.html)\n", "19. [数据源](http://www.abuquant.com/lecture/lecture_19.html)\n", "20. [A股全市场回测](http://www.abuquant.com/lecture/lecture_20.html)\n", "21. [A股UMP决策](http://www.abuquant.com/lecture/lecture_21.html)\n", "22. [美股全市场回测](http://www.abuquant.com/lecture/lecture_22.html)\n", "23. [美股UMP决策](http://www.abuquant.com/lecture/lecture_23.html)\n", "\n", "abu量化系统文档教程持续更新中，请关注公众号中的更新提醒。\n", "\n", "#### 《量化交易之路》目录章节及随书代码地址\n", "\n", "1. [第二章 量化语言——Python](https://github.com/bbfamily/abu/tree/master/ipython/第二章-量化语言——Python.ipynb)\n", "2. [第三章 量化工具——NumPy](https://github.com/bbfamily/abu/tree/master/ipython/第三章-量化工具——NumPy.ipynb)\n", "3. [第四章 量化工具——pandas](https://github.com/bbfamily/abu/tree/master/ipython/第四章-量化工具——pandas.ipynb)\n", "4. [第五章 量化工具——可视化](https://github.com/bbfamily/abu/tree/master/ipython/第五章-量化工具——可视化.ipynb)\n", "5. [第六章 量化工具——数学：你一生的追求到底能带来多少幸福](https://github.com/bbfamily/abu/tree/master/ipython/第六章-量化工具——数学.ipynb)\n", "6. [第七章 量化系统——入门：三只小猪股票投资的故事](https://github.com/bbfamily/abu/tree/master/ipython/第七章-量化系统——入门.ipynb)\n", "7. [第八章 量化系统——开发](https://github.com/bbfamily/abu/tree/master/ipython/第八章-量化系统——开发.ipynb)\n", "8. [第九章 量化系统——度量与优化](https://github.com/bbfamily/abu/tree/master/ipython/第九章-量化系统——度量与优化.ipynb)\n", "9. [第十章 量化系统——机器学习•猪老三](https://github.com/bbfamily/abu/tree/master/ipython/第十章-量化系统——机器学习•猪老三.ipynb)\n", "10. [第十一章 量化系统——机器学习•ABU](https://github.com/bbfamily/abu/tree/master/ipython/第十一章-量化系统——机器学习•ABU.ipynb)\n", "11. [附录A 量化环境部署](https://github.com/bbfamily/abu/tree/master/ipython/附录A-量化环境部署.ipynb)\n", "12. [附录B 量化相关性分析](https://github.com/bbfamily/abu/tree/master/ipython/附录B-量化相关性分析.ipynb)\n", "13. [附录C 量化统计分析及指标应用](https://github.com/bbfamily/abu/tree/master/ipython/附录C-量化统计分析及指标应用.ipynb)\n", "\n", "[更多阿布量化量化技术文章](http://www.abuquant.com/article)\n", "\n", "\n", "更多关于量化交易相关请阅读[《量化交易之路》](http://www.abuquant.com/books/quantify-trading-road.html)\n", "\n", "更多关于量化交易与机器学习相关请阅读[《机器学习之路》](http://www.abuquant.com/books/machine-learning-road.html)\n", "\n", "更多关于abu量化系统请关注微信公众号: abu_quant\n", "\n", "![](./image/qrcode.jpg)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.2"}}, "nbformat": 4, "nbformat_minor": 2}