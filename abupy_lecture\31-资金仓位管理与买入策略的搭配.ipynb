{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# ABU量化系统使用文档 \n", "\n", "<center>\n", "        <img src=\"./image/abu_logo.png\" alt=\"\" style=\"vertical-align:middle;padding:10px 20px;\"><font size=\"6\" color=\"black\"><b>第31节 资金仓位管理与买入策略的搭配</b></font>\n", "</center>\n", "\n", "-----------------\n", "\n", "作者: 阿布\n", "\n", "阿布量化版权所有 未经允许 禁止转载\n", "\n", "[abu量化系统github地址](https://github.com/bbfamily/abu) (欢迎+star)\n", "\n", "[本节ipython notebook](https://github.com/bbfamily/abu/tree/master/abupy_lecture)\n", "\n", "\n", "上一节讲解了趋势跟踪与均值回复的长短线搭配的示例，本节讲解资金仓位管理与买入策略的搭配。\n", "\n", "首先导入本节需要使用的abupy中的模块： "]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["enable example env will only read RomDataBu/csv\n"]}], "source": ["# 基础库导入\n", "\n", "from __future__ import print_function\n", "from __future__ import division\n", "\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "warnings.simplefilter('ignore')\n", "\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "%matplotlib inline\n", "\n", "import os\n", "import sys\n", "# 使用insert 0即只使用github，避免交叉使用了pip安装的abupy，导致的版本不一致问题\n", "sys.path.insert(0, os.path.abspath('../'))\n", "import abupy\n", "\n", "# 使用沙盒数据，目的是和书中一样的数据环境\n", "abupy.env.enable_example_env_ipython()"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"collapsed": true}, "outputs": [], "source": ["from abupy import AbuDownUpTrend, AbuPtPosition, ABuMarketDrawing\n", "from abupy import AbuFactorCloseAtrNStop, AbuFactorAtrNStop, AbuFactorPreAtrNStop, tl\n", "from abupy import abu, ABuProgress, AbuMetricsBase, EMarketTargetType"]}, {"cell_type": "markdown", "metadata": {}, "source": ["abupy中的买策，卖策，选股，资管模块在内部都是面向对象的独立存在，只有使用者在顶层的调度是面向过程的，这种设计的目标是为上层使用者提供最大限度的自由，即通过代码实现整体策略的自由度，以及降低整体策略的复杂耦合度，便于后期维护升级扩张的需要。\n", "\n", "在abupy定制整体策略的步骤如下：\n", "1. 定性整体策略风格，必需(定性趋势策略或者回复策略) \n", "2. 定制买入信号策略，必需\n", "3. 定制卖出信号策略，必需\n", "4. 定制选股策略，可选\n", "5. 定制资金仓位管理策略，可选\n", "6. 定制滑点买入策略，可选（中低频回测可不涉及）\n", "\n", "在abpy中对于一个完整的策略其中1，2，3是必需要做的，4，5，6是可以选择进行的工作，从abupy提供的回测ui可交互界面大体可看到组合一个整体个性化策略步骤需要，如下图：\n", "\n", "![](./image/run_ui.png)\n", "\n", "上一节讲解的通过买入和卖出策略的搭配定性整体策略风格为趋势跟踪策略或均值回复策略，即通过买入策略和卖出策略定性整体策略的交易风格，选股策略和资金管理策略的意义更多在与配合整体策略提高稳定可靠性，简单如下所示：\n", "\n", "![](./image/st_mod.png)\n", "\n", "备注：abupy中更关键技术是使用交易结果预测拦截ump模块对策略交易进行深度优化定制，本节暂不涉及，请阅读‘第15节 量化交易和搜索引擎’或之后的教程\n", "\n", "关于定制选股策略相关内容在‘第27节 狗股选股策略与择时策略的配合’有完整示例，请阅读相关内容。\n", "\n", "本节主要讲解针对整体策略风格体制资金仓位管理策略，abupy默认的仓位管理策略为atr资管策略，详请阅读‘第4节 多支股票择时回测与仓位管理’。\n", "\n", "上一节使用了abupy内置的一个长短线买入策略AbuDownUpTrend：\n", "1. 寻找长线下跌的股票，比如一个季度(4个月)整体趋势为下跌趋势\n", "2. 短线走势上涨的股票，比如一个月整体趋势为上涨趋势\n", "3. 最后使用海龟突破的N日突破策略作为策略最终买入信号\n", "\n", "![](./image/du_trend.png)\n", "\n", "本节针对这个策略示例定制一个对应的资管策略。\n", "\n", "本示例资管策略原理很简单，如下图所示：\n", "\n", "如果AbuDownUpTrend策略设置不同的参数将有可能在buy A或buy B两个位置发出买入信号，那么资管策略将根据前期最高点位置3067做为值'100'定位当前买入价格的位置，很明显buy B的买入位置要高于buy A的买入位置，那么：\n", "\n", "* buy A点的买入仓位配比会高(认为均值回复有很大向上空间)\n", "* buy B点的买入仓位配比会低(认为均值回复向上空间比较小)\n", "\n", "![](./image/pos_trend.png)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["资管仓位策略不需要考虑买入点是否合理以及何时卖出，只需要关注在buy a点buy b应该如何对资金仓位进行配比，策略模块之间尽量减少耦合度。\n", "\n", "上述策略简单举例代码描述如下:\n", "\n", "1. 价格价格曲线由10下跌到5后上涨到9\n", "2. 如果买入点buy_a的位置为7，则相对最高点价格位置：45.0\n", "3. 如果买入点buy_b的位置为9，则相对最高点价格位置：85.0\n", "4. buy A点的买入仓位配比会高(认为均值回复有很大向上空间)\n", "5. buy B点的买入仓位配比会低(认为均值回复向上空间比较小)\n", "\n", "如下所示："]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["buy_a 点相对最高点价格位置：45.0\n", "buy_b 点相对最高点价格位置：85.0\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAy0AAAGaCAYAAAACQKziAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nRK5CYII=\n", "text/plain": ["<matplotlib.figure.Figure at 0x1c16c3d898>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from scipy import stats\n", "price = [10, 9, 8, 7, 6, 5, 6, 7, 8, 9]\n", "buy_a = 7\n", "buy_b = 9\n", "plt.plot(price, label='price')\n", "plt.axhline(buy_a, c='r', label='buy_a')\n", "plt.axhline(buy_b, c='g', label='buy_b')\n", "plt.legend(loc='best')\n", "print('buy_a 点相对最高点价格位置：{}'.format(stats.percentileofscore(price, buy_a)))\n", "print('buy_b 点相对最高点价格位置：{}'.format(stats.percentileofscore(price, buy_b)))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["下面首先依然使用默认的仓位管理策略对上一节的AbuDownUpTrend策略进行回测，打印出交易单，可以看到仓位管理策略为AbuAtrPosition，如下所示："]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style>\n", "    .dataframe thead tr:only-child th {\n", "        text-align: right;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>buy_cnt</th>\n", "      <th>buy_pos</th>\n", "      <th>buy_price</th>\n", "      <th>profit</th>\n", "      <th>result</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2014-02-28</th>\n", "      <td>17024.0</td>\n", "      <td>AbuAtrPosition</td>\n", "      <td>17.5845</td>\n", "      <td>-41700.29</td>\n", "      <td>-1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-06-11</th>\n", "      <td>6405.0</td>\n", "      <td>AbuAtrPosition</td>\n", "      <td>43.8950</td>\n", "      <td>35964.08</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-06-13</th>\n", "      <td>26653.0</td>\n", "      <td>AbuAtrPosition</td>\n", "      <td>14.6100</td>\n", "      <td>15725.27</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-11-03</th>\n", "      <td>9261.0</td>\n", "      <td>AbuAtrPosition</td>\n", "      <td>40.0150</td>\n", "      <td>24333.28</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-04-09</th>\n", "      <td>40713.0</td>\n", "      <td>AbuAtrPosition</td>\n", "      <td>7.4200</td>\n", "      <td>19949.37</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-06-04</th>\n", "      <td>2959.0</td>\n", "      <td>AbuAtrPosition</td>\n", "      <td>202.7500</td>\n", "      <td>-6376.65</td>\n", "      <td>-1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-10-20</th>\n", "      <td>2798.0</td>\n", "      <td>AbuAtrPosition</td>\n", "      <td>153.2500</td>\n", "      <td>152560.95</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            buy_cnt         buy_pos  buy_price     profit  result\n", "2014-02-28  17024.0  AbuAtrPosition    17.5845  -41700.29      -1\n", "2014-06-11   6405.0  AbuAtrPosition    43.8950   35964.08       1\n", "2014-06-13  26653.0  AbuAtrPosition    14.6100   15725.27       1\n", "2014-11-03   9261.0  AbuAtrPosition    40.0150   24333.28       1\n", "2015-04-09  40713.0  AbuAtrPosition     7.4200   19949.37       1\n", "2015-06-04   2959.0  AbuAtrPosition   202.7500   -6376.65      -1\n", "2015-10-20   2798.0  AbuAtrPosition   153.2500  152560.95       1"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# 初始资金量\n", "cash = 3000000\n", "def run_loo_back(ps=None, n_folds=3, start=None, end=None):\n", "    us_choice_symbols = ['usTSLA', 'usNOAH', 'usSFUN', 'usBIDU', 'usAAPL', 'usGOOG', 'usWUBA', 'usVIPS']\n", "    abu_result_tuple, _ = abu.run_loop_back(cash,\n", "                                       buy_factors,\n", "                                       sell_factors,\n", "                                       ps,\n", "                                       start=start,\n", "                                       end=end,\n", "                                       n_folds=n_folds,\n", "                                       choice_symbols=us_choice_symbols)\n", "    ABuProgress.clear_output()\n", "    return abu_result_tuple\n", "        \n", "# 买入策略使用AbuDownUpTrend\n", "buy_factors = [{'class': AbuDownUpTrend}]\n", "# 卖出策略：利润保护止盈策略+风险下跌止损+较大的止盈位\n", "sell_factors = [{'stop_loss_n': 1.0, 'stop_win_n': 3.0,\n", "                 'class': AbuFactorAtrNStop},\n", "                {'class': AbuFactorPreAtrNStop, 'pre_atr_n': 1.5},\n", "                {'class': AbuFactorCloseAtr<PERSON><PERSON>, 'close_atr_n': 1.5}]\n", "# 开始回测\n", "abu_result_tuple = run_loo_back()\n", "# 筛出有交易结果的\n", "orders_pd_atr = abu_result_tuple.orders_pd[abu_result_tuple.orders_pd.result != 0]\n", "orders_pd_atr.filter(['buy_cnt', 'buy_pos', 'buy_price', 'profit', 'result'])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["abupy内置的资管AbuPtPosition策略为上述策略的代码实现, 详代码请阅读源代码，关键策略代码如下:\n", "    \n", "        def fit_position(self, factor_object):\n", "            \"\"\"\n", "            针对均值回复类型策略的仓位管理：\n", "            根据当前买入价格在过去一段金融序列中的价格rank位置来决定仓位\n", "            fit_position计算的结果是买入多少个单位（股，手，顿，合约）\n", "            :param factor_object: ABuFactorBuyBases子类实例对象\n", "            :return:买入多少个单位（股，手，顿，合约）\n", "            \"\"\"\n", "            # self.kl_pd_buy为买入当天的数据，获取之前的past_day_cnt天数据\n", "            last_kl = factor_object.past_today_kl(self.kl_pd_buy, self.past_day_cnt)\n", "            if last_kl is None or last_kl.empty:\n", "                precent_pos = self.pos_base\n", "            else:\n", "                # 使用percentileofscore计算买入价格在过去的past_day_cnt天的价格位置\n", "                precent_pos = stats.percentileofscore(last_kl.close, self.bp)\n", "                precent_pos = (1 + (self.mid_precent - precent_pos) / 100) * self.pos_base\n", "            # 最大仓位限制，依然受上层最大仓位控制限制，eg：如果算出全仓，依然会减少到75%，如修改需要修改最大仓位值\n", "            precent_pos = self.pos_max if precent_pos > self.pos_max else precent_pos\n", "            # 结果是买入多少个单位（股，手，顿，合约）\n", "            return self.read_cash * precent_pos / self.bp * self.deposit_rate\n", "            \n", "下面使用同样的买入卖出策略，但是资管策略使用AbuPtPosition进行回测，如下："]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style>\n", "    .dataframe thead tr:only-child th {\n", "        text-align: right;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>buy_cnt</th>\n", "      <th>buy_pos</th>\n", "      <th>buy_price</th>\n", "      <th>profit</th>\n", "      <th>result</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2014-02-28</th>\n", "      <td>17060.0</td>\n", "      <td>AbuPtPosition</td>\n", "      <td>17.5845</td>\n", "      <td>-41788.47</td>\n", "      <td>-1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-06-11</th>\n", "      <td>5382.0</td>\n", "      <td>AbuPtPosition</td>\n", "      <td>43.8950</td>\n", "      <td>30219.93</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-06-13</th>\n", "      <td>17197.0</td>\n", "      <td>AbuPtPosition</td>\n", "      <td>14.6100</td>\n", "      <td>10146.23</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-11-03</th>\n", "      <td>7684.0</td>\n", "      <td>AbuPtPosition</td>\n", "      <td>40.0150</td>\n", "      <td>20189.71</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-04-09</th>\n", "      <td>26280.0</td>\n", "      <td>AbuPtPosition</td>\n", "      <td>7.4200</td>\n", "      <td>12877.20</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-06-04</th>\n", "      <td>1849.0</td>\n", "      <td>AbuPtPosition</td>\n", "      <td>202.7500</td>\n", "      <td>-3984.60</td>\n", "      <td>-1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-10-20</th>\n", "      <td>1957.0</td>\n", "      <td>AbuPtPosition</td>\n", "      <td>153.2500</td>\n", "      <td>106705.43</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            buy_cnt        buy_pos  buy_price     profit  result\n", "2014-02-28  17060.0  AbuPtPosition    17.5845  -41788.47      -1\n", "2014-06-11   5382.0  AbuPtPosition    43.8950   30219.93       1\n", "2014-06-13  17197.0  AbuPtPosition    14.6100   10146.23       1\n", "2014-11-03   7684.0  AbuPtPosition    40.0150   20189.71       1\n", "2015-04-09  26280.0  AbuPtPosition     7.4200   12877.20       1\n", "2015-06-04   1849.0  AbuPtPosition   202.7500   -3984.60      -1\n", "2015-10-20   1957.0  AbuPtPosition   153.2500  106705.43       1"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# 买策还是AbuDownUpTrend，但资管类字段position使用AbuPtPosition做为策略\n", "buy_factors = [{'class': AbuDownUpTrend, 'position': {'class': AbuPtPosition, 'past_day_cnt': 80}}]\n", "abu_result_tuple = run_loo_back()\n", "# 筛出有交易结果的\n", "orders_pd_precent = abu_result_tuple.orders_pd[abu_result_tuple.orders_pd.result != 0]\n", "orders_pd_precent.filter(['buy_cnt', 'buy_pos', 'buy_price', 'profit', 'result'])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["对比上面两处交易单输出结果可以发现在buy_pos处使用的资管策略不同，导致在buy_cnt上资金仓位配比发生了变化。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### abu量化文档目录章节\n", "\n", "1. [择时策略的开发](http://www.abuquant.com/lecture/lecture_1.html)\n", "2. [择时策略的优化](http://www.abuquant.com/lecture/lecture_2.html)\n", "3. [滑点策略与交易手续费](http://www.abuquant.com/lecture/lecture_3.html)\n", "4. [多支股票择时回测与仓位管理](http://www.abuquant.com/lecture/lecture_4.html)\n", "5. [选股策略的开发](http://www.abuquant.com/lecture/lecture_5.html)\n", "6. [回测结果的度量](http://www.abuquant.com/lecture/lecture_6.html)\n", "7. [寻找策略最优参数和评分](http://www.abuquant.com/lecture/lecture_7.html)\n", "8. [A股市场的回测](http://www.abuquant.com/lecture/lecture_8.html)\n", "9. [港股市场的回测](http://www.abuquant.com/lecture/lecture_9.html)\n", "10. [比特币，莱特币的回测](http://www.abuquant.com/lecture/lecture_10.html)\n", "11. [期货市场的回测](http://www.abuquant.com/lecture/lecture_11.html)\n", "12. [机器学习与比特币示例](http://www.abuquant.com/lecture/lecture_12.html)\n", "13. [量化技术分析应用](http://www.abuquant.com/lecture/lecture_13.html)\n", "14. [量化相关性分析应用](http://www.abuquant.com/lecture/lecture_14.html)\n", "15. [量化交易和搜索引擎](http://www.abuquant.com/lecture/lecture_15.html)\n", "16. [UMP主裁交易决策](http://www.abuquant.com/lecture/lecture_16.html)\n", "17. [UMP边裁交易决策](http://www.abuquant.com/lecture/lecture_17.html)\n", "18. [自定义裁判决策交易](http://www.abuquant.com/lecture/lecture_18.html)\n", "19. [数据源](http://www.abuquant.com/lecture/lecture_19.html)\n", "20. [A股全市场回测](http://www.abuquant.com/lecture/lecture_20.html)\n", "21. [A股UMP决策](http://www.abuquant.com/lecture/lecture_21.html)\n", "22. [美股全市场回测](http://www.abuquant.com/lecture/lecture_22.html)\n", "23. [美股UMP决策](http://www.abuquant.com/lecture/lecture_23.html)\n", "\n", "abu量化系统文档教程持续更新中，请关注公众号中的更新提醒。\n", "\n", "#### 《量化交易之路》目录章节及随书代码地址\n", "\n", "1. [第二章 量化语言——Python](https://github.com/bbfamily/abu/tree/master/ipython/第二章-量化语言——Python.ipynb)\n", "2. [第三章 量化工具——NumPy](https://github.com/bbfamily/abu/tree/master/ipython/第三章-量化工具——NumPy.ipynb)\n", "3. [第四章 量化工具——pandas](https://github.com/bbfamily/abu/tree/master/ipython/第四章-量化工具——pandas.ipynb)\n", "4. [第五章 量化工具——可视化](https://github.com/bbfamily/abu/tree/master/ipython/第五章-量化工具——可视化.ipynb)\n", "5. [第六章 量化工具——数学：你一生的追求到底能带来多少幸福](https://github.com/bbfamily/abu/tree/master/ipython/第六章-量化工具——数学.ipynb)\n", "6. [第七章 量化系统——入门：三只小猪股票投资的故事](https://github.com/bbfamily/abu/tree/master/ipython/第七章-量化系统——入门.ipynb)\n", "7. [第八章 量化系统——开发](https://github.com/bbfamily/abu/tree/master/ipython/第八章-量化系统——开发.ipynb)\n", "8. [第九章 量化系统——度量与优化](https://github.com/bbfamily/abu/tree/master/ipython/第九章-量化系统——度量与优化.ipynb)\n", "9. [第十章 量化系统——机器学习•猪老三](https://github.com/bbfamily/abu/tree/master/ipython/第十章-量化系统——机器学习•猪老三.ipynb)\n", "10. [第十一章 量化系统——机器学习•ABU](https://github.com/bbfamily/abu/tree/master/ipython/第十一章-量化系统——机器学习•ABU.ipynb)\n", "11. [附录A 量化环境部署](https://github.com/bbfamily/abu/tree/master/ipython/附录A-量化环境部署.ipynb)\n", "12. [附录B 量化相关性分析](https://github.com/bbfamily/abu/tree/master/ipython/附录B-量化相关性分析.ipynb)\n", "13. [附录C 量化统计分析及指标应用](https://github.com/bbfamily/abu/tree/master/ipython/附录C-量化统计分析及指标应用.ipynb)\n", "\n", "[更多阿布量化量化技术文章](http://www.abuquant.com/article)\n", "\n", "\n", "更多关于量化交易相关请阅读[《量化交易之路》](http://www.abuquant.com/books/quantify-trading-road.html)\n", "\n", "更多关于量化交易与机器学习相关请阅读[《机器学习之路》](http://www.abuquant.com/books/machine-learning-road.html)\n", "\n", "更多关于abu量化系统请关注微信公众号: abu_quant\n", "\n", "![](./image/qrcode.jpg)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.2"}, "widgets": {"state": {"c0b89918956049b1bf85daea466051be": {"views": [{"cell_index": 5}]}}, "version": "1.2.0"}}, "nbformat": 4, "nbformat_minor": 2}