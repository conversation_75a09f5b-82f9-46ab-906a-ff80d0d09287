#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
abupy回测系统输入格式深度勘探

基于源码分析，验证回测系统接收策略数据的完整格式要求
"""

import os
import sys

# 添加abupy路径
sys.path.insert(0, os.path.abspath('.'))

def analyze_run_loop_back_signature():
    """分析run_loop_back函数签名和参数要求"""
    print("="*80)
    print("🔍 分析1: run_loop_back函数签名")
    print("="*80)
    
    signature_info = {
        "function_name": "abu.run_loop_back",
        "source_file": "abupy/CoreBu/ABu.py",
        "line_range": "27-32",
        "required_params": [
            {
                "name": "read_cash",
                "type": "float/int",
                "description": "初始化资金额度",
                "example": "1000000",
                "required": True
            },
            {
                "name": "buy_factors", 
                "type": "List[Dict]",
                "description": "回测使用的买入因子策略序列",
                "example": "[{'xd': 60, 'class': AbuFactorBuyBreak}]",
                "required": True
            },
            {
                "name": "sell_factors",
                "type": "List[Dict]", 
                "description": "回测使用的卖出因子序列",
                "example": "[{'stop_loss_n': 0.5, 'stop_win_n': 3.0, 'class': AbuFactorAtrNStop}]",
                "required": True
            }
        ],
        "optional_params": [
            {
                "name": "stock_picks",
                "type": "List[Dict] | None",
                "description": "回测使用的选股因子序列",
                "default": "None"
            },
            {
                "name": "choice_symbols",
                "type": "List[str] | None", 
                "description": "备选股票池",
                "default": "None (全市场回测)"
            },
            {
                "name": "n_folds",
                "type": "int",
                "description": "回测n_folds年的历史数据",
                "default": "2"
            },
            {
                "name": "start",
                "type": "str | None",
                "description": "回测开始时间",
                "default": "None"
            },
            {
                "name": "end", 
                "type": "str | None",
                "description": "回测结束时间",
                "default": "None"
            },
            {
                "name": "commission_dict",
                "type": "Dict | None",
                "description": "自定义交易手续费",
                "default": "None"
            }
        ]
    }
    
    print(f"📋 函数: {signature_info['function_name']}")
    print(f"📁 源文件: {signature_info['source_file']}")
    print(f"📍 行号: {signature_info['line_range']}")
    
    print(f"\n✅ 必需参数 ({len(signature_info['required_params'])}个):")
    for param in signature_info['required_params']:
        print(f"   • {param['name']} ({param['type']})")
        print(f"     描述: {param['description']}")
        print(f"     示例: {param['example']}")
        print()
    
    print(f"⚙️  可选参数 ({len(signature_info['optional_params'])}个):")
    for param in signature_info['optional_params']:
        print(f"   • {param['name']} ({param['type']}) = {param['default']}")
        print(f"     描述: {param['description']}")
        print()
    
    return signature_info


def analyze_buy_factors_format():
    """分析买入因子的详细格式要求"""
    print("="*80)
    print("🔍 分析2: 买入因子(buy_factors)格式要求")
    print("="*80)
    
    buy_factors_format = {
        "data_type": "List[Dict]",
        "description": "买入因子字典列表，每个字典代表一个买入策略",
        "required_keys": [
            {
                "key": "class",
                "type": "class object",
                "description": "买入因子类对象",
                "example": "AbuFactorBuyBreak"
            }
        ],
        "optional_keys": [
            {
                "key": "position",
                "type": "Dict | class object",
                "description": "仓位管理配置",
                "format_options": [
                    "直接传入类: AbuAtrPosition",
                    "字典格式: {'class': AbuAtrPosition, 'atr_pos_base': 0.05}"
                ]
            },
            {
                "key": "sell_factors",
                "type": "List[Dict]",
                "description": "专属卖出因子(只对当前买入因子生效)",
                "example": "[{'class': AbuFactorSellBreak, 'xd': 30}]"
            },
            {
                "key": "slippage",
                "type": "class object",
                "description": "滑点类配置",
                "default": "AbuSlippageBuyMean"
            }
        ],
        "factor_specific_params": "因子特定参数(如xd, past_factor等)直接作为字典的键值对",
        "examples": [
            {
                "name": "基础买入因子",
                "config": "{'xd': 60, 'class': AbuFactorBuyBreak}"
            },
            {
                "name": "带仓位管理的买入因子",
                "config": "{'xd': 42, 'class': AbuFactorBuyBreak, 'position': {'class': AbuAtrPosition, 'atr_pos_base': 0.15}}"
            },
            {
                "name": "带专属卖出因子的买入因子",
                "config": "{'xd': 30, 'class': AbuFactorBuyBreak, 'sell_factors': [{'class': AbuFactorSellBreak, 'xd': 20}]}"
            }
        ]
    }
    
    print(f"📊 数据类型: {buy_factors_format['data_type']}")
    print(f"📝 描述: {buy_factors_format['description']}")
    
    print(f"\n✅ 必需键:")
    for key_info in buy_factors_format['required_keys']:
        print(f"   • '{key_info['key']}' ({key_info['type']})")
        print(f"     {key_info['description']}")
        print(f"     示例: {key_info['example']}")
    
    print(f"\n⚙️  可选键:")
    for key_info in buy_factors_format['optional_keys']:
        print(f"   • '{key_info['key']}' ({key_info['type']})")
        print(f"     {key_info['description']}")
        if 'format_options' in key_info:
            print(f"     格式选项:")
            for option in key_info['format_options']:
                print(f"       - {option}")
        if 'example' in key_info:
            print(f"     示例: {key_info['example']}")
        if 'default' in key_info:
            print(f"     默认: {key_info['default']}")
        print()
    
    print(f"🔧 因子特定参数: {buy_factors_format['factor_specific_params']}")
    
    print(f"\n📋 完整示例:")
    for example in buy_factors_format['examples']:
        print(f"   {example['name']}:")
        print(f"   {example['config']}")
        print()
    
    return buy_factors_format


def analyze_sell_factors_format():
    """分析卖出因子的详细格式要求"""
    print("="*80)
    print("🔍 分析3: 卖出因子(sell_factors)格式要求")
    print("="*80)
    
    sell_factors_format = {
        "data_type": "List[Dict]",
        "description": "全局卖出因子字典列表，对所有买入因子生效",
        "required_keys": [
            {
                "key": "class",
                "type": "class object", 
                "description": "卖出因子类对象",
                "example": "AbuFactorAtrNStop"
            }
        ],
        "optional_keys": [
            {
                "key": "slippage",
                "type": "class object",
                "description": "滑点类配置",
                "default": "AbuSlippageSellMean"
            }
        ],
        "factor_specific_params": "因子特定参数直接作为字典的键值对",
        "available_factors": [
            {
                "class": "AbuFactorAtrNStop",
                "description": "ATR动态止盈止损",
                "params": ["stop_loss_n", "stop_win_n"],
                "example": "{'stop_loss_n': 0.5, 'stop_win_n': 3.0, 'class': AbuFactorAtrNStop}"
            },
            {
                "class": "AbuFactorPreAtrNStop", 
                "description": "单日跌幅止损",
                "params": ["pre_atr_n"],
                "example": "{'pre_atr_n': 1.0, 'class': AbuFactorPreAtrNStop}"
            },
            {
                "class": "AbuFactorCloseAtrNStop",
                "description": "利润保护止盈", 
                "params": ["close_atr_n"],
                "example": "{'close_atr_n': 1.5, 'class': AbuFactorCloseAtrNStop}"
            },
            {
                "class": "AbuFactorSellBreak",
                "description": "向下突破卖出",
                "params": ["xd"],
                "example": "{'xd': 120, 'class': AbuFactorSellBreak}"
            },
            {
                "class": "AbuFactorSellNDay",
                "description": "N日持有卖出",
                "params": ["sell_n", "is_sell_today"],
                "example": "{'sell_n': 30, 'class': AbuFactorSellNDay}"
            },
            {
                "class": "AbuDoubleMaSell",
                "description": "双均线死叉卖出",
                "params": ["fast", "slow"],
                "example": "{'fast': 5, 'slow': 60, 'class': AbuDoubleMaSell}"
            }
        ]
    }
    
    print(f"📊 数据类型: {sell_factors_format['data_type']}")
    print(f"📝 描述: {sell_factors_format['description']}")
    
    print(f"\n✅ 必需键:")
    for key_info in sell_factors_format['required_keys']:
        print(f"   • '{key_info['key']}' ({key_info['type']})")
        print(f"     {key_info['description']}")
        print(f"     示例: {key_info['example']}")
    
    print(f"\n📚 可用卖出因子:")
    for factor in sell_factors_format['available_factors']:
        print(f"   • {factor['class']}: {factor['description']}")
        print(f"     参数: {factor['params']}")
        print(f"     示例: {factor['example']}")
        print()
    
    return sell_factors_format


def analyze_position_management():
    """分析仓位管理配置方式"""
    print("="*80)
    print("🔍 分析4: 仓位管理配置方式")
    print("="*80)
    
    position_config = {
        "global_config": {
            "description": "全局仓位管理配置，影响所有买入因子",
            "method": "设置全局变量",
            "variables": [
                {
                    "name": "ABuPositionBase.g_default_pos_class",
                    "type": "Dict | None",
                    "description": "全局默认仓位管理类",
                    "example": "{'class': AbuAtrPosition, 'atr_pos_base': 0.1}"
                },
                {
                    "name": "ABuPositionBase.g_pos_max",
                    "type": "float",
                    "description": "每笔交易最大仓位比例",
                    "default": "0.75 (75%)"
                },
                {
                    "name": "ABuPositionBase.g_deposit_rate",
                    "type": "float", 
                    "description": "保证金最小比例",
                    "default": "1.0 (不使用融资)"
                }
            ]
        },
        "factor_specific_config": {
            "description": "买入因子专属仓位管理配置",
            "method": "在买入因子字典中添加position键",
            "formats": [
                {
                    "type": "直接传入类",
                    "example": "{'class': AbuFactorBuyBreak, 'xd': 60, 'position': AbuAtrPosition}"
                },
                {
                    "type": "字典格式(推荐)",
                    "example": "{'class': AbuFactorBuyBreak, 'xd': 60, 'position': {'class': AbuAtrPosition, 'atr_pos_base': 0.05}}"
                }
            ]
        },
        "available_position_classes": [
            {
                "class": "AbuAtrPosition",
                "description": "ATR仓位管理类(默认)",
                "params": ["atr_pos_base", "atr_base_price", "std_atr_threshold"],
                "default_ratio": "10%"
            },
            {
                "class": "AbuKellyPosition",
                "description": "Kelly公式仓位管理类",
                "params": ["win_rate", "gains_mean", "losses_mean"],
                "default_ratio": "动态计算"
            },
            {
                "class": "AbuPtPosition",
                "description": "价格位置仓位管理类",
                "params": ["pos_base", "past_day_cnt", "mid_precent"],
                "default_ratio": "10%"
            }
        ]
    }
    
    print("🌐 全局配置方式:")
    print(f"   方法: {position_config['global_config']['method']}")
    print(f"   描述: {position_config['global_config']['description']}")
    print("   变量:")
    for var in position_config['global_config']['variables']:
        print(f"     • {var['name']} ({var['type']})")
        print(f"       {var['description']}")
        if 'default' in var:
            print(f"       默认: {var['default']}")
        if 'example' in var:
            print(f"       示例: {var['example']}")
        print()
    
    print("🎯 因子专属配置方式:")
    print(f"   方法: {position_config['factor_specific_config']['method']}")
    print(f"   描述: {position_config['factor_specific_config']['description']}")
    print("   格式:")
    for fmt in position_config['factor_specific_config']['formats']:
        print(f"     • {fmt['type']}")
        print(f"       {fmt['example']}")
        print()
    
    print("📚 可用仓位管理类:")
    for pos_class in position_config['available_position_classes']:
        print(f"   • {pos_class['class']}: {pos_class['description']}")
        print(f"     参数: {pos_class['params']}")
        print(f"     默认仓位: {pos_class['default_ratio']}")
        print()
    
    return position_config


def analyze_ump_system():
    """分析UMP裁判系统配置"""
    print("="*80)
    print("🔍 分析5: UMP裁判系统配置")
    print("="*80)
    
    ump_config = {
        "description": "UMP(Umpire)裁判系统用于风险控制，可以拦截不符合条件的交易信号",
        "global_switches": [
            {
                "name": "ABuUmpManager.g_enable_user_ump",
                "type": "bool",
                "description": "是否开启用户自定义裁判",
                "default": "False"
            },
            {
                "name": "ABuEnv.g_enable_ml_feature",
                "type": "bool", 
                "description": "是否开启特征记录功能(裁判系统依赖)",
                "default": "False"
            }
        ],
        "builtin_ump_switches": [
            {
                "name": "ABuEnv.g_enable_ump_main_deg_block",
                "description": "主裁判-角度拦截"
            },
            {
                "name": "ABuEnv.g_enable_ump_main_jump_block", 
                "description": "主裁判-跳空拦截"
            },
            {
                "name": "ABuEnv.g_enable_ump_main_price_block",
                "description": "主裁判-价格拦截"
            },
            {
                "name": "ABuEnv.g_enable_ump_main_wave_block",
                "description": "主裁判-波动拦截"
            }
        ],
        "custom_ump_usage": [
            "1. 开启全局开关: ABuUmpManager.g_enable_user_ump = True",
            "2. 开启特征记录: ABuEnv.g_enable_ml_feature = True", 
            "3. 清空之前设置: ABuUmpManager.clear_user_ump()",
            "4. 实例化裁判: ump = AbuUmpMainDeg(predict=True, market_name='my_ump')",
            "5. 添加到管理器: ABuUmpManager.append_user_ump(ump)"
        ],
        "note": "UMP系统不是通过参数传递给回测函数，而是通过全局变量和管理器配置"
    }
    
    print(f"📝 描述: {ump_config['description']}")
    
    print(f"\n🔧 全局开关:")
    for switch in ump_config['global_switches']:
        print(f"   • {switch['name']} ({switch['type']})")
        print(f"     {switch['description']}")
        print(f"     默认: {switch['default']}")
        print()
    
    print(f"🛡️  内置裁判开关:")
    for switch in ump_config['builtin_ump_switches']:
        print(f"   • {switch['name']}")
        print(f"     {switch['description']}")
        print()
    
    print(f"👤 自定义裁判使用步骤:")
    for step in ump_config['custom_ump_usage']:
        print(f"   {step}")
    
    print(f"\n⚠️  注意: {ump_config['note']}")
    
    return ump_config


def main():
    """主分析函数"""
    print("🚀 abupy回测系统输入格式深度勘探")
    print("基于源码分析，验证回测系统接收策略数据的完整格式要求")
    print("="*80)
    
    # 执行各项分析
    results = []
    
    # 分析1: run_loop_back函数签名
    signature_info = analyze_run_loop_back_signature()
    results.append(("函数签名分析", signature_info))
    
    # 分析2: 买入因子格式
    buy_factors_format = analyze_buy_factors_format()
    results.append(("买入因子格式", buy_factors_format))
    
    # 分析3: 卖出因子格式
    sell_factors_format = analyze_sell_factors_format()
    results.append(("卖出因子格式", sell_factors_format))
    
    # 分析4: 仓位管理配置
    position_config = analyze_position_management()
    results.append(("仓位管理配置", position_config))
    
    # 分析5: UMP裁判系统
    ump_config = analyze_ump_system()
    results.append(("UMP裁判系统", ump_config))
    
    # 汇总结果
    print("\n" + "="*80)
    print("📊 勘探结果汇总")
    print("="*80)
    
    print("✅ 完成的分析项目:")
    for analysis_name, _ in results:
        print(f"   • {analysis_name}")
    
    print(f"\n🎯 关键发现:")
    print("   1. ✅ 回测系统有3个必需参数: read_cash, buy_factors, sell_factors")
    print("   2. ✅ 买入因子支持嵌套position字典和专属sell_factors")
    print("   3. ✅ 仓位管理可通过全局变量或因子专属配置")
    print("   4. ✅ UMP裁判系统通过全局变量配置，不是函数参数")
    print("   5. ✅ 所有因子都使用字典格式，必须包含'class'键")
    
    print(f"\n📝 技术要点:")
    print("   - 因子参数直接作为字典的键值对传递")
    print("   - position支持类对象或字典两种格式")
    print("   - sell_factors分为全局和专属两种作用域")
    print("   - 风控通过UMP系统实现，独立于回测参数")


if __name__ == "__main__":
    main()
