# abupy参数提取器 - 快速开始

## 🚀 5分钟快速上手

### 1. 基本使用

```python
from universal_abupy_param_extractor import UniversalAbuFactorParamExtractor

# 创建提取器（自动检测环境）
extractor = UniversalAbuFactorParamExtractor()

# 提取完整的因子结构
factor_structure = extractor.extract_complete_factor_structure()

# 查看结果
print(f"买入因子: {len(factor_structure['buy_factors'])}个")
print(f"卖出因子: {len(factor_structure['sell_factors'])}个")
```

### 2. 生成API Schema

```python
# 生成API数据契约
api_schema = extractor.generate_api_schema(factor_structure)

# 保存为JSON文件
import json
with open('api_schema.json', 'w', encoding='utf-8') as f:
    json.dump(api_schema, f, indent=2, ensure_ascii=False)
```

### 3. 命令行使用

```bash
# 直接运行
python universal_abupy_param_extractor.py

# 运行测试
python test_universal_extractor.py

# 查看使用示例
python usage_examples.py
```

## 📁 文件说明

### 核心文件
- `universal_abupy_param_extractor.py` - **主要工具**（推荐使用）
- `test_universal_extractor.py` - 测试脚本
- `usage_examples.py` - 使用示例

### 输出文件
- `universal_factor_structure.json` - 完整的因子参数结构
- `universal_api_schema.json` - API数据契约JSON Schema

### 文档文件
- `PARAM_EXTRACTOR_README.md` - 详细文档
- `QUICK_START.md` - 本快速指南
- `updated_factor_existence_report.md` - 验证报告

## 🎯 主要特性

### ✅ 环境自适应
- 自动检测abupy安装位置
- 支持pip/conda安装的abupy
- 支持源码目录
- 无需手动配置路径

### ✅ 完整参数覆盖
- **68个参数**: 买入因子(34) + 卖出因子 + 仓位管理(7) + UmpBu(24) + 选股(3)
- **15个买入因子**: 双均线、趋势、突破等策略
- **7个卖出因子**: ATR止损、时间止损等
- **3个仓位管理**: ATR、Kelly、固定仓位

### ✅ 双重提取策略
- **动态导入**: 当abupy可导入时使用
- **静态分析**: 当abupy不可导入时使用
- **自动切换**: 根据环境自动选择

## 📊 输出结构

### 因子参数结构
```json
{
  "buy_factors": {
    "AbuDoubleMaBuy": {
      "parameters": {
        "fast": {"type": "int", "required": false, "default": -1},
        "slow": {"type": "int", "required": false, "default": -1}
      }
    }
  },
  "position_classes": {
    "AbuAtrPosition": {
      "parameters": {
        "atr_base_price": {"type": "number", "default": 15}
      }
    }
  }
}
```

### API Schema结构
```json
{
  "StrategyCreate": {
    "properties": {
      "buy_factors": {
        "items": {
          "properties": {
            "class_name": {"enum": ["AbuDoubleMaBuy", ...]},
            "parameters": {
              "fast": {"type": "int"},
              "position": {
                "properties": {
                  "class": {"enum": ["AbuAtrPosition", ...]},
                  "atr_base_price": {"type": "number", "default": 15}
                }
              }
            }
          }
        }
      }
    }
  }
}
```

## 🔧 常见使用场景

### 1. API开发
```python
# 验证策略配置
from jsonschema import validate
validate(request_data, api_schema['StrategyCreate'])
```

### 2. 前端开发
```python
# 生成前端选项数据
python usage_examples.py  # 生成 frontend_factor_options.json
```

### 3. 参数文档
```python
# 生成参数文档
python usage_examples.py  # 生成 parameter_documentation.md
```

## 🐛 常见问题

### Q: 无法找到abupy路径
**A**: 确保abupy已安装：`pip install abupy`

### Q: 无法导入abupy模块
**A**: 这是正常的，工具会自动使用静态分析模式

### Q: 参数数量与预期不符
**A**: 不同版本的abupy参数可能不同，这是正常现象

## 📈 性能提示

### 缓存结果
```python
# 缓存提取结果，避免重复提取
import json
import os

cache_file = 'factor_cache.json'
if os.path.exists(cache_file):
    with open(cache_file, 'r') as f:
        factor_structure = json.load(f)
else:
    extractor = UniversalAbuFactorParamExtractor()
    factor_structure = extractor.extract_complete_factor_structure()
    with open(cache_file, 'w') as f:
        json.dump(factor_structure, f)
```

### 部分提取
```python
# 只提取需要的部分
extractor = UniversalAbuFactorParamExtractor()
buy_factors = extractor._extract_buy_factors()  # 只提取买入因子
```

## 🎉 下一步

1. **阅读详细文档**: `PARAM_EXTRACTOR_README.md`
2. **查看使用示例**: `python usage_examples.py`
3. **运行测试**: `python test_universal_extractor.py`
4. **集成到项目**: 根据API Schema构建策略配置系统

## 📞 获取帮助

- 查看 `PARAM_EXTRACTOR_README.md` 获取详细文档
- 运行 `usage_examples.py` 查看完整示例
- 检查 `updated_factor_existence_report.md` 了解参数验证详情
