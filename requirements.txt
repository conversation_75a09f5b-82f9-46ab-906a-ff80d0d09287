numpy>=1.24.0
pandas>=2.0.0
matplotlib>=3.7.0
scipy>=1.10.0
scikit-learn>=1.2.0
statsmodels>=0.13.0
jupyter>=1.0.0
ipywidgets>=8.0.0
seaborn>=0.12.0
tushare>=1.2.0
pyecharts>=2.0.0
pymongo>=4.3.0
pymysql>=1.0.0
SQLAlchemy>=2.0.0
requests>=2.28.0
beautifulsoup4>=4.11.0
lxml>=4.9.0
tqdm>=4.64.0
PyYAML>=6.0
python-dateutil>=2.8.2
pytz>=2022.7
six>=1.16.0
future>=0.18.0
# TA-Lib 需要单独安装，因为它是通过预编译的二进制文件安装的
# 可以使用 conda install -c conda-forge ta-lib 或者从 https://www.lfd.uci.edu/~gohlke/pythonlibs/#ta-lib 下载对应版本的whl文件
# ta-lib>=0.4.24

# 以下是一些可能需要根据操作系统安装的依赖项
# 在Windows上可能需要安装Visual C++ Build Tools
# 在Linux上可能需要安装: python3-dev, build-essential 等

# 可选依赖，如果需要使用MongoDB
# pymongo>=4.3.0

# 可选依赖，如果需要使用MySQL
# pymysql>=1.0.0
# SQLAlchemy>=2.0.0

# 可选依赖，如果需要使用Jupyter Notebook
# jupyter>=1.0.0
# ipywidgets>=8.0.0
