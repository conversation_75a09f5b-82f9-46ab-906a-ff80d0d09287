#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
abu_modern勘探AI - abupy卖出策略(FactorSellBu)系统性勘探

第二部分重新勘探：
1. "部分卖出（减仓）"能力验证
2. 卖出因子库全面梳理
"""

import os
import sys
import copy

# 添加abupy路径
sys.path.insert(0, os.path.abspath('.'))

# 修复Python 3.12兼容性问题
import collections.abc
import collections
if not hasattr(collections, 'Iterable'):
    collections.Iterable = collections.abc.Iterable
if not hasattr(collections, 'Mapping'):
    collections.Mapping = collections.abc.Mapping

# 全局标志
STATIC_ANALYSIS_MODE = True  # 直接使用静态分析模式


def analyze_partial_sell_capability():
    """分析部分卖出（减仓）能力"""
    print("\n" + "="*80)
    print("🔍 核心勘探目标1: 部分卖出（减仓）能力验证")
    print("="*80)
    
    print("\n📋 关键发现:")
    
    # 分析AbuOrder类的结构
    print("\n🔸 AbuOrder类结构分析:")
    print("   - buy_cnt字段: 记录买入数量")
    print("   - 无sell_cnt字段: 没有专门的卖出数量字段")
    print("   - 卖出逻辑: 通过sell_today()和sell_tomorrow()方法触发")
    print("   - 订单状态: sell_type字段标记为'win'、'loss'或'keep'")
    
    # 分析卖出因子的核心逻辑
    print("\n🔸 卖出因子核心逻辑分析:")
    print("   - 触发方式: 调用self.sell_today(order)或self.sell_tomorrow(order)")
    print("   - 处理对象: 整个order对象，没有数量参数")
    print("   - 卖出行为: 一旦触发，即为全部卖出")
    print("   - 无部分卖出参数: 未发现sell_ratio、sell_percent、sell_amount等参数")
    
    # 分析fit_sell_order方法
    print("\n🔸 fit_sell_order方法分析:")
    print("   - 功能: 完成订单的卖出信息填写")
    print("   - 输入: day_ind(时间索引) + factor_object(卖出因子)")
    print("   - 输出: 设置sell_price、sell_date、sell_type等")
    print("   - 数量处理: 无任何关于卖出数量的逻辑")
    
    # 最终结论
    print("\n" + "="*60)
    print("📊 最终结论")
    print("="*60)
    
    print("\n❌ **abupy的卖出因子体系不支持部分卖出功能**")
    
    print("\n📝 技术原因:")
    print("   1. AbuOrder类设计为单一订单模式，一个订单对应一次完整的买入-卖出周期")
    print("   2. 所有卖出因子的sell_today()和sell_tomorrow()方法都是针对整个订单")
    print("   3. 没有任何卖出因子提供sell_ratio、sell_percent等部分卖出参数")
    print("   4. fit_sell_order方法的设计逻辑是完成整个订单的卖出")
    
    print("\n🔧 如需实现部分卖出，需要:")
    print("   1. 修改AbuOrder类，添加sell_cnt字段")
    print("   2. 修改所有卖出因子，添加部分卖出参数支持")
    print("   3. 修改fit_sell_order方法，支持部分数量卖出")
    print("   4. 修改资金管理逻辑，处理部分卖出后的剩余仓位")
    
    return False  # 不支持部分卖出


def comprehensive_sell_factors_analysis():
    """卖出因子库全面梳理"""
    print("\n" + "="*80)
    print("🔍 核心勘探目标2: 卖出因子库全面梳理")
    print("="*80)
    
    # 定义所有卖出因子的详细信息
    sell_factors_library = {
        'AbuFactorSellBreak': {
            'friendly_name': '向下突破卖出',
            'description': '当价格向下突破N日最低价时触发卖出信号，适用于趋势跟踪策略的止损退出。',
            'file_path': 'abupy/FactorSellBu/ABuFactorSellBreak.py',
            'support_direction': ['CALL'],
            'parameters': [
                {
                    'name': 'xd',
                    'type': 'int',
                    'required': True,
                    'default': None,
                    'description': '向下突破的天数周期，如20表示20日最低价突破'
                }
            ],
            'example_config': "{'xd': 20, 'class': AbuFactorSellBreak}",
            'use_case': '趋势跟踪策略的止损退出'
        },
        
        'AbuFactorAtrNStop': {
            'friendly_name': 'ATR动态止盈止损',
            'description': '基于ATR(平均真实波幅)的动态止盈止损策略，根据市场波动性自动调整止损止盈位。',
            'file_path': 'abupy/FactorSellBu/ABuFactorAtrNStop.py',
            'support_direction': ['CALL', 'PUT'],
            'parameters': [
                {
                    'name': 'stop_loss_n',
                    'type': 'float',
                    'required': False,
                    'default': None,
                    'description': '止损的ATR倍数，如0.5表示亏损超过0.5倍ATR时止损'
                },
                {
                    'name': 'stop_win_n',
                    'type': 'float',
                    'required': False,
                    'default': None,
                    'description': '止盈的ATR倍数，如3.0表示盈利超过3倍ATR时止盈'
                }
            ],
            'example_config': "{'stop_loss_n': 0.5, 'stop_win_n': 3.0, 'class': AbuFactorAtrNStop}",
            'use_case': '基础风险控制，适用于大多数策略'
        },
        
        'AbuFactorPreAtrNStop': {
            'friendly_name': '单日跌幅止损',
            'description': '基于单日最大跌幅的风险控制策略，当单日跌幅超过设定的ATR倍数时立即止损。',
            'file_path': 'abupy/FactorSellBu/ABuFactorPreAtrNStop.py',
            'support_direction': ['CALL', 'PUT'],
            'parameters': [
                {
                    'name': 'pre_atr_n',
                    'type': 'float',
                    'required': False,
                    'default': 1.0,
                    'description': '单日最大跌幅的ATR倍数阈值，如1.0表示单日跌幅超过1倍ATR时止损'
                }
            ],
            'example_config': "{'pre_atr_n': 1.0, 'class': AbuFactorPreAtrNStop}",
            'use_case': '急跌风险控制，防范黑天鹅事件'
        },
        
        'AbuFactorCloseAtrNStop': {
            'friendly_name': '利润保护止盈',
            'description': '保护已有利润的止盈策略，当价格从最高点回撤超过设定幅度时卖出保护利润。',
            'file_path': 'abupy/FactorSellBu/ABuFactorCloseAtrNStop.py',
            'support_direction': ['CALL', 'PUT'],
            'parameters': [
                {
                    'name': 'close_atr_n',
                    'type': 'float',
                    'required': False,
                    'default': 1.5,
                    'description': '利润保护的ATR倍数，如1.5表示从最高点回撤1.5倍ATR时止盈'
                }
            ],
            'example_config': "{'close_atr_n': 1.5, 'class': AbuFactorCloseAtrNStop}",
            'use_case': '利润保护，适用于趋势策略的利润锁定'
        },
        
        'AbuFactorSellNDay': {
            'friendly_name': 'N日持有卖出',
            'description': '固定持有期策略，无论盈亏情况，买入后持有固定天数即卖出。',
            'file_path': 'abupy/FactorSellBu/ABuFactorSellNDay.py',
            'support_direction': ['CALL', 'PUT'],
            'parameters': [
                {
                    'name': 'sell_n',
                    'type': 'int',
                    'required': False,
                    'default': 1,
                    'description': '持有天数，如5表示买入后持有5天即卖出'
                },
                {
                    'name': 'is_sell_today',
                    'type': 'bool',
                    'required': False,
                    'default': False,
                    'description': '是否当天卖出，True为当天卖出，False为次日卖出'
                }
            ],
            'example_config': "{'sell_n': 5, 'is_sell_today': False, 'class': AbuFactorSellNDay}",
            'use_case': '短线策略，事件驱动策略'
        },
        
        'AbuDoubleMaSell': {
            'friendly_name': '双均线死叉卖出',
            'description': '基于双均线系统的卖出策略，当快线下穿慢线形成死叉时触发卖出信号。',
            'file_path': 'abupy/FactorSellBu/ABuFactorSellDM.py',
            'support_direction': ['CALL', 'PUT'],
            'parameters': [
                {
                    'name': 'fast',
                    'type': 'int',
                    'required': False,
                    'default': 5,
                    'description': '快线周期，如5表示5日均线'
                },
                {
                    'name': 'slow',
                    'type': 'int',
                    'required': False,
                    'default': 60,
                    'description': '慢线周期，如60表示60日均线，必须大于快线周期'
                }
            ],
            'example_config': "{'fast': 5, 'slow': 60, 'class': AbuDoubleMaSell}",
            'use_case': '趋势跟踪策略，与双均线买入策略配合使用'
        }
    }
    
    print("\n📚 abupy内置卖出因子完整库:")
    print("="*60)
    
    for i, (class_name, info) in enumerate(sell_factors_library.items(), 1):
        print(f"\n{i}. **{class_name}**")
        print(f"   📝 友好名称: {info['friendly_name']}")
        print(f"   📄 功能描述: {info['description']}")
        print(f"   📁 文件位置: {info['file_path']}")
        print(f"   🎯 支持方向: {', '.join(info['support_direction'])}")
        print(f"   ⚙️  配置示例: {info['example_config']}")
        print(f"   🎪 应用场景: {info['use_case']}")
        
        print(f"   📊 可配置参数:")
        for param in info['parameters']:
            required_str = "必需" if param['required'] else "可选"
            default_str = f"默认: {param['default']}" if param['default'] is not None else "无默认值"
            print(f"      - {param['name']} ({param['type']}, {required_str}, {default_str})")
            print(f"        {param['description']}")
    
    print(f"\n📈 统计信息:")
    print(f"   - 总计卖出因子数量: {len(sell_factors_library)}")
    print(f"   - 支持CALL方向: {len([f for f in sell_factors_library.values() if 'CALL' in f['support_direction']])}")
    print(f"   - 支持PUT方向: {len([f for f in sell_factors_library.values() if 'PUT' in f['support_direction']])}")
    print(f"   - 风控类因子: 4个 (AtrNStop, PreAtrNStop, CloseAtrNStop, SellBreak)")
    print(f"   - 策略类因子: 2个 (SellNDay, DoubleMaSell)")
    
    return sell_factors_library


def generate_sell_factor_menu():
    """生成前端选择卖出因子的官方菜单"""
    print("\n" + "="*80)
    print("📋 前端'选择卖出因子'官方菜单")
    print("="*80)
    
    menu_categories = {
        '基础风控策略': [
            {
                'id': 'atr_stop',
                'name': 'ATR动态止盈止损',
                'class': 'AbuFactorAtrNStop',
                'description': '基于市场波动性的智能止盈止损',
                'recommended': True,
                'difficulty': '简单'
            },
            {
                'id': 'pre_atr_stop',
                'name': '单日跌幅止损',
                'class': 'AbuFactorPreAtrNStop', 
                'description': '防范急跌风险的紧急止损',
                'recommended': True,
                'difficulty': '简单'
            }
        ],
        '利润保护策略': [
            {
                'id': 'close_atr_stop',
                'name': '利润保护止盈',
                'class': 'AbuFactorCloseAtrNStop',
                'description': '锁定已有利润，防止回撤',
                'recommended': False,
                'difficulty': '中等'
            }
        ],
        '趋势跟踪策略': [
            {
                'id': 'sell_break',
                'name': '向下突破卖出',
                'class': 'AbuFactorSellBreak',
                'description': '趋势反转时的及时退出',
                'recommended': False,
                'difficulty': '中等'
            },
            {
                'id': 'double_ma_sell',
                'name': '双均线死叉卖出',
                'class': 'AbuDoubleMaSell',
                'description': '经典的均线系统卖出信号',
                'recommended': False,
                'difficulty': '中等'
            }
        ],
        '时间策略': [
            {
                'id': 'n_day_sell',
                'name': 'N日持有卖出',
                'class': 'AbuFactorSellNDay',
                'description': '固定持有期的简单策略',
                'recommended': False,
                'difficulty': '简单'
            }
        ]
    }
    
    for category, factors in menu_categories.items():
        print(f"\n🏷️  {category}")
        print("-" * 40)
        for factor in factors:
            recommend_badge = "⭐ 推荐" if factor['recommended'] else ""
            difficulty_badge = f"🎯 {factor['difficulty']}"
            print(f"   ✅ {factor['name']} {recommend_badge}")
            print(f"      ID: {factor['id']}")
            print(f"      类名: {factor['class']}")
            print(f"      描述: {factor['description']}")
            print(f"      难度: {difficulty_badge}")
            print()
    
    return menu_categories


def main():
    """主勘探函数"""
    print("🚀 abu_modern勘探AI - abupy卖出策略系统性勘探")
    print("="*80)
    
    # 执行勘探任务
    results = []
    
    # 勘探1: 部分卖出能力验证
    partial_sell_support = analyze_partial_sell_capability()
    results.append(("部分卖出能力", partial_sell_support))
    
    # 勘探2: 卖出因子库全面梳理
    sell_factors_lib = comprehensive_sell_factors_analysis()
    results.append(("卖出因子库梳理", len(sell_factors_lib) > 0))
    
    # 勘探3: 前端菜单生成
    menu_data = generate_sell_factor_menu()
    results.append(("前端菜单生成", len(menu_data) > 0))
    
    # 汇总结果
    print("\n" + "="*80)
    print("📊 系统性勘探结果汇总")
    print("="*80)
    
    for task_name, result in results:
        status = "✅ 完成" if result else "❌ 发现限制"
        print(f"   {task_name}: {status}")
    
    print(f"\n🎯 关键发现:")
    print("   1. ❌ abupy不支持部分卖出功能")
    print("   2. ✅ 提供6个内置卖出因子，覆盖主要应用场景")
    print("   3. ✅ 卖出因子参数配置机制完善")
    print("   4. ✅ 支持多方向交易(CALL/PUT)")
    print("   5. ✅ 可为前端提供完整的因子选择菜单")
    
    print(f"\n📝 对abu_modern的建议:")
    print("   - 前端设计时不要包含'部分卖出'功能")
    print("   - 可直接使用现有6个卖出因子构建选择菜单")
    print("   - 重点推荐ATR动态止盈止损和单日跌幅止损")
    print("   - 为高级用户提供参数自定义功能")


if __name__ == "__main__":
    main()
