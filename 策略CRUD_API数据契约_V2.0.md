# 策略CRUD API数据契约 V2.0

## 概述

本文档定义了量化交易策略管理系统的完整API数据契约，基于对abupy框架的深度分析和实际使用需求制定。

**重要变更说明（V2.0）：**
- ✅ **移除独立的position_strategy字段**
- ✅ **仓位管理参数必须合并到buy_factors中**
- ✅ **明确umpire相关字段的全局状态设置用途**

**🚨 重要发现：日志文件错误纠正**
- ❌ **发现日志文件中的`atr_period`和`atr_times`参数在abupy源码中不存在**
- ✅ **正确的仓位管理参数：`atr_base_price`、`atr_pos_base`、`std_atr_threshold`**
- ✅ **所有参数名称均基于实际源码验证，确保准确性**

---

## 核心数据模型

### StrategyCreate（策略创建）

```json
{
  "name": "string",
  "description": "string", 
  "read_cash": "number",
  "buy_factors": [
    {
      "class_name": "string",
      "parameters": {
        // 买入因子参数
        "xd": "number",
        // ⚠️ 重要：仓位管理参数必须在此处定义
        "position": {
          "class": "string",
          "atr_pos_base": "number",
          "atr_base_price": "number"
        }
      }
    }
  ],
  "sell_factors": [
    {
      "class_name": "string", 
      "parameters": {
        "stop_loss_n": "number",
        "stop_win_n": "number"
      }
    }
  ],
  "stock_picks": [
    {
      "class_name": "string",
      "parameters": {
        "threshold_ang_min": "number",
        "reversed": "boolean"
      }
    }
  ],
  "choice_symbols": ["string"],
  "n_folds": "number",
  "start_date": "string",
  "end_date": "string",
  "commission_dict": {
    "buy_commission_func": "string",
    "sell_commission_func": "string"
  },
  "umpire_rules": ["string"],
  "umpire_market_name": "string"
}
```

### StrategyUpdate（策略更新）

```json
{
  "name": "string",
  "description": "string",
  "read_cash": "number", 
  "buy_factors": [
    {
      "class_name": "string",
      "parameters": {
        "xd": "number",
        // ⚠️ 重要：仓位管理参数必须在此处定义
        "position": {
          "class": "string",
          "atr_pos_base": "number",
          "atr_base_price": "number"
        }
      }
    }
  ],
  "sell_factors": [
    {
      "class_name": "string",
      "parameters": {
        "stop_loss_n": "number", 
        "stop_win_n": "number"
      }
    }
  ],
  "stock_picks": [
    {
      "class_name": "string",
      "parameters": {
        "threshold_ang_min": "number",
        "reversed": "boolean"
      }
    }
  ],
  "choice_symbols": ["string"],
  "n_folds": "number",
  "start_date": "string", 
  "end_date": "string",
  "commission_dict": {
    "buy_commission_func": "string",
    "sell_commission_func": "string"
  },
  "umpire_rules": ["string"],
  "umpire_market_name": "string"
}
```

---

## 🚨 重要说明：仓位管理参数整合

### ❌ 错误的做法（V1.0中的方式）
```json
{
  "buy_factors": [...],
  "sell_factors": [...],
  "position_strategy": {  // ❌ 此字段已移除
    "class_name": "AbuAtrPosition",
    "parameters": {
      "atr_pos_base": 0.1
    }
  }
}
```

### ✅ 正确的做法（V2.0要求）
```json
{
  "buy_factors": [
    {
      "class_name": "AbuFactorBuyBreak",
      "parameters": {
        "xd": 60,
        // ✅ 仓位管理参数必须在买入因子的parameters中定义
        "position": {
          "class": "AbuAtrPosition",
          "atr_pos_base": 0.15,
          "atr_base_price": 18,
          "std_atr_threshold": 0.5
        }
      }
    }
  ]
}
```

### 仓位管理参数说明

#### AbuAtrPosition（ATR仓位管理）
```json
"position": {
  "class": "AbuAtrPosition",
  "atr_pos_base": 0.1,        // 仓位基础配比，默认10%
  "atr_base_price": 15,       // 常数价格设定，默认15
  "std_atr_threshold": 0.5    // ATR阈值，默认0.5
}
```

#### AbuKellyPosition（凯利公式仓位管理）
```json
"position": {
  "class": "AbuKellyPosition", 
  "win_rate": 0.5,      // 胜率期望，默认50%
  "gains_mean": 0.1,    // 平均获利期望，默认10%
  "losses_mean": 0.05   // 平均亏损期望，默认5%
}
```

#### AbuPtPosition（价格位置仓位管理）
```json
"position": {
  "class": "AbuPtPosition",
  "pos_base": 0.1,        // 仓位基础配比，默认10%
  "past_day_cnt": 20      // 参考天数，默认20
}
```

---

## 🎯 Umpire字段说明

### umpire_rules 字段
**用途：** 全局状态设置，用于启用/禁用特定的裁判拦截规则

**重要说明：** 此字段对应后端的全局环境变量设置，而非直接的参数传递。

```json
"umpire_rules": [
  "g_enable_ump_main_deg_block",    // 启用主裁deg拦截
  "g_enable_ump_main_jump_block",   // 启用主裁jump拦截  
  "g_enable_ump_main_price_block",  // 启用主裁price拦截
  "g_enable_ump_main_wave_block",   // 启用主裁wave拦截
  "g_enable_ump_edge_deg_block",    // 启用边裁deg拦截
  "g_enable_ump_edge_full_block",   // 启用边裁full拦截
  "g_enable_ump_edge_price_block",  // 启用边裁price拦截
  "g_enable_ump_edge_wave_block"    // 启用边裁wave拦截
]
```

### umpire_market_name 字段
**用途：** 指定裁判训练数据对应的市场名称

**重要说明：** 此字段用于设置全局市场环境，影响裁判模型的加载和使用。

```json
"umpire_market_name": "us"  // 可选值: "us", "cn", "hk"
```

---

## API端点定义

### 创建策略
```
POST /api/strategies
Content-Type: application/json

Request Body: StrategyCreate
Response: StrategyResponse
```

### 更新策略  
```
PUT /api/strategies/{id}
Content-Type: application/json

Request Body: StrategyUpdate
Response: StrategyResponse
```

### 获取策略
```
GET /api/strategies/{id}
Response: StrategyResponse
```

### 删除策略
```
DELETE /api/strategies/{id}
Response: 204 No Content
```

### 策略列表
```
GET /api/strategies
Query Parameters:
- page: number (default: 1)
- limit: number (default: 10)
- search: string (optional)

Response: StrategyListResponse
```

---

## 响应数据模型

### StrategyResponse
```json
{
  "id": "string",
  "name": "string", 
  "description": "string",
  "read_cash": "number",
  "buy_factors": [...],
  "sell_factors": [...],
  "stock_picks": [...],
  "choice_symbols": ["string"],
  "n_folds": "number",
  "start_date": "string",
  "end_date": "string", 
  "commission_dict": {...},
  "umpire_rules": ["string"],
  "umpire_market_name": "string",
  "created_at": "string",
  "updated_at": "string"
}
```

### StrategyListResponse
```json
{
  "data": [StrategyResponse],
  "pagination": {
    "page": "number",
    "limit": "number", 
    "total": "number",
    "pages": "number"
  }
}
```

---

## 完整示例

### 创建策略请求示例
```json
{
  "name": "ATR突破策略V2",
  "description": "基于ATR的突破买入策略，带仓位管理",
  "read_cash": 1000000,
  "buy_factors": [
    {
      "class_name": "AbuFactorBuyBreak",
      "parameters": {
        "xd": 60,
        "position": {
          "class": "AbuAtrPosition",
          "atr_pos_base": 0.15,
          "atr_base_price": 18,
          "std_atr_threshold": 0.5
        }
      }
    }
  ],
  "sell_factors": [
    {
      "class_name": "AbuFactorAtrNStop", 
      "parameters": {
        "stop_loss_n": 0.5,
        "stop_win_n": 3.0
      }
    }
  ],
  "stock_picks": [
    {
      "class_name": "AbuPickRegressAngMinMax",
      "parameters": {
        "threshold_ang_min": 0.0,
        "reversed": false
      }
    }
  ],
  "choice_symbols": ["AAPL", "TSLA", "GOOGL"],
  "n_folds": 2,
  "start_date": "2020-01-01",
  "end_date": "2023-12-31",
  "umpire_rules": [
    "g_enable_ump_main_deg_block",
    "g_enable_ump_main_price_block"
  ],
  "umpire_market_name": "us"
}
```

---

## 附录：源码依据与验证

### A.1 仓位管理参数验证依据

#### A.1.1 AbuAtrPosition类参数验证
**源码位置：** `abupy/BetaBu/ABuAtrPosition.py`

**验证的参数名称：**
```python
# 第51-55行：_init_self方法中的参数定义
def _init_self(self, **kwargs):
    """atr仓位控制管理类初始化设置"""
    self.atr_base_price = kwargs.pop('atr_base_price', AbuAtrPosition.s_atr_base_price)
    self.std_atr_threshold = kwargs.pop('std_atr_threshold', AbuAtrPosition.s_std_atr_threshold)
    self.atr_pos_base = kwargs.pop('atr_pos_base', g_atr_pos_base)
```

**全局变量验证：**
```python
# 第20行：全局仓位基础配比变量
g_atr_pos_base = 0.1

# 第26-27行：类静态变量
s_atr_base_price = 15  # best fit wide: 12-20
s_std_atr_threshold = 0.5  # best fit wide: 0.3-0.65
```

#### A.1.2 AbuKellyPosition类参数验证
**源码位置：** `abupy/BetaBu/ABuKellyPosition.py`

**验证的参数名称：**
```python
# 第28-31行：_init_self方法中的参数定义
def _init_self(self, **kwargs):
    self.win_rate = kwargs.pop('win_rate', 0.5)
    self.gains_mean = kwargs.pop('gains_mean', 0.1)
    self.losses_mean = kwargs.pop('losses_mean', 0.05)
```

#### A.1.3 AbuPtPosition类参数验证
**源码位置：** `abupy/BetaBu/ABuPtPosition.py`

**验证的参数名称：**
```python
# 第46-53行：_init_self方法中的参数定义
def _init_self(self, **kwargs):
    """价格位置仓位控制管理类初始化设置"""
    # 默认平均仓位比例0.10，即10%
    self.pos_base = kwargs.pop('pos_base', 0.10)
    # 默认获取之前金融时间序列的长短数量
    self.past_day_cnt = kwargs.pop('past_day_cnt', 20)
    # 默认的比例中值，一般不需要设置
    self.mid_precent = kwargs.pop('mid_precent', 50.0)
```

### A.2 仓位管理参数传递机制验证

#### A.2.1 买入因子中position参数处理
**源码位置：** `abupy/FactorBuyBu/ABuFactorBuyBase.py`

**验证的参数传递机制：**
```python
# 第124-140行：_position_class_init方法中的position参数处理
if 'position' in kwargs:
    position = kwargs.pop('position', AbuAtrPosition)
    if isinstance(position, six.class_types):
        # 如果position里面直接设置的是一个class，直接弹出
        self.position_class = position
    elif isinstance(position, dict):
        # 支持赋予字典结构 eg: {'class': AbuAtrPosition, 'atr_base_price': 20, 'atr_pos_base': 0.5}
        if 'class' not in position:
            # 必须要有需要实例化的类信息
            raise ValueError('position class key must name class !!!')
        position_cp = copy.deepcopy(position)
        # pop出类信息后剩下的都为类需要的参数
        self.position_class = position_cp.pop('class')
        # pop出class之后剩下的就class的构造关键字参数
        self.position_kwargs = position_cp
```

#### A.2.2 全局默认仓位管理类验证
**源码位置：** `abupy/BetaBu/ABuPositionBase.py`

**验证的全局变量：**
```python
# 第24-30行：全局默认仓位管理类变量
"""
    买入因子全局默认仓位管理类，默认None的情况下会使用AbuAtrPosition作为默认仓位管理类.

    和卖出因子，选股因子不同，一个买入因子可以对应多个卖出因子，多个选股因子，但一个买入
    因子只能对应一个仓位管理类，可以是全局仓位管理，也可以是针对买入因子的独有附属仓位管理
    类
"""
g_default_pos_class = None
```

### A.3 Umpire相关全局变量验证

#### A.3.1 主裁判全局变量验证
**源码位置：** `abupy/CoreBu/ABuEnv.py`

**验证的全局变量：**
```python
# 第462-469行：主裁判全局开关变量
"""是否开启裁判拦截机制: 主裁deg，默认关闭False"""
g_enable_ump_main_deg_block = False
"""是否开启裁判拦截机制: 主裁jump，默认关闭False"""
g_enable_ump_main_jump_block = False
"""是否开启裁判拦截机制: 主裁price，默认关闭False"""
g_enable_ump_main_price_block = False
"""是否开启裁判拦截机制: 主裁wave，默认关闭False"""
g_enable_ump_main_wave_block = False
```

#### A.3.2 边裁判全局变量验证
**源码位置：** `abupy/CoreBu/ABuEnv.py`

**验证的全局变量：**
```python
# 第474-483行：边裁判全局开关变量
"""是否开启裁判拦截机制: 边裁deg，默认关闭False"""
g_enable_ump_edge_deg_block = False

"""是否开启裁判拦截机制: 边裁price，默认关闭False"""
g_enable_ump_edge_price_block = False

"""是否开启裁判拦截机制: 边裁wave，默认关闭False"""
g_enable_ump_edge_wave_block = False
"""是否开启裁判拦截机制: 边裁full，默认关闭False"""
g_enable_ump_edge_full_block = False
```

#### A.3.3 裁判管理器中的全局变量使用验证
**源码位置：** `abupy/UmpBu/ABuUmpManager.py`

**验证的全局变量使用：**
```python
# 第98-103行：_fix_ump_env方法中的全局变量检查
if ABuEnv.g_enable_ump_edge_deg_block or ABuEnv.g_enable_ump_edge_price_block or \
        ABuEnv.g_enable_ump_edge_wave_block or ABuEnv.g_enable_ump_edge_full_block:
    ABuEnv.g_enable_ml_feature = True
if ABuEnv.g_enable_ump_main_deg_block or ABuEnv.g_enable_ump_main_jump_block or \
        ABuEnv.g_enable_ump_main_price_block or ABuEnv.g_enable_ump_main_wave_block:
    ABuEnv.g_enable_ml_feature = True
```

### A.4 参考文档依据

#### A.4.1 BetaBu模块勘探日志
**文件位置：** `abupy/log_20250628_betabu_explorer.md`

**关键发现引用：**
- 第17-18行："`ABuAtrPosition` 实例**不会**被直接作为参数传递给核心回测函数"
- 第18行："仓位管理对象是在**买入因子**（`buy_factors`，`AbuFactorBuyBase` 的子类）的初始化过程中被创建和管理的"
- 第22行："传递给买入因子构造函数的 `**kwargs` 会被原封不动地传递给仓位管理类的构造函数"

#### A.4.2 UmpBu模块勘探日志
**文件位置：** `abupy/log_20250724_001_umpbu_explorer_v2.md`

**关键发现引用：**
- 第7行："UmpBu（Umpire System，裁判系统）是abupy中用于风险控制的核心模块"
- 第44行："**必须在任何abupy操作之前**，设置其全局项目根目录"

#### A.4.3 原始API契约模板
**文件位置：** `abupy/log_20250811_001_strategy_crud_api_contract_v1.md`

**需要修正的内容：**
- 第97-102行：原始模板中的独立`position_strategy`字段已在V2.0中移除
- 基于源码验证，仓位管理参数必须整合到买入因子的parameters中

### A.5 重要发现：日志文件错误纠正

#### A.5.1 日志文件中的错误参数
**发现的错误（来自`abupy/log_20250628_betabu_explorer.md`第22行和第74-75行）：**
- `atr_period: 25` - **此参数在abupy源码中不存在**
- `atr_times: 1.8` - **此参数在abupy源码中不存在**

**错误验证：**
- 使用 `findstr /s /i "atr_period" *.py` 搜索结果：**无结果**
- 使用 `findstr /s /i "atr_times" *.py` 搜索结果：**无结果**

#### A.5.2 正确的仓位管理参数
**基于源码`abupy/BetaBu/ABuAtrPosition.py`第51-55行的真实参数：**
```python
def _init_self(self, **kwargs):
    self.atr_base_price = kwargs.pop('atr_base_price', AbuAtrPosition.s_atr_base_price)
    self.std_atr_threshold = kwargs.pop('std_atr_threshold', AbuAtrPosition.s_std_atr_threshold)
    self.atr_pos_base = kwargs.pop('atr_pos_base', g_atr_pos_base)
```

**正确的参数名称：**
- `atr_base_price` - 常数价格设定，默认15
- `atr_pos_base` - 仓位基础配比，默认0.1
- `std_atr_threshold` - ATR阈值，默认0.5

#### A.5.3 ATR参数用途澄清
**ATR在abupy中的三个不同用途：**

1. **技术指标计算**（`abupy/IndicatorBu/ABuNDAtr.py`）：
   - `time_period` - ATR计算周期，默认14或21天

2. **风险管理**（`abupy/FactorSellBu/ABuFactorAtrNStop.py`）：
   - `stop_loss_n` - 止损ATR倍数
   - `stop_win_n` - 止盈ATR倍数

3. **仓位管理**（`abupy/BetaBu/ABuAtrPosition.py`）：
   - `atr_base_price` - 常数价格设定
   - `atr_pos_base` - 仓位基础配比
   - `std_atr_threshold` - ATR阈值

#### A.5.4 日志文件错误的可能原因
**分析：** 日志文件中的`atr_period`和`atr_times`可能是：
- 对ATR技术指标参数的误解
- 与风险管理中ATR倍数参数的混淆
- 早期版本的参数名称（但在当前源码中不存在）

#### A.5.5 API结构验证
**验证依据：** 基于`abupy/FactorBuyBu/ABuFactorBuyBase.py`第124-140行的position参数处理机制，确认仓位管理参数必须在买入因子的parameters.position中定义

---

**文档版本：** V2.0
**发布日期：** 2025-08-13
**维护团队：** 量化交易API开发组
