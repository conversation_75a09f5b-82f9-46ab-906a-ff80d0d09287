# abupy参数提取器

一个强大的abupy量化交易框架参数提取工具，支持自动检测环境并提取完整的因子参数结构。

## 🎯 功能特性

### 📊 完整的参数覆盖
- **买入因子参数**: 15个因子类，34个参数
- **卖出因子参数**: 7个因子类，多种止损止盈策略
- **仓位管理参数**: 3个仓位管理类（ATR、Kelly、固定仓位）
- **UmpBu裁判参数**: 8个全局开关 + 16个ML特征
- **选股因子参数**: 角度阈值、反向选择等
- **总计**: 68个用户可配置参数

### 🔧 环境自适应
- ✅ **pip安装的abupy**（site-packages中）
- ✅ **conda安装的abupy**
- ✅ **源码目录安装**
- ✅ **开发环境中的源码**
- ✅ **自动路径检测**，无需手动配置

### 🚀 双重提取策略
- **动态导入**: 当abupy可导入时，使用`importlib`和`inspect`获取实时参数
- **静态分析**: 当abupy不可导入时，使用文件分析提取参数
- **自动切换**: 根据环境自动选择最佳提取方法

## 📦 文件说明

### 核心文件
- `universal_abupy_param_extractor.py` - 通用参数提取器（推荐使用）
- `enhanced_abupy_param_extractor.py` - 增强版参数提取器（源码目录专用）
- `test_universal_extractor.py` - 测试脚本

### 输出文件
- `universal_factor_structure.json` - 完整的因子参数结构
- `universal_api_schema.json` - API数据契约JSON Schema
- `enhanced_factor_structure_corrected.json` - 增强版输出（如果使用）

### 文档文件
- `updated_factor_existence_report.md` - 因子存在性验证报告
- `PARAM_EXTRACTOR_README.md` - 本文档

## 🚀 快速开始

### 基本使用

```python
from universal_abupy_param_extractor import UniversalAbuFactorParamExtractor

# 创建提取器（自动检测环境）
extractor = UniversalAbuFactorParamExtractor()

# 提取完整的因子结构
factor_structure = extractor.extract_complete_factor_structure()

# 查看提取结果
print(f"买入因子: {len(factor_structure['buy_factors'])}个")
print(f"卖出因子: {len(factor_structure['sell_factors'])}个")
print(f"仓位管理: {len(factor_structure['position_classes'])}个")
```

### 生成API Schema

```python
# 生成符合API数据契约的JSON Schema
api_schema = extractor.generate_api_schema(factor_structure)

# 保存为文件
import json
with open('api_schema.json', 'w', encoding='utf-8') as f:
    json.dump(api_schema, f, indent=2, ensure_ascii=False)
```

### 命令行使用

```bash
# 直接运行提取器
python universal_abupy_param_extractor.py

# 运行测试
python test_universal_extractor.py
```

## 📋 输出结构

### 因子参数结构示例

```json
{
  "buy_factors": {
    "AbuFactorBuyXD": {
      "file_path": "abupy/FactorBuyBu/ABuFactorBuyBase.py",
      "parameters": {
        "xd": {
          "type": "int",
          "required": true,
          "default": null,
          "description": "周期参数"
        }
      }
    }
  },
  "position_classes": {
    "AbuAtrPosition": {
      "location": "abupy.BetaBu.ABuAtrPosition",
      "parameters": {
        "atr_base_price": {
          "type": "number",
          "required": false,
          "default": 15,
          "description": "常数价格设定"
        }
      }
    }
  },
  "extraction_info": {
    "abupy_path": "/path/to/abupy",
    "abupy_importable": true,
    "extraction_method": "dynamic_import"
  }
}
```

### API Schema结构示例

```json
{
  "StrategyCreate": {
    "type": "object",
    "properties": {
      "name": {"type": "string", "description": "策略名称"},
      "buy_factors": {
        "type": "array",
        "items": {
          "properties": {
            "class_name": {"enum": ["AbuFactorBuyXD", "AbuDoubleMaBuy", ...]},
            "parameters": {
              "xd": {"type": "int"},
              "position": {
                "type": "object",
                "properties": {
                  "class": {"enum": ["AbuAtrPosition", "AbuKellyPosition"]},
                  "atr_base_price": {"type": "number", "default": 15}
                }
              }
            }
          }
        }
      }
    }
  }
}
```

## 🎯 使用场景

### 1. API开发
```python
# 用于构建策略CRUD API
from jsonschema import validate

api_schema = extractor.generate_api_schema(factor_structure)
validate(request_data, api_schema['StrategyCreate'])
```

### 2. 前端开发
```javascript
// 使用因子结构生成动态表单
const factorStructure = require('./universal_factor_structure.json');

factorStructure.buy_factors.forEach(factor => {
  generateParameterForm(factor.parameters);
});
```

### 3. 参数验证
```python
# 验证策略配置的参数完整性
def validate_strategy_config(config, factor_structure):
    for factor in config['buy_factors']:
        class_name = factor['class_name']
        if class_name in factor_structure['buy_factors']:
            # 验证参数类型和默认值
            pass
```

## 📊 支持的参数类型

### 买入因子参数（34个）
| 参数名 | 类型 | 描述 | 默认值 | 使用因子 |
|--------|------|------|--------|----------|
| `xd` | int | 周期参数 | 必需 | 6个因子 |
| `buy_dw` | float | 胜率阈值 | 0.55 | AbuFactorBuyWD |
| `fast` | int | 快线周期 | -1 | AbuDoubleMaBuy |
| `slow` | int | 慢线周期 | -1 | AbuDoubleMaBuy |
| `close_atr_n` | float | 收盘ATR倍数 | 3 | 多个因子 |

### 仓位管理参数（7个）
| 参数名 | 类型 | 描述 | 默认值 | 所属类 |
|--------|------|------|--------|--------|
| `atr_base_price` | number | 常数价格设定 | 15 | AbuAtrPosition |
| `atr_pos_base` | number | 仓位基础配比 | 0.1 | AbuAtrPosition |
| `win_rate` | number | Kelly仓位胜率 | 0.50 | AbuKellyPosition |
| `gains_mean` | number | 平均获利期望 | 0.10 | AbuKellyPosition |
| `losses_mean` | number | 平均亏损期望 | 0.05 | AbuKellyPosition |

### UmpBu裁判参数（24个）

#### 全局开关（8个）
- `g_enable_ump_main_deg_block` - 主要角度拦截
- `g_enable_ump_main_jump_block` - 主要跳跃拦截
- `g_enable_ump_main_price_block` - 主要价格拦截
- `g_enable_ump_main_wave_block` - 主要波动拦截
- `g_enable_ump_edge_deg_block` - 边缘角度拦截
- `g_enable_ump_edge_full_block` - 边缘完整拦截
- `g_enable_ump_edge_price_block` - 边缘价格拦截
- `g_enable_ump_edge_wave_block` - 边缘波动拦截

#### ML特征（16个）
- **角度特征**: `buy_deg_ang21`, `buy_deg_ang42`, `buy_deg_ang60`, `buy_deg_ang252`
- **价格排名**: `buy_price_rank60`, `buy_price_rank90`, `buy_price_rank120`, `buy_price_rank252`
- **波动得分**: `buy_wave_score1`, `buy_wave_score2`, `buy_wave_score3`
- **ATR特征**: `buy_atr_std`
- **跳跃特征**: `buy_jump_down_power`, `buy_jump_up_power`, `buy_diff_down_days`, `buy_diff_up_days`

### 选股因子参数（3个）
| 参数名 | 类型 | 描述 | 默认值 |
|--------|------|------|--------|
| `threshold_ang_min` | float | 最小角度阈值 | 0.0 |
| `threshold_ang_max` | float | 最大角度阈值 | 90.0 |
| `reversed` | bool | 反向选择 | false |

## 🔧 环境检测

### 自动检测流程
1. **尝试导入abupy**: `import abupy`
2. **获取安装路径**: `abupy.__file__`
3. **回退到当前目录**: 如果导入失败
4. **选择提取方法**: 动态导入 vs 静态分析

### 检测结果示例
```
✅ 检测到abupy安装路径: /usr/local/lib/python3.8/site-packages/abupy
✅ 可以导入abupy模块，使用动态导入模式
```

或

```
✅ 在当前目录找到abupy: ./abupy
⚠️ 无法导入abupy模块，将使用静态分析
```

## 🐛 故障排除

### 常见问题

#### 1. 无法找到abupy路径
```
❌ 无法找到abupy安装路径
```
**解决方案**:
- 确保abupy已正确安装：`pip install abupy`
- 检查Python环境：`python -c "import abupy; print(abupy.__file__)"`
- 如果使用源码，确保在包含abupy目录的路径中运行

#### 2. 参数数量不符合预期
```
⚠️ 参数数量与预期有较大差异（预期约57个）
```
**说明**:
- 这通常是正常的，不同版本的abupy可能有不同的参数
- 68个参数 > 57个预期值，说明发现了更多参数
- 检查`extraction_info`了解具体的提取方法

#### 3. 无法导入abupy模块
```
⚠️ 无法导入abupy模块，将使用静态分析
```
**说明**:
- 这不是错误，静态分析模式仍然可以正常工作
- 通常是因为abupy的依赖包未完全安装
- 如需动态导入模式，请解决abupy的依赖问题

### 验证提取结果

```python
# 检查提取结果的完整性
def validate_extraction(factor_structure):
    required_keys = ['buy_factors', 'sell_factors', 'position_classes', 
                     'umpire_params', 'extraction_info']
    
    for key in required_keys:
        if key not in factor_structure:
            print(f"❌ 缺少关键字段: {key}")
            return False
    
    print("✅ 提取结果结构完整")
    return True

validate_extraction(factor_structure)
```

## 📈 性能优化

### 缓存提取结果
```python
import json
import os
from datetime import datetime, timedelta

def get_cached_factor_structure(cache_hours=24):
    """获取缓存的因子结构，如果缓存过期则重新提取"""
    cache_file = 'factor_structure_cache.json'
    
    # 检查缓存是否存在且未过期
    if os.path.exists(cache_file):
        cache_time = datetime.fromtimestamp(os.path.getmtime(cache_file))
        if datetime.now() - cache_time < timedelta(hours=cache_hours):
            with open(cache_file, 'r', encoding='utf-8') as f:
                return json.load(f)
    
    # 重新提取并缓存
    extractor = UniversalAbuFactorParamExtractor()
    factor_structure = extractor.extract_complete_factor_structure()
    
    with open(cache_file, 'w', encoding='utf-8') as f:
        json.dump(factor_structure, f, indent=2, ensure_ascii=False)
    
    return factor_structure
```

### 部分提取
```python
# 只提取特定类型的参数
extractor = UniversalAbuFactorParamExtractor()

# 只提取买入因子
buy_factors = extractor._extract_buy_factors()

# 只提取仓位管理参数
position_classes = extractor._extract_position_classes()

# 只提取UmpBu参数
umpire_params = extractor._extract_umpire_params()
```

## 🔄 版本对比

### 通用版 vs 增强版

| 特性 | 通用版 | 增强版 |
|------|--------|--------|
| 环境适应性 | ✅ 完全自适应 | ❌ 仅源码目录 |
| 路径检测 | ✅ 自动检测 | ❌ 硬编码路径 |
| 提取方法 | ✅ 双重策略 | ❌ 仅静态分析 |
| 推荐使用 | ✅ **推荐** | ❌ 已废弃 |

**建议**: 始终使用`universal_abupy_param_extractor.py`

## 🤝 贡献指南

### 添加新的参数类型

1. 在`extract_complete_factor_structure`中添加新的提取方法
2. 实现对应的`_extract_xxx_classes`方法
3. 更新`generate_api_schema`方法
4. 添加测试用例

### 改进环境检测

1. 在`_detect_abupy_path`中添加新的检测逻辑
2. 更新`_check_abupy_import`方法
3. 测试不同环境下的兼容性

## 📄 许可证

MIT License

## 🔗 相关文档

- `updated_factor_existence_report.md` - 详细的参数验证报告
- `UmpBu_API数据契约_V1.0.md` - UmpBu API规范
- `策略CRUD_API数据契约_V2.0.md` - 策略API规范

## 📞 支持

如有问题或建议，请查看相关文档或提交Issue。
