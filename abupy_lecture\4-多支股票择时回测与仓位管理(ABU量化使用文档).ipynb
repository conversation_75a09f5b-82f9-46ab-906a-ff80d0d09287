{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# ABU量化系统使用文档 \n", "\n", "<center>\n", "        <img src=\"./image/abu_logo.png\" alt=\"\" style=\"vertical-align:middle;padding:10px 20px;\"><font size=\"6\" color=\"black\"><b>第4节 多支股票择时回测与仓位管理</b></font>\n", "</center>\n", "\n", "-----------------\n", "\n", "作者: 阿布\n", "\n", "阿布量化版权所有 未经允许 禁止转载\n", "\n", "[abu量化系统github地址](https://github.com/bbfamily/abu) (您的star是我的动力！)\n", "\n", "[本节ipython notebook](https://github.com/bbfamily/abu/tree/master/abupy_lecture)\n", "\n", "\n", "首先导入abupy中本节使用的模块："]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["enable example env will only read RomDataBu/df_kl.h5\n"]}], "source": ["from __future__ import print_function\n", "from __future__ import division\n", "\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "warnings.simplefilter('ignore')\n", "\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "%matplotlib inline\n", "\n", "import os\n", "import sys\n", "# 使用insert 0即只使用github，避免交叉使用了pip安装的abupy，导致的版本不一致问题\n", "sys.path.insert(0, os.path.abspath('../'))\n", "import abupy\n", "\n", "# 使用沙盒数据，目的是和书中一样的数据环境\n", "abupy.env.enable_example_env_ipython()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["之前的章节无论讲解策略优化，还是针对回测进行滑点或是手续费都是针对一支股票进行择时操作。\n", "\n", "本节将示例讲解多支股票进行择时策略的实现，依然使用AbuFactorBuyBreak做为买入策略，其它四个卖出策略同时生效的组合。"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"collapsed": true}, "outputs": [], "source": ["from abupy import AbuFactorBuyBreak, AbuFactorSellBreak, AbuPositionBase\n", "from abupy import AbuFactorAtrNStop, AbuFactorPreAtrNStop, AbuFactorCloseAtrNStop\n", "from abupy import ABuPickTimeExecute, AbuBenchmark, AbuCapital\n", "\n", "# buy_factors 60日向上突破，42日向上突破两个因子\n", "buy_factors = [{'xd': 60, 'class': AbuFactorBuyBreak}, \n", "               {'xd': 42, 'class': AbuFactorBuyBreak}]\n", "# 四个卖出因子同时并行生效\n", "sell_factors = [\n", "    {\n", "        'xd': 120,\n", "        'class': AbuFactorSellBreak\n", "    },\n", "    {\n", "        'stop_loss_n': 0.5,\n", "        'stop_win_n': 3.0,\n", "        'class': AbuFactorAtrNStop\n", "    },\n", "    {\n", "        'class': AbuFactorPreAtrNStop,\n", "        'pre_atr_n': 1.0\n", "    },\n", "    {\n", "        'class': AbuFactorCloseAtrNStop,\n", "        'close_atr_n': 1.5\n", "    }]\n", "benchmark = AbuBenchmark()\n", "capital = AbuCapital(1000000, benchmark)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1. 多支股票使用相同的因子进行择时"]}, {"cell_type": "markdown", "metadata": {}, "source": ["选择的股票如下所示：\n", "choice_symbols = ['usTSLA', 'usNOAH', 'usSFUN', 'usBIDU', 'usAAPL',\n", "                  'usGOOG', 'usWUBA', 'usVIPS']\n", "\n", "备注：本节示例都基于美股市场，针对A股市场及港股市场，比特币，期货市场后在后面的章节讲解"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"collapsed": true}, "outputs": [], "source": ["# 我们假定choice_symbols是我们选股模块的结果，\n", "choice_symbols = ['usTSLA', 'usNOAH', 'usSFUN', 'usBIDU', 'usAAPL',\n", "                  'usGOOG', 'usWUBA', 'usVIPS']"]}, {"cell_type": "markdown", "metadata": {}, "source": ["使用ABuPickTimeExecute.do_symbols_with_same_factors()函数对多支股票使用相同的买入因子，卖出因子"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["pid:12485 pick times complete:100.0%\n", "pid:12485 done!\n", "CPU times: user 45.4 s, sys: 210 ms, total: 45.6 s\n", "Wall time: 55.9 s\n"]}], "source": ["%%time\n", "capital = AbuCapital(1000000, benchmark)\n", "orders_pd, action_pd, all_fit_symbols_cnt = ABuPickTimeExecute.do_symbols_with_same_factors(choice_symbols,\n", "                                                                                            benchmark,\n", "                                                                                            buy_factors,\n", "                                                                                            sell_factors,\n", "                                                                                            capital,\n", "                                                                                            show=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["运行完毕，使用了ipython的magic code %%time去统计代码块运行时间，显示运行了19.2 s，本节最后会使用多进程模式运行相同的回测，会和这个时间进行比较。\n", "\n", "备注：具体实际运行时间根据cpu的性能确定\n", "\n", "下面代码显示orders_pd中前10个交易数据："]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>buy_date</th>\n", "      <th>buy_price</th>\n", "      <th>buy_cnt</th>\n", "      <th>buy_factor</th>\n", "      <th>symbol</th>\n", "      <th>buy_pos</th>\n", "      <th>buy_type_str</th>\n", "      <th>expect_direction</th>\n", "      <th>sell_type_extra</th>\n", "      <th>sell_date</th>\n", "      <th>sell_price</th>\n", "      <th>sell_type</th>\n", "      <th>ml_features</th>\n", "      <th>key</th>\n", "      <th>profit</th>\n", "      <th>result</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2014-10-24</th>\n", "      <td>20141024</td>\n", "      <td>105.010</td>\n", "      <td>1904.0</td>\n", "      <td>AbuFactorBuyBreak:60</td>\n", "      <td>usAAPL</td>\n", "      <td>AbuAtrPosition</td>\n", "      <td>call</td>\n", "      <td>1.0</td>\n", "      <td>AbuFactorPreAtrNStop:pre_atr=1.0</td>\n", "      <td>20141202</td>\n", "      <td>114.2500</td>\n", "      <td>win</td>\n", "      <td>None</td>\n", "      <td>64</td>\n", "      <td>17592.96</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-10-24</th>\n", "      <td>20141024</td>\n", "      <td>105.010</td>\n", "      <td>1904.0</td>\n", "      <td>AbuFactorBuyBreak:42</td>\n", "      <td>usAAPL</td>\n", "      <td>AbuAtrPosition</td>\n", "      <td>call</td>\n", "      <td>1.0</td>\n", "      <td>AbuFactorPreAtrNStop:pre_atr=1.0</td>\n", "      <td>20141202</td>\n", "      <td>114.2500</td>\n", "      <td>win</td>\n", "      <td>None</td>\n", "      <td>64</td>\n", "      <td>17592.96</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-10-29</th>\n", "      <td>20141029</td>\n", "      <td>223.680</td>\n", "      <td>781.0</td>\n", "      <td>AbuFactorBuyBreak:42</td>\n", "      <td>usBIDU</td>\n", "      <td>AbuAtrPosition</td>\n", "      <td>call</td>\n", "      <td>1.0</td>\n", "      <td>AbuFactorPreAtrNStop:pre_atr=1.0</td>\n", "      <td>20141202</td>\n", "      <td>235.8100</td>\n", "      <td>win</td>\n", "      <td>None</td>\n", "      <td>67</td>\n", "      <td>9473.53</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-10-29</th>\n", "      <td>20141029</td>\n", "      <td>16.010</td>\n", "      <td>9217.0</td>\n", "      <td>AbuFactorBuyBreak:42</td>\n", "      <td>usNOAH</td>\n", "      <td>AbuAtrPosition</td>\n", "      <td>call</td>\n", "      <td>1.0</td>\n", "      <td>AbuFactorAtrNStop:stop_win=3.0</td>\n", "      <td>20141208</td>\n", "      <td>24.0500</td>\n", "      <td>win</td>\n", "      <td>None</td>\n", "      <td>67</td>\n", "      <td>74104.68</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-10-29</th>\n", "      <td>20141029</td>\n", "      <td>21.430</td>\n", "      <td>6095.0</td>\n", "      <td>AbuFactorBuyBreak:42</td>\n", "      <td>usVIPS</td>\n", "      <td>AbuAtrPosition</td>\n", "      <td>call</td>\n", "      <td>1.0</td>\n", "      <td>AbuFactorPreAtrNStop:pre_atr=1.0</td>\n", "      <td>20141105</td>\n", "      <td>23.4700</td>\n", "      <td>win</td>\n", "      <td>None</td>\n", "      <td>67</td>\n", "      <td>12433.80</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-10-29</th>\n", "      <td>20141029</td>\n", "      <td>223.680</td>\n", "      <td>781.0</td>\n", "      <td>AbuFactorBuyBreak:60</td>\n", "      <td>usBIDU</td>\n", "      <td>AbuAtrPosition</td>\n", "      <td>call</td>\n", "      <td>1.0</td>\n", "      <td>AbuFactorPreAtrNStop:pre_atr=1.0</td>\n", "      <td>20141202</td>\n", "      <td>235.8100</td>\n", "      <td>win</td>\n", "      <td>None</td>\n", "      <td>67</td>\n", "      <td>9473.53</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-11-03</th>\n", "      <td>20141103</td>\n", "      <td>23.364</td>\n", "      <td>5900.0</td>\n", "      <td>AbuFactorBuyBreak:60</td>\n", "      <td>usVIPS</td>\n", "      <td>AbuAtrPosition</td>\n", "      <td>call</td>\n", "      <td>1.0</td>\n", "      <td>AbuFactorPreAtrNStop:pre_atr=1.0</td>\n", "      <td>20141105</td>\n", "      <td>23.4700</td>\n", "      <td>win</td>\n", "      <td>None</td>\n", "      <td>70</td>\n", "      <td>625.40</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-11-11</th>\n", "      <td>20141111</td>\n", "      <td>16.990</td>\n", "      <td>9491.0</td>\n", "      <td>AbuFactorBuyBreak:60</td>\n", "      <td>usNOAH</td>\n", "      <td>AbuAtrPosition</td>\n", "      <td>call</td>\n", "      <td>1.0</td>\n", "      <td>AbuFactorCloseAtrNStop:close_atr_n=1.5</td>\n", "      <td>20141211</td>\n", "      <td>20.1450</td>\n", "      <td>win</td>\n", "      <td>None</td>\n", "      <td>76</td>\n", "      <td>29944.10</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-11-12</th>\n", "      <td>20141112</td>\n", "      <td>43.250</td>\n", "      <td>2945.0</td>\n", "      <td>AbuFactorBuyBreak:42</td>\n", "      <td>usWUBA</td>\n", "      <td>AbuAtrPosition</td>\n", "      <td>call</td>\n", "      <td>1.0</td>\n", "      <td>AbuFactorPreAtrNStop:pre_atr=1.0</td>\n", "      <td>20141209</td>\n", "      <td>42.6425</td>\n", "      <td>loss</td>\n", "      <td>None</td>\n", "      <td>77</td>\n", "      <td>-1789.09</td>\n", "      <td>-1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-11-26</th>\n", "      <td>20141126</td>\n", "      <td>47.100</td>\n", "      <td>3262.0</td>\n", "      <td>AbuFactorBuyBreak:60</td>\n", "      <td>usWUBA</td>\n", "      <td>AbuAtrPosition</td>\n", "      <td>call</td>\n", "      <td>1.0</td>\n", "      <td>AbuFactorAtrNStop:stop_loss=0.5</td>\n", "      <td>20141209</td>\n", "      <td>42.6425</td>\n", "      <td>loss</td>\n", "      <td>None</td>\n", "      <td>87</td>\n", "      <td>-14540.36</td>\n", "      <td>-1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            buy_date  buy_price  buy_cnt            buy_factor  symbol  \\\n", "2014-10-24  20141024    105.010   1904.0  AbuFactorBuyBreak:60  usAAPL   \n", "2014-10-24  20141024    105.010   1904.0  AbuFactorBuyBreak:42  usAAPL   \n", "2014-10-29  20141029    223.680    781.0  AbuFactorBuyBreak:42  usBIDU   \n", "2014-10-29  20141029     16.010   9217.0  AbuFactorBuyBreak:42  usNOAH   \n", "2014-10-29  20141029     21.430   6095.0  AbuFactorBuyBreak:42  usVIPS   \n", "2014-10-29  20141029    223.680    781.0  AbuFactorBuyBreak:60  usBIDU   \n", "2014-11-03  20141103     23.364   5900.0  AbuFactorBuyBreak:60  usVIPS   \n", "2014-11-11  20141111     16.990   9491.0  AbuFactorBuyBreak:60  usNOAH   \n", "2014-11-12  20141112     43.250   2945.0  AbuFactorBuyBreak:42  usWUBA   \n", "2014-11-26  20141126     47.100   3262.0  AbuFactorBuyBreak:60  usWUBA   \n", "\n", "                   buy_pos buy_type_str  expect_direction  \\\n", "2014-10-24  AbuAtrPosition         call               1.0   \n", "2014-10-24  AbuAtrPosition         call               1.0   \n", "2014-10-29  AbuAtrPosition         call               1.0   \n", "2014-10-29  AbuAtrPosition         call               1.0   \n", "2014-10-29  AbuAtrPosition         call               1.0   \n", "2014-10-29  AbuAtrPosition         call               1.0   \n", "2014-11-03  AbuAtrPosition         call               1.0   \n", "2014-11-11  AbuAtrPosition         call               1.0   \n", "2014-11-12  AbuAtrPosition         call               1.0   \n", "2014-11-26  AbuAtrPosition         call               1.0   \n", "\n", "                                   sell_type_extra  sell_date  sell_price  \\\n", "2014-10-24        AbuFactorPreAtrNStop:pre_atr=1.0   20141202    114.2500   \n", "2014-10-24        AbuFactorPreAtrNStop:pre_atr=1.0   20141202    114.2500   \n", "2014-10-29        AbuFactorPreAtrNStop:pre_atr=1.0   20141202    235.8100   \n", "2014-10-29          AbuFactorAtrNStop:stop_win=3.0   20141208     24.0500   \n", "2014-10-29        AbuFactorPreAtrNStop:pre_atr=1.0   20141105     23.4700   \n", "2014-10-29        AbuFactorPreAtrNStop:pre_atr=1.0   20141202    235.8100   \n", "2014-11-03        AbuFactorPreAtrNStop:pre_atr=1.0   20141105     23.4700   \n", "2014-11-11  AbuFactorCloseAtrNStop:close_atr_n=1.5   20141211     20.1450   \n", "2014-11-12        AbuFactorPreAtrNStop:pre_atr=1.0   20141209     42.6425   \n", "2014-11-26         AbuFactorAtrNStop:stop_loss=0.5   20141209     42.6425   \n", "\n", "           sell_type ml_features  key    profit  result  \n", "2014-10-24       win        None   64  17592.96       1  \n", "2014-10-24       win        None   64  17592.96       1  \n", "2014-10-29       win        None   67   9473.53       1  \n", "2014-10-29       win        None   67  74104.68       1  \n", "2014-10-29       win        None   67  12433.80       1  \n", "2014-10-29       win        None   67   9473.53       1  \n", "2014-11-03       win        None   70    625.40       1  \n", "2014-11-11       win        None   76  29944.10       1  \n", "2014-11-12      loss        None   77  -1789.09      -1  \n", "2014-11-26      loss        None   87 -14540.36      -1  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["orders_pd[:10]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["通过buy_cnt列可以发现每次交易数量都不一样，由于内部有资金管理控制模块默认使用atr进行仓位控制\n", "\n", "默认资金管理控制使用AbuAtrPosition，详情请阅读源代码，下面会有自定义仓位管理的示例。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["下面代码显示action_pd中前10个行为数据："]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Date</th>\n", "      <th>Price</th>\n", "      <th>Cnt</th>\n", "      <th>symbol</th>\n", "      <th>Direction</th>\n", "      <th>Price2</th>\n", "      <th>action</th>\n", "      <th>deal</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>20141024</td>\n", "      <td>105.010</td>\n", "      <td>1904.0</td>\n", "      <td>usAAPL</td>\n", "      <td>1.0</td>\n", "      <td>114.250</td>\n", "      <td>buy</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>20141024</td>\n", "      <td>105.010</td>\n", "      <td>1904.0</td>\n", "      <td>usAAPL</td>\n", "      <td>1.0</td>\n", "      <td>114.250</td>\n", "      <td>buy</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>20141029</td>\n", "      <td>16.010</td>\n", "      <td>9217.0</td>\n", "      <td>usNOAH</td>\n", "      <td>1.0</td>\n", "      <td>24.050</td>\n", "      <td>buy</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>20141029</td>\n", "      <td>223.680</td>\n", "      <td>781.0</td>\n", "      <td>usBIDU</td>\n", "      <td>1.0</td>\n", "      <td>235.810</td>\n", "      <td>buy</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>20141029</td>\n", "      <td>223.680</td>\n", "      <td>781.0</td>\n", "      <td>usBIDU</td>\n", "      <td>1.0</td>\n", "      <td>235.810</td>\n", "      <td>buy</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>20141029</td>\n", "      <td>21.430</td>\n", "      <td>6095.0</td>\n", "      <td>usVIPS</td>\n", "      <td>1.0</td>\n", "      <td>23.470</td>\n", "      <td>buy</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>20141103</td>\n", "      <td>23.364</td>\n", "      <td>5900.0</td>\n", "      <td>usVIPS</td>\n", "      <td>1.0</td>\n", "      <td>23.470</td>\n", "      <td>buy</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>20141105</td>\n", "      <td>23.470</td>\n", "      <td>6095.0</td>\n", "      <td>usVIPS</td>\n", "      <td>1.0</td>\n", "      <td>21.430</td>\n", "      <td>sell</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>20141105</td>\n", "      <td>23.470</td>\n", "      <td>5900.0</td>\n", "      <td>usVIPS</td>\n", "      <td>1.0</td>\n", "      <td>23.364</td>\n", "      <td>sell</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>20141111</td>\n", "      <td>16.990</td>\n", "      <td>9491.0</td>\n", "      <td>usNOAH</td>\n", "      <td>1.0</td>\n", "      <td>20.145</td>\n", "      <td>buy</td>\n", "      <td>False</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       Date    Price     Cnt  symbol  Direction   Price2 action   deal\n", "0  20141024  105.010  1904.0  usAAPL        1.0  114.250    buy   True\n", "1  20141024  105.010  1904.0  usAAPL        1.0  114.250    buy   True\n", "2  20141029   16.010  9217.0  usNOAH        1.0   24.050    buy   True\n", "3  20141029  223.680   781.0  usBIDU        1.0  235.810    buy   True\n", "4  20141029  223.680   781.0  usBIDU        1.0  235.810    buy   True\n", "5  20141029   21.430  6095.0  usVIPS        1.0   23.470    buy  False\n", "6  20141103   23.364  5900.0  usVIPS        1.0   23.470    buy  False\n", "7  20141105   23.470  6095.0  usVIPS        1.0   21.430   sell  False\n", "8  20141105   23.470  5900.0  usVIPS        1.0   23.364   sell  False\n", "9  20141111   16.990  9491.0  usNOAH        1.0   20.145    buy  False"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["action_pd[:10]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["* 注意deal列代表了交易是否成交，由于内部有资金管理控制模块，所以不是所有交易信号都可以最后成交。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["下面我们使用abu量化系统度量模块对整体结果做个度量，如下图所示（之后章节会对度量方法及模块进行详细讲解，这里请先简单使用即可）。"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["买入后卖出的交易数量:67\n", "买入后尚未卖出的交易数量:3\n", "胜率:43.2836%\n", "平均获利期望:12.2712%\n", "平均亏损期望:-4.9050%\n", "盈亏比:1.9327\n", "策略收益: 29.4383%\n", "基准收益: 15.0841%\n", "策略年化收益: 14.7192%\n", "基准年化收益: 7.5420%\n", "策略买入成交比例:84.2857%\n", "策略资金利用率比例:22.3612%\n", "策略共执行504个交易日\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAzAAAAGGCAYAAACkBGRaAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\n", "text/plain": ["<matplotlib.figure.Figure at 0x110360080>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from abupy import AbuMetricsBase \n", "metrics = AbuMetricsBase(orders_pd, action_pd, capital, benchmark)\n", "metrics.fit_metrics()\n", "metrics.plot_returns_cmp(only_show_returns=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2. 自定义仓位管理策略的实现"]}, {"cell_type": "markdown", "metadata": {}, "source": ["上面使用AbuMetricsBase进行度量，我们计算出：\n", "\n", "1. 胜率:41.79%\n", "2. 平均获利期望:12.01%\n", "3. 平均亏损期望:-4.91%\n", "    \n", "有这三个参数就可以使用kelly公式来做仓位控制，AbuKellyPosition实现如下："]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true}, "outputs": [], "source": ["class AbuKellyPosition(AbuPositionBase):\n", "    \"\"\"示例kelly仓位管理类\"\"\"\n", "\n", "    def fit_position(self, factor_object):\n", "        \"\"\"\n", "        fit_position计算的结果是买入多少个单位（股，手，顿，合约）\n", "        需要factor_object策略因子对象通过历史回测统计胜率，期望收益，期望亏损，\n", "        并设置构造当前factor_object对象，通过kelly公司计算仓位\n", "        :param factor_object: ABuFactorBuyBases子类实例对象\n", "        :return:买入多少个单位（股，手，顿，合约）\n", "        \"\"\"\n", "        # 败率\n", "        loss_rate = 1 - self.win_rate\n", "        # kelly计算出仓位比例\n", "        kelly_pos = self.win_rate - loss_rate / (self.gains_mean / self.losses_mean)\n", "        # 最大仓位限制，依然受上层最大仓位控制限制，eg：如果kelly计算出全仓，依然会减少到75%，如修改需要修改最大仓位值\n", "        kelly_pos = self.pos_max if kelly_pos > self.pos_max else kelly_pos\n", "        # 结果是买入多少个单位（股，手，顿，合约）\n", "        return self.read_cash * kelly_pos / self.bp * self.deposit_rate\n", "\n", "    def _init_self(self, **kwargs):\n", "        \"\"\"kelly仓位控制管理类初始化设置\"\"\"\n", "\n", "        # 默认kelly仓位胜率0.50\n", "        self.win_rate = kwargs.pop('win_rate', 0.50)\n", "        # 默认平均获利期望0.10\n", "        self.gains_mean = kwargs.pop('gains_mean', 0.10)\n", "        # 默认平均亏损期望0.05\n", "        self.losses_mean = kwargs.pop('losses_mean', 0.05)\n", "\n", "        \"\"\"以默认的设置kelly根据计算0.5 - 0.5 / (0.10 / 0.05) 仓位将是0.25即25%\"\"\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["自定义仓位管理代码如上AbuKellyPosition：\n", "\n", "1. 仓位管理类需要继承AbuPositionBase\n", "2. 仓位管理类主要需要实现函数fit_position，即根据买入价格，本金基数等融合买入策略对买入单位进行计算\n", "3. 仓位管理类主要需要实现函数_init_self，外部通过字典参数将胜率等参数进行关键子参数设置（详见后使用示例）\n", "\n", "更多资金管理代码请阅读AbuPositionBase\n", "\n", "下面编写buy_factors2，其42d突破使用position＝AbuKellyPosition\n", "\n", "* 参数胜率：metrics.win_rate(41.79%)\n", "* 期望收益：metrics.gains_mean(12.01%)\n", "* 期望亏损：metrics.losses_mean(-4.91%)，\n", "\n", "代码如下所示："]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["pid:12485 pick times complete:100.0%\n", "pid:12485 done!\n"]}], "source": ["from abupy import AbuKellyPosition\n", "\n", "# 42d使用刚刚编写的AbuKellyPosition，60d仍然使用默认仓位管理类，即abupy中内置的AbuAtrPosition类\n", "buy_factors2 = [{'xd': 60, 'class': AbuFactorBuyBreak},\n", "                {'xd': 42, 'position': {'class': AbuKellyPosition, 'win_rate': metrics.win_rate, \n", "                                        'gains_mean': metrics.gains_mean, 'losses_mean': -metrics.losses_mean},\n", "                 'class': AbuFactorBuyBreak}]\n", "\n", "capital = AbuCapital(1000000, benchmark)\n", "orders_pd, action_pd, all_fit_symbols_cnt = ABuPickTimeExecute.do_symbols_with_same_factors(choice_symbols,\n", "                                                                                            benchmark,\n", "                                                                                            buy_factors2,\n", "                                                                                            sell_factors,\n", "                                                                                            capital,\n", "                                                                                            show=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["从输出生成的orders_pd中可以看到buy Pos列所有42d突破都使用了AbuKellyPosition，60d仍然使用AbuAtrPosition"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>symbol</th>\n", "      <th>buy_cnt</th>\n", "      <th>buy_factor</th>\n", "      <th>buy_pos</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2014-10-24</th>\n", "      <td>usAAPL</td>\n", "      <td>1904.0</td>\n", "      <td>AbuFactorBuyBreak:60</td>\n", "      <td>AbuAtrPosition</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-10-24</th>\n", "      <td>usAAPL</td>\n", "      <td>1962.0</td>\n", "      <td>AbuFactorBuyBreak:42</td>\n", "      <td>AbuKellyPosition</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-10-29</th>\n", "      <td>usBIDU</td>\n", "      <td>921.0</td>\n", "      <td>AbuFactorBuyBreak:42</td>\n", "      <td>AbuKellyPosition</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-10-29</th>\n", "      <td>usNOAH</td>\n", "      <td>12875.0</td>\n", "      <td>AbuFactorBuyBreak:42</td>\n", "      <td>AbuKellyPosition</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-10-29</th>\n", "      <td>usVIPS</td>\n", "      <td>9618.0</td>\n", "      <td>AbuFactorBuyBreak:42</td>\n", "      <td>AbuKellyPosition</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-10-29</th>\n", "      <td>usBIDU</td>\n", "      <td>781.0</td>\n", "      <td>AbuFactorBuyBreak:60</td>\n", "      <td>AbuAtrPosition</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-11-03</th>\n", "      <td>usVIPS</td>\n", "      <td>5900.0</td>\n", "      <td>AbuFactorBuyBreak:60</td>\n", "      <td>AbuAtrPosition</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-11-11</th>\n", "      <td>usNOAH</td>\n", "      <td>9491.0</td>\n", "      <td>AbuFactorBuyBreak:60</td>\n", "      <td>AbuAtrPosition</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-11-12</th>\n", "      <td>usWUBA</td>\n", "      <td>4765.0</td>\n", "      <td>AbuFactorBuyBreak:42</td>\n", "      <td>AbuKellyPosition</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-11-26</th>\n", "      <td>usWUBA</td>\n", "      <td>3262.0</td>\n", "      <td>AbuFactorBuyBreak:60</td>\n", "      <td>AbuAtrPosition</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            symbol  buy_cnt            buy_factor           buy_pos\n", "2014-10-24  usAAPL   1904.0  AbuFactorBuyBreak:60    AbuAtrPosition\n", "2014-10-24  usAAPL   1962.0  AbuFactorBuyBreak:42  AbuKellyPosition\n", "2014-10-29  usBIDU    921.0  AbuFactorBuyBreak:42  AbuKellyPosition\n", "2014-10-29  usNOAH  12875.0  AbuFactorBuyBreak:42  AbuKellyPosition\n", "2014-10-29  usVIPS   9618.0  AbuFactorBuyBreak:42  AbuKellyPosition\n", "2014-10-29  usBIDU    781.0  AbuFactorBuyBreak:60    AbuAtrPosition\n", "2014-11-03  usVIPS   5900.0  AbuFactorBuyBreak:60    AbuAtrPosition\n", "2014-11-11  usNOAH   9491.0  AbuFactorBuyBreak:60    AbuAtrPosition\n", "2014-11-12  usWUBA   4765.0  AbuFactorBuyBreak:42  AbuKellyPosition\n", "2014-11-26  usWUBA   3262.0  AbuFactorBuyBreak:60    AbuAtrPosition"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["orders_pd[:10].filter(['symbol', 'buy_cnt', 'buy_factor', 'buy_pos'])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3. 多支股票使用不同的因子进行择时 \n", "\n", "使用ABuPickTimeExecute.do_symbols_with_diff_factors()函数针对不同的股票使用不同的买入因子和不同的卖出因子，\n", "\n", "具体实现请查阅源代码ABuPickTimeExecute，使用示例如下："]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["pid:12485 pick times complete:100.0%\n", "pid:12485 done!\n"]}], "source": ["# 选定noah和sfun\n", "target_symbols = ['usSFUN', 'usNOAH']\n", "\n", "# 针对sfun只使用42d向上突破作为买入因子\n", "buy_factors_sfun = [{'xd': 42, 'class': AbuFactorBuyBreak}]\n", "# 针对sfun只使用60d向下突破作为卖出因子\n", "sell_factors_sfun = [{'xd': 60, 'class': AbuFactorSellBreak}]\n", "\n", "# 针对noah只使用21d向上突破作为买入因子\n", "buy_factors_noah = [{'xd': 21, 'class': AbuFactorBuyBreak}]\n", "# 针对noah只使用42d向下突破作为卖出因子\n", "sell_factors_noah = [{'xd': 42, 'class': AbuFactorSellBreak}]\n", "\n", "factor_dict = dict()\n", "# 构建SFUN独立的buy_factors，sell_factors的dict\n", "factor_dict['usSFUN'] = {'buy_factors': buy_factors_sfun,\n", "                         'sell_factors': sell_factors_sfun}\n", "\n", "# 构建NOAH独立的buy_factors，sell_factors的dict\n", "factor_dict['usNOAH'] = {'buy_factors': buy_factors_noah,\n", "                         'sell_factors': sell_factors_noah}\n", "\n", "# 初始化资金\n", "capital = AbuCapital(1000000, benchmark)\n", "# 使用do_symbols_with_diff_factors执行\n", "orders_pd, action_pd, all_fit_symbols = ABuPickTimeExecute.do_symbols_with_diff_factors(target_symbols,\n", "                                                                                        benchmark,\n", "                                                                                        factor_dict,\n", "                                                                                        capital)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["如下代码通过pandas的交叉表来分析输出的orders_pd， 来证明: noah买入因子全部是使用21d向上突破，sfun买入因子全部是使用42d向上突破："]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>symbol</th>\n", "      <th>usNOAH</th>\n", "      <th>usSFUN</th>\n", "    </tr>\n", "    <tr>\n", "      <th>buy_factor</th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>AbuFactorBuyBreak:21</th>\n", "      <td>9</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AbuFactorBuyBreak:42</th>\n", "      <td>0</td>\n", "      <td>4</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["symbol                usNOAH  usSFUN\n", "buy_factor                          \n", "AbuFactorBuyBreak:21       9       0\n", "AbuFactorBuyBreak:42       0       4"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.crosstab(orders_pd.buy_factor, orders_pd.symbol)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4. 使用并行来提升择时运行效率"]}, {"cell_type": "markdown", "metadata": {}, "source": ["当你选择的股票非常多的时候，比如很多时候是对全市场进行回测，那就需要多进程并行来提升运行效率，AbuPickTimeMaster.do_symbols_with_same_factors_process()函数通过定义n_process_kl（同时获取股票数据的进程数）和n_process_pick_time（同时进行择时的进程数）来完成操作.\n", "\n", "具体实现代码请阅读AbuPickTimeMaster，使用示例如下所示："]}, {"cell_type": "code", "execution_count": 13, "metadata": {"scrolled": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["pid:12502 gen kl_pd complete:100.0%\n", "pid:12503 gen kl_pd complete:100.0%\n", "pid:12504 gen kl_pd complete:100.0%\n", "pid:12505 gen kl_pd complete:100.0%\n", "pid:12506 gen kl_pd complete:100.0%\n", "pid:12507 gen kl_pd complete:100.0%\n", "pid:12508 gen kl_pd complete:100.0%\n", "pid:12509 gen kl_pd complete:100.0%\n", "pid:12503 done!\n", "pid:12505 done!\n", "pid:12506 done!\n", "pid:12507 done!\n", "pid:12504 done!\n", "pid:12502 done!\n", "pid:12509 done!\n", "pid:12508 done!\n", "pid:12510 pick times complete:100.0%\n", "pid:12511 pick times complete:100.0%\n", "pid:12512 pick times complete:100.0%\n", "pid:12513 pick times complete:100.0%\n", "pid:12514 pick times complete:100.0%\n", "pid:12515 pick times complete:100.0%\n", "pid:12516 pick times complete:100.0%\n", "pid:12517 pick times complete:100.0%\n", "pid:12510 done!\n", "pid:12512 done!\n", "pid:12515 done!\n", "pid:12513 done!\n", "pid:12511 done!\n", "pid:12514 done!\n", "pid:12517 done!\n", "pid:12516 done!\n", "CPU times: user 4.85 s, sys: 162 ms, total: 5.01 s\n", "Wall time: 19 s\n"]}], "source": ["%%time\n", "from abupy import AbuPickTimeMaster\n", "\n", "capital = AbuCapital(1000000, benchmark)\n", "orders_pd, action_pd, _ = AbuPickTimeMaster.do_symbols_with_same_factors_process(\n", "                                    choice_symbols, benchmark, buy_factors, sell_factors, capital,\n", "                                    n_process_kl=4, n_process_pick_time=4)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["依然使用%%time度量代码块运行时间，之前使用开始时使用ABuPickTimeExecute运行相同的回测输出运行时间18.3 s，\n", "使用AbuPickTimeMaster使用参数n_process_pick_time=8，即启动8个择时进程后，运行时间缩短到6.36s，从输出的信息也可以看到8个pid同时进行\n", "择时\n", "\n", "- pid:84544 pick times complete:100.0%\n", "- pid:84545 pick times complete:100.0%\n", "- pid:84546 pick times complete:100.0%\n", "- pid:84547 pick times complete:100.0%\n", "- pid:84548 pick times complete:100.0%\n", "- pid:84549 pick times complete:100.0%\n", "- pid:84550 pick times complete:100.0%\n", "- pid:84551 pick times complete:100.0%\n", "\n", "多进程的运行方式在进行多支股票设置全市场策略回测时是非常有用的，本节由于choice_symbols中的股票数少，n_process_kl即并行金融数据采集模块\n", "并没有使用，还有一些需要注意的地方也还没有讲到，在后续的全市场回测章节讲继续讲解。\n", "\n", "备注: \n", "\n", "**由于本例开辟进程数较多，但是每个进程处理的任务非常少，所以在cpu不够快的电脑上结果可能正好相反，多进程模式的运行也行会更加耗时，效率更低，属正常现象，后面的例子每个进程可能会处理几十几百个交易对象就不会出现创建销毁进程的开销大于任务执行时间的情况**\n", "\n", "* 特别是windows系统，mac上不会发生，由于mac上的并行使用joblib，在windows上长任务joblib存在bug，所以windows上并行只是封装为joblib的接口形式，详情阅读源代码ABuParallel"]}, {"cell_type": "markdown", "metadata": {"collapsed": true}, "source": ["#### abu量化文档目录章节\n", "\n", "1. [择时策略的开发](http://www.abuquant.com/lecture/lecture_1.html)\n", "2. [择时策略的优化](http://www.abuquant.com/lecture/lecture_2.html)\n", "3. [滑点策略与交易手续费](http://www.abuquant.com/lecture/lecture_3.html)\n", "4. [多支股票择时回测与仓位管理](http://www.abuquant.com/lecture/lecture_4.html)\n", "5. [选股策略的开发](http://www.abuquant.com/lecture/lecture_5.html)\n", "6. [回测结果的度量](http://www.abuquant.com/lecture/lecture_6.html)\n", "7. [寻找策略最优参数和评分](http://www.abuquant.com/lecture/lecture_7.html)\n", "8. [A股市场的回测](http://www.abuquant.com/lecture/lecture_8.html)\n", "9. [港股市场的回测](http://www.abuquant.com/lecture/lecture_9.html)\n", "10. [比特币，莱特币的回测](http://www.abuquant.com/lecture/lecture_10.html)\n", "11. [期货市场的回测](http://www.abuquant.com/lecture/lecture_11.html)\n", "12. [机器学习与比特币示例](http://www.abuquant.com/lecture/lecture_12.html)\n", "13. [量化技术分析应用](http://www.abuquant.com/lecture/lecture_13.html)\n", "14. [量化相关性分析应用](http://www.abuquant.com/lecture/lecture_14.html)\n", "15. [量化交易和搜索引擎](http://www.abuquant.com/lecture/lecture_15.html)\n", "16. [UMP主裁交易决策](http://www.abuquant.com/lecture/lecture_16.html)\n", "17. [UMP边裁交易决策](http://www.abuquant.com/lecture/lecture_17.html)\n", "18. [自定义裁判决策交易](http://www.abuquant.com/lecture/lecture_18.html)\n", "19. [数据源](http://www.abuquant.com/lecture/lecture_19.html)\n", "20. [A股全市场回测](http://www.abuquant.com/lecture/lecture_20.html)\n", "21. [A股UMP决策](http://www.abuquant.com/lecture/lecture_21.html)\n", "22. [美股全市场回测](http://www.abuquant.com/lecture/lecture_22.html)\n", "23. [美股UMP决策](http://www.abuquant.com/lecture/lecture_23.html)\n", "abu量化系统文档教程持续更新中，请关注公众号中的更新提醒。\n", "\n", "#### 《量化交易之路》目录章节及随书代码地址\n", "\n", "1. [第二章 量化语言——Python](https://github.com/bbfamily/abu/tree/master/ipython/第二章-量化语言——Python.ipynb)\n", "2. [第三章 量化工具——NumPy](https://github.com/bbfamily/abu/tree/master/ipython/第三章-量化工具——NumPy.ipynb)\n", "3. [第四章 量化工具——pandas](https://github.com/bbfamily/abu/tree/master/ipython/第四章-量化工具——pandas.ipynb)\n", "4. [第五章 量化工具——可视化](https://github.com/bbfamily/abu/tree/master/ipython/第五章-量化工具——可视化.ipynb)\n", "5. [第六章 量化工具——数学：你一生的追求到底能带来多少幸福](https://github.com/bbfamily/abu/tree/master/ipython/第六章-量化工具——数学.ipynb)\n", "6. [第七章 量化系统——入门：三只小猪股票投资的故事](https://github.com/bbfamily/abu/tree/master/ipython/第七章-量化系统——入门.ipynb)\n", "7. [第八章 量化系统——开发](https://github.com/bbfamily/abu/tree/master/ipython/第八章-量化系统——开发.ipynb)\n", "8. [第九章 量化系统——度量与优化](https://github.com/bbfamily/abu/tree/master/ipython/第九章-量化系统——度量与优化.ipynb)\n", "9. [第十章 量化系统——机器学习•猪老三](https://github.com/bbfamily/abu/tree/master/ipython/第十章-量化系统——机器学习•猪老三.ipynb)\n", "10. [第十一章 量化系统——机器学习•ABU](https://github.com/bbfamily/abu/tree/master/ipython/第十一章-量化系统——机器学习•ABU.ipynb)\n", "11. [附录A 量化环境部署](https://github.com/bbfamily/abu/tree/master/ipython/附录A-量化环境部署.ipynb)\n", "12. [附录B 量化相关性分析](https://github.com/bbfamily/abu/tree/master/ipython/附录B-量化相关性分析.ipynb)\n", "13. [附录C 量化统计分析及指标应用](https://github.com/bbfamily/abu/tree/master/ipython/附录C-量化统计分析及指标应用.ipynb)\n", "\n", "\n", "[更多阿布量化量化技术文章](http://www.abuquant.com/article)\n", "\n", "\n", "更多关于量化交易相关请阅读[《量化交易之路》](http://www.abuquant.com/books/quantify-trading-road.html)\n", "\n", "更多关于量化交易与机器学习相关请阅读[《机器学习之路》](http://www.abuquant.com/books/machine-learning-road.html)\n", "\n", "更多关于abu量化系统请关注微信公众号: abu_quant\n", "\n", "如有任何问题也可在公众号中联系我的个人微信号。\n", "\n", "![](./image/qrcode.jpg)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.2"}}, "nbformat": 4, "nbformat_minor": 2}