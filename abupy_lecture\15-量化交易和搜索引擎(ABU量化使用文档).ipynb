{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# ABU量化系统使用文档 \n", "\n", "<center>\n", "        <img src=\"./image/abu_logo.png\" alt=\"\" style=\"vertical-align:middle;padding:10px 20px;\"><font size=\"6\" color=\"black\"><b>第15节 量化交易和搜索引擎</b></font>\n", "</center>\n", "\n", "-----------------\n", "\n", "作者: 阿布\n", "\n", "阿布量化版权所有 未经允许 禁止转载\n", "\n", "[abu量化系统github地址](https://github.com/bbfamily/abu) (欢迎+star)\n", "\n", "[本节ipython notebook](https://github.com/bbfamily/abu/tree/master/abupy_lecture)\n", "\n", ">骰子? 骰子是什么东西?它应该出现在大富翁游戏里,应该出现在澳门和拉斯维加斯的赌场中，但是，物理学？不，那不是它应该来的地方。骰子代表了投机，代表了不确定，而物理学不是一门最严格最精密，最不能容忍不确定的科学吗？——《量子物理史话》\n", "\n", "虽然我们无法对市场做到确定性的预测，但是股票市场也并不是杂乱无章的，预测和混沌之前存在着一种状态，这种状态可以使用使用概率来描述。《量子物理史话》中薛定谔方程说整个宇宙，你和我都是概率，波恩对波动方程的解释为：电子电荷在空间中的实际分布是电子在某处出现的概率，我们只能预言概率！电子有90%的可能出现在这里, 10%的可能出现在那里，我们也同然可以使用统计来预言概率，如某个策略在某种情况下失败概率为90%，成功概率为10%。\n", "\n", "本节将介绍abu量化系统中的ump模块，它使用了多种机器学习技术，来实现我上面说的预测概率, 首先导入abupy中本节使用的模块："]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["enable example env will only read RomDataBu/df_kl.h5\n"]}], "source": ["# 基础库导入\n", "\n", "from __future__ import print_function\n", "from __future__ import division\n", "\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "%matplotlib inline\n", "\n", "import os\n", "import sys\n", "# 使用insert 0即只使用github，避免交叉使用了pip安装的abupy，导致的版本不一致问题\n", "sys.path.insert(0, os.path.abspath('../'))\n", "import abupy\n", "\n", "# 使用沙盒数据，目的是和书中一样的数据环境\n", "abupy.env.enable_example_env_ipython()"]}, {"cell_type": "code", "execution_count": 22, "metadata": {"collapsed": true}, "outputs": [], "source": ["from abupy import AbuFactorAtrNStop, AbuFactorPreAtrNStop, AbuFactorCloseAtrNStop, AbuFactorBuyBreak, ABuProgress\n", "from abupy import abu, EMarketTargetType, AbuMetricsBase, ABuMarketDrawing, AbuFuturesCn, ABuSymbolPd, ABuMarket\n", "from abupy import AbuUmpMainDeg, AbuUmpMainJump, AbuUmpMainPrice, AbuUmpMainWave, AbuFuturesCn, EStoreAbu"]}, {"cell_type": "markdown", "metadata": {}, "source": ["受限于沙盒中数据限制，本节示例的相关性分析只限制在abupy内置沙盒数据中，完整示例以及代码请阅读后面章节的全市场回测示例或者《量化交易之路》中相关章节。\n", "\n", "首先将内置沙盒中美股，A股，港股, 比特币，莱特币，期货市场中的symbol都列出来:"]}, {"cell_type": "code", "execution_count": 23, "metadata": {"collapsed": true}, "outputs": [], "source": ["us_choice_symbols = ['usTSLA', 'usNOAH', 'usSFUN', 'usBIDU', 'usAAPL', 'usGOOG', 'usWUBA', 'usVIPS']\n", "cn_choice_symbols = ['002230', '300104', '300059', '601766', '600085', '600036', '600809', '000002', '002594']\n", "hk_choice_symbols = ['hk03333', 'hk00700', 'hk02333', 'hk01359', 'hk00656', 'hk03888', 'hk02318']\n", "tc_choice_symbols = ['btc', 'ltc']\n", "# 期货市场的直接从AbuFuturesCn().symbo中读取\n", "ft_choice_symbols = AbuFuturesCn().symbol.tolist()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1. 切分训练集交易的回测"]}, {"cell_type": "markdown", "metadata": {}, "source": ["下面把沙盒数据中的symbol分成两组，一组做为训练symbol\n", "\n", "* 本例训练集symbol使用：沙盒中所有美股 ＋ 沙盒中所有A股 ＋ 沙盒中所有港股 ＋ 比特币\n", "* 本例测试集symbol使用：沙盒中所有期货 ＋ 莱特币\n", "\n", "备注：本例由于symbol数量少所以手动分配训练集，测试集，非沙盒数据环境下使用abupy.env.g_enable_train_test_split等相关设置进行对参数中的symbol或者某个全市场symbol进行自动切割训练集，测试集，详例请阅读《量化交易之路》中相关章节"]}, {"cell_type": "code", "execution_count": 24, "metadata": {"collapsed": true}, "outputs": [], "source": ["# 训练集：沙盒中所有美股 ＋ 沙盒中所有A股 ＋ 沙盒中所有港股 ＋ 比特币\n", "train_choice_symbols = us_choice_symbols + cn_choice_symbols +  hk_choice_symbols + tc_choice_symbols[:1]\n", "# 测试集：沙盒中所有期货 ＋ 莱特币\n", "test_choice_symbols = ft_choice_symbols  + tc_choice_symbols[1:]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["下面的买入因子，卖出因子继续使用之前章节的设置，如下所示："]}, {"cell_type": "code", "execution_count": 25, "metadata": {"collapsed": true}, "outputs": [], "source": ["# 设置初始资金数\n", "read_cash = 1000000\n", "# 买入因子依然延用向上突破因子\n", "buy_factors = [{'xd': 60, 'class': AbuFactorBuyBreak},\n", "               {'xd': 42, 'class': AbuFactorBuyBreak}]\n", "\n", "# 卖出因子继续使用上一节使用的因子\n", "sell_factors = [\n", "    {'stop_loss_n': 1.0, 'stop_win_n': 3.0,\n", "     'class': AbuFactorAtrNStop},\n", "    {'class': AbuFactorPreAtrNStop, 'pre_atr_n': 1.5},\n", "    {'class': AbuFactorCloseAtr<PERSON><PERSON>, 'close_atr_n': 1.5}\n", "]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["本节示例的裁判系统是建立在机器学习技术基础上的，所以必然会涉及到特征，abu量化系统支持在回测过程中生成特征数据，切分训练测试集，甚至成交买单快照图片，通过下面的一行代码设置即可在生成最终的输出结果数据orders_pd上加上买入时刻的很多信息，比如价格位置、趋势走向、波动情况等等特征：\n", "\n", "* 关于特征的类的具体编写请阅读源代码ABuMLFeature以及ABuKLManager\n", "* 本节只示例使用内置特征，在之后的章节后示例自定义特征类的实现"]}, {"cell_type": "code", "execution_count": 26, "metadata": {"collapsed": true}, "outputs": [], "source": ["# 回测生成买入时刻特征\n", "abupy.env.g_enable_ml_feature = True"]}, {"cell_type": "markdown", "metadata": {}, "source": ["下面通过abu.run_loop_back进行回测，choice_symbol使用分配好的训练集symbol："]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["please wait! dump_pickle....: /Users/<USER>/abu/data/cache/n2_lecture_train_capital\n", "please wait! dump_pickle....: /Users/<USER>/abu/data/cache/n2_lecture_train_benchmark\n"]}], "source": ["abu_result_tuple_train, _ = abu.run_loop_back(read_cash,\n", "                                                   buy_factors,\n", "                                                   sell_factors,\n", "                                                   start='2014-07-26',\n", "                                                   end='2016-07-26',\n", "                                                   choice_symbols=train_choice_symbols)\n", "ABuProgress.clear_output()\n", "# 把运行的结果保存在本地，以便后面的章节直接使用，保存回测结果数据代码如下所示\n", "abu.store_abu_result_tuple(abu_result_tuple_train, n_folds=2, store_type=EStoreAbu.E_STORE_CUSTOM_NAME,\n", "                           custom_name='lecture_train')\n", "orders_pd_train = abu_result_tuple_train.orders_pd"]}, {"cell_type": "markdown", "metadata": {}, "source": ["回测结束后，看一些orders_pd的columns，可以看到buy_deg_ang252，buy_price_rank90，buy_atr_std，buy_wave_score3等等都是特征列："]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['buy_date', 'buy_price', 'buy_cnt', 'buy_factor', 'symbol', 'buy_pos',\n", "       'buy_type_str', 'expect_direction', 'sell_type_extra', 'sell_date',\n", "       'sell_price', 'sell_type', 'ml_features', 'key', 'profit', 'result',\n", "       'buy_deg_ang42', 'buy_deg_ang252', 'buy_deg_ang60', 'buy_deg_ang21',\n", "       'buy_price_rank120', 'buy_price_rank90', 'buy_price_rank60',\n", "       'buy_price_rank252', 'buy_wave_score1', 'buy_wave_score2',\n", "       'buy_wave_score3', 'buy_atr_std', 'buy_jump_down_power',\n", "       'buy_jump_up_power', 'buy_diff_down_days', 'buy_diff_up_days',\n", "       'sell_deg_ang42', 'sell_deg_ang252', 'sell_deg_ang60', 'sell_deg_ang21',\n", "       'sell_price_rank120', 'sell_price_rank90', 'sell_price_rank60',\n", "       'sell_price_rank252', 'sell_wave_score1', 'sell_wave_score2',\n", "       'sell_wave_score3', 'sell_jump_down_power', 'sell_jump_up_power',\n", "       'sell_diff_down_days', 'sell_diff_up_days'],\n", "      dtype='object')"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["orders_pd_train.columns"]}, {"cell_type": "markdown", "metadata": {}, "source": ["下面看看生成的特征的具体示例，如下所示：\n", "\n", "备注：buy开头的是买入时刻形成的特征，sell开头的是卖出时刻形成的特征"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>buy_deg_ang42</th>\n", "      <th>buy_deg_ang252</th>\n", "      <th>buy_deg_ang60</th>\n", "      <th>buy_deg_ang21</th>\n", "      <th>buy_price_rank120</th>\n", "      <th>buy_price_rank90</th>\n", "      <th>buy_price_rank60</th>\n", "      <th>buy_price_rank252</th>\n", "      <th>buy_wave_score1</th>\n", "      <th>buy_wave_score2</th>\n", "      <th>buy_wave_score3</th>\n", "      <th>buy_atr_std</th>\n", "      <th>buy_jump_down_power</th>\n", "      <th>buy_jump_up_power</th>\n", "      <th>buy_diff_down_days</th>\n", "      <th>buy_diff_up_days</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2014-09-25</th>\n", "      <td>7.168</td>\n", "      <td>-3.708</td>\n", "      <td>4.342</td>\n", "      <td>2.255</td>\n", "      <td>1.000</td>\n", "      <td>1.000</td>\n", "      <td>1.000</td>\n", "      <td>0.857</td>\n", "      <td>0.496</td>\n", "      <td>0.454</td>\n", "      <td>0.441</td>\n", "      <td>1.159</td>\n", "      <td>-1.369</td>\n", "      <td>0.000</td>\n", "      <td>77</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-10-09</th>\n", "      <td>-0.567</td>\n", "      <td>-6.527</td>\n", "      <td>1.309</td>\n", "      <td>1.837</td>\n", "      <td>0.992</td>\n", "      <td>0.989</td>\n", "      <td>0.983</td>\n", "      <td>0.798</td>\n", "      <td>0.230</td>\n", "      <td>0.072</td>\n", "      <td>-0.001</td>\n", "      <td>1.281</td>\n", "      <td>0.000</td>\n", "      <td>0.000</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-10-17</th>\n", "      <td>2.328</td>\n", "      <td>4.764</td>\n", "      <td>2.096</td>\n", "      <td>2.357</td>\n", "      <td>1.000</td>\n", "      <td>1.000</td>\n", "      <td>1.000</td>\n", "      <td>1.000</td>\n", "      <td>0.743</td>\n", "      <td>0.374</td>\n", "      <td>0.232</td>\n", "      <td>2.033</td>\n", "      <td>0.000</td>\n", "      <td>1.778</td>\n", "      <td>0</td>\n", "      <td>41</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-10-24</th>\n", "      <td>-0.454</td>\n", "      <td>5.532</td>\n", "      <td>2.142</td>\n", "      <td>0.931</td>\n", "      <td>1.000</td>\n", "      <td>1.000</td>\n", "      <td>1.000</td>\n", "      <td>1.000</td>\n", "      <td>1.251</td>\n", "      <td>1.290</td>\n", "      <td>1.289</td>\n", "      <td>0.192</td>\n", "      <td>-13.570</td>\n", "      <td>1.038</td>\n", "      <td>136</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-10-24</th>\n", "      <td>-0.454</td>\n", "      <td>5.532</td>\n", "      <td>2.142</td>\n", "      <td>0.931</td>\n", "      <td>1.000</td>\n", "      <td>1.000</td>\n", "      <td>1.000</td>\n", "      <td>1.000</td>\n", "      <td>1.251</td>\n", "      <td>1.290</td>\n", "      <td>1.289</td>\n", "      <td>0.192</td>\n", "      <td>-13.570</td>\n", "      <td>1.038</td>\n", "      <td>136</td>\n", "      <td>2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            buy_deg_ang42  buy_deg_ang252  buy_deg_ang60  buy_deg_ang21  \\\n", "2014-09-25          7.168          -3.708          4.342          2.255   \n", "2014-10-09         -0.567          -6.527          1.309          1.837   \n", "2014-10-17          2.328           4.764          2.096          2.357   \n", "2014-10-24         -0.454           5.532          2.142          0.931   \n", "2014-10-24         -0.454           5.532          2.142          0.931   \n", "\n", "            buy_price_rank120  buy_price_rank90  buy_price_rank60  \\\n", "2014-09-25              1.000             1.000             1.000   \n", "2014-10-09              0.992             0.989             0.983   \n", "2014-10-17              1.000             1.000             1.000   \n", "2014-10-24              1.000             1.000             1.000   \n", "2014-10-24              1.000             1.000             1.000   \n", "\n", "            buy_price_rank252  buy_wave_score1  buy_wave_score2  \\\n", "2014-09-25              0.857            0.496            0.454   \n", "2014-10-09              0.798            0.230            0.072   \n", "2014-10-17              1.000            0.743            0.374   \n", "2014-10-24              1.000            1.251            1.290   \n", "2014-10-24              1.000            1.251            1.290   \n", "\n", "            buy_wave_score3  buy_atr_std  buy_jump_down_power  \\\n", "2014-09-25            0.441        1.159               -1.369   \n", "2014-10-09           -0.001        1.281                0.000   \n", "2014-10-17            0.232        2.033                0.000   \n", "2014-10-24            1.289        0.192              -13.570   \n", "2014-10-24            1.289        0.192              -13.570   \n", "\n", "            buy_jump_up_power  buy_diff_down_days  buy_diff_up_days  \n", "2014-09-25              0.000                  77                 0  \n", "2014-10-09              0.000                   0                 0  \n", "2014-10-17              1.778                   0                41  \n", "2014-10-24              1.038                 136                 2  \n", "2014-10-24              1.038                 136                 2  "]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["orders_pd_train.filter(regex='buy*').drop(\n", "    ['buy_date', 'buy_price', 'buy_cnt', 'buy_factor', 'buy_pos', 'buy_type_str'], axis=1).head()"]}, {"cell_type": "markdown", "metadata": {"collapsed": true}, "source": ["下面度量训练集使用returns_cmp模式，即不对比标尺大盘，在无资金限制下所有买入交易都可以成交的模式, 如下所示："]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["买入后卖出的交易数量:196\n", "买入后尚未卖出的交易数量:9\n", "胜率:59.6939%\n", "平均获利期望:18.6899%\n", "平均亏损期望:-7.1235%\n", "盈亏比:4.4972\n", "所有交易收益比例和:16.2396 \n", "所有交易总盈亏和:2717948.4900 \n"]}, {"data": {"text/plain": ["<abupy.MetricsBu.ABuMetricsBase.AbuMetricsBase at 0x11e4acc50>"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["AbuMetricsBase.show_general(*abu_result_tuple_train, returns_cmp=True, only_info=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["如上所示的输出即为无资金限制所有买入交易都可以成交的模式下的度量结果，可以看到：所有交易总盈亏和:2717948，但实际上如果在考虑资金的情况下的实际交易总盈亏和并没有这么多，因为有很多交易因为资金限制没能买入成交，如下所示："]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["1364434.764"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["capital_pd = abu_result_tuple_train.capital.capital_pd\n", "capital_pd['capital_blance'][-1] - capital_pd['capital_blance'][0]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["本节的示例回测的度量都将使用无资金限制下所有买入交易都可以成交的模式。\n", "\n", "备注：无资金限制下所有买入交易都可以成交的模式具体实现请阅读AbuMetricsBase"]}, {"cell_type": "markdown", "metadata": {}, "source": ["进入本节核心主体量化交易和搜索引擎结果的好坏最相似的地方有两个：\n", "\n", "1. 对搜索引擎（量化策略）失败结果的人工分析，注重分析失败的结果以及是否存在改进方案，改进方案是否会引进新的问题\n", "2. 机器学习技术在搜索引擎（量化策略）上的改进，必须赋予宏观上合理的解释\n", "\n", "下面将依次展开以上两点：\n", "\n", "### 2. 对交易进行人工分析\n", "\n", "对交易进行人工分析最常用的手动即是直接可视化交易的买入卖出点及走势。\n", "\n", "下面使用plot_candle_from_order直接将orders_pd(交易单子数据)作为参数传入，save=True将交易当时买入点、卖出点等信息标注在图上并保存在本地，针对保存后的交易快照我们就可以进行人工分析："]}, {"cell_type": "code", "execution_count": 12, "metadata": {"collapsed": true}, "outputs": [], "source": ["# 选择失败的笔交易绘制交易快照\n", "plot_simple = orders_pd_train[orders_pd_train.profit_cg < 0]\n", "# save=True保存在本地，耗时操作，需要运行几分钟\n", "ABuMarketDrawing.plot_candle_from_order(plot_simple, save=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["保存完成后，快照将保存在~/abu/data/save_png下当前日期的文件夹中，可使用如下命令直接打开查看："]}, {"cell_type": "code", "execution_count": 13, "metadata": {"collapsed": true}, "outputs": [], "source": ["if abupy.env.g_is_mac_os:\n", "    !open $abupy.env.g_project_data_dir\n", "else:\n", "    !echo $abupy.env.g_project_data_dir"]}, {"cell_type": "markdown", "metadata": {}, "source": ["通过人工分析这些失败的交易，可以观察是否有改进方案，或者不合理的交易，比如下面这笔交易：\n", "\n", "![](./image/ump_trade.png)\n", "\n", "从上面趋势图可以看出：之前大幅度下跌后，底部开始向上拉升，可以发现买入点是在前期阻力位的位置，你可以在具体策略中编写代码阻止类似的交易生效，但是这样容易过拟合，并且这种对策略的微调一定也会带来一些负面的影响，很难量化最终的得失。\n", "\n", "而且这样会导致你的基础策略太过复杂，基础追求的就应该是简单, **可以一句话说明你的基础策略**，针对此类问题的一种解决方法在之前的第九节港股市场的回测中将优化策略的'策略'做为类装饰器进行封装有做过示例讲解，具体效果即是分离基础策略和策略优化监督模块，提高灵活度和适配性，本节示例通过ump来解决此类问题。\n", "\n", "\n", "abupy中ump模块的设计目标是：\n", "\n", "1. 不需要在具体策略中硬编码\n", "2. 不需要人工设定阀值，即且使得代码逻辑清晰\n", "3. 分离基础策略和策略优化监督模块，提高灵活度和适配性\n", "4. 发现策略中隐藏的交易策略问题\n", "5. 可以通过不断的学习新的交易数据\n", "\n", "现阶段的量化策略还是通过人来编写代码，未来的发展也许会向着完全由计算机实现整套流程的方向迈进，包括量化策略本身。\n", "\n", "abupy的设计目标是：\n", "\n", "只需要提供一些基础的简单种子策略代码，计算机在这些简单种子策略基础上不断自我学习、自我完善，创造新的策略，并且紧跟时间序列不断自我调整策略参数。\n", "\n", "### 3. 主裁系统原理:\n", "\n", "下面的内容主要示例通过abupy中的ump模块解决上述问题，abu量化系统中的ump裁判模块，abu量化系统命名规则里，a代表alpha，b代表beta，u代表ump即裁判员的意思，ump将策略回测交易结果作为训练集进行模式识别，特别针对失败的交易识别模式，寻找规律，通过非均衡技术近一步寻找概率上的优势，通过构建多个裁判员的方式来构建裁判（主裁、边裁）机制，来对新的交易进行识别，当新的交易失败的风险大于一定的概率的时候，放弃这次交易，如下图所示：\n", "\n", "![](./image/ump.png)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["主裁核心代码在基类AbuUmpMainBase源代码中，使用gmm进行无监督机器学习, gmm根据参数component将特征分类，component的数值表示将回测交易数据分为多少个类别，默认component值从40至85，即默认将回测交易数据分为40至85个分类，对所有分类结果的cluster组中对应的交易结果数据result进行统计，将cluster组中交易失败概率大于阀值（默认参数0.65即65%失败率）的gmm分类器clf进行保存。\n", "\n", "举例说明：即使用gmm对回测交易数据进行聚类，比如你对所有交易数据聚类聚了20个分类，然后发现第19个分类里面65％以上都是赔钱的交易，那就提取这个分类的的类别及分类器，作为之后的判定器的组成部份，如果新的交易被判定为这类那我们就对这个交易进行拦截。\n", "\n", "更多详情请自行阅读AbuUmpMainBase源代码\n", "\n", "\n", "### 4. 角度主裁:\n", "\n", "每个特定主裁有自己独特的选定特征，子类完成的主要工作就是对特征进行处理，如AbuUmpMainDeg的特征为21、42、60、252日拟合角度, 更多具体实现请阅读《量化交易之路》中相关内容，下面仅示例使用，先看看角度主裁有哪些特征："]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>result</th>\n", "      <th>buy_deg_ang42</th>\n", "      <th>buy_deg_ang252</th>\n", "      <th>buy_deg_ang60</th>\n", "      <th>buy_deg_ang21</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2014-09-25</th>\n", "      <td>1</td>\n", "      <td>7.168</td>\n", "      <td>-3.708</td>\n", "      <td>4.342</td>\n", "      <td>2.255</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-10-09</th>\n", "      <td>0</td>\n", "      <td>-0.567</td>\n", "      <td>-6.527</td>\n", "      <td>1.309</td>\n", "      <td>1.837</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-10-17</th>\n", "      <td>1</td>\n", "      <td>2.328</td>\n", "      <td>4.764</td>\n", "      <td>2.096</td>\n", "      <td>2.357</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-10-24</th>\n", "      <td>1</td>\n", "      <td>-0.454</td>\n", "      <td>5.532</td>\n", "      <td>2.142</td>\n", "      <td>0.931</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-10-24</th>\n", "      <td>1</td>\n", "      <td>-0.454</td>\n", "      <td>5.532</td>\n", "      <td>2.142</td>\n", "      <td>0.931</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            result  buy_deg_ang42  buy_deg_ang252  buy_deg_ang60  \\\n", "2014-09-25       1          7.168          -3.708          4.342   \n", "2014-10-09       0         -0.567          -6.527          1.309   \n", "2014-10-17       1          2.328           4.764          2.096   \n", "2014-10-24       1         -0.454           5.532          2.142   \n", "2014-10-24       1         -0.454           5.532          2.142   \n", "\n", "            buy_deg_ang21  \n", "2014-09-25          2.255  \n", "2014-10-09          1.837  \n", "2014-10-17          2.357  \n", "2014-10-24          0.931  \n", "2014-10-24          0.931  "]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["# 参数为orders_pd_train\n", "ump_deg = AbuUmpMainDeg(orders_pd_train)\n", "ump_deg.fiter.df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["上面输出的每一行实际上代表一次交易，result代表这次交易的最终结果，0：亏损，1：盈利，deng_ang21代表买入信号发生时刻向前21天的交易日收盘价格拟合曲线角度特征值，与此相似deg_ang42、deg_ang60、deg_ang252分别代表买入信号发生时刻向前42天、60天、252天收盘价格拟合曲线角度特征值。\n", "\n", "下面使用AbuUmpMainBase.fit()函数进行主裁分类簇的筛选，以及可视化分类簇特性，如下所示：\n", "\n", "备注：默认使用component值从40至85，本示例由于使用的沙盒数据，训练集数据量太少，所以下面参数p_ncs降低component值从20至40"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["pid:6505 gmm fit:100.0%\n", "pid:6505 done!\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAhEAAAFyCAYAAABLFoh7AAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nCEmSlMUQIUmSshgiJElSFkOEJEnK8v8BXyDSf1QIdD8AAAAASUVORK5CYII=\n", "text/plain": ["<matplotlib.figure.Figure at 0x11ea719b0>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAgQAAAFUCAYAAABbZCT8AAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nRAAAAABJRU5ErkJggg==\n", "text/plain": ["<matplotlib.figure.Figure at 0x12606ddd8>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["ump_deg.fit(p_ncs=slice(20, 40, 1), brust_min=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["上面可视化结果各个轴分别代表：\n", "\n", "* lcs: 分类簇中样本总数\n", "* lrs: 分类簇中样本失败率\n", "* lps: 分类簇中样本交易获利比例总和\n", "* lms: 分类簇中样本每笔交易平均获利"]}, {"cell_type": "markdown", "metadata": {}, "source": ["ump_deg.cprs提取了使用gmm从20-40个分类中交易失败率大于65%的簇:"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>lcs</th>\n", "      <th>lms</th>\n", "      <th>lps</th>\n", "      <th>lrs</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>20_11</th>\n", "      <td>7</td>\n", "      <td>-0.0282</td>\n", "      <td>-0.1972</td>\n", "      <td>0.7143</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20_19</th>\n", "      <td>1</td>\n", "      <td>-0.1797</td>\n", "      <td>-0.1797</td>\n", "      <td>1.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21_1</th>\n", "      <td>10</td>\n", "      <td>0.0216</td>\n", "      <td>0.2156</td>\n", "      <td>0.7000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21_13</th>\n", "      <td>9</td>\n", "      <td>-0.0423</td>\n", "      <td>-0.3809</td>\n", "      <td>0.7778</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21_15</th>\n", "      <td>7</td>\n", "      <td>-0.0401</td>\n", "      <td>-0.2807</td>\n", "      <td>0.8571</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       lcs     lms     lps     lrs\n", "20_11    7 -0.0282 -0.1972  0.7143\n", "20_19    1 -0.1797 -0.1797  1.0000\n", "21_1    10  0.0216  0.2156  0.7000\n", "21_13    9 -0.0423 -0.3809  0.7778\n", "21_15    7 -0.0401 -0.2807  0.8571"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["ump_deg.cprs.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["对上表格第一行数据详细解释如下：\n", "\n", "用gmm将特征进行分类，分20个类，这个分类中的第11簇失败率为0.6667，即index:20_11，这个分类中有7笔交易，平均每笔交易平均获利0.0282，分类中所有交易获利比例总和为-0.1972\n", "\n", "备注：不同的运行环境下分类的结果序号等是不相同的\n", "\n", "下面我们找出所有提取结果中交易失败概率最大的分类簇, 由于沙盒数据中数据量限制，导致每个分类簇中数据很少，下面的筛选条件是分类中有至少有5笔以上交易:"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>lcs</th>\n", "      <th>lms</th>\n", "      <th>lps</th>\n", "      <th>lrs</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>23_15</th>\n", "      <td>7</td>\n", "      <td>-0.0401</td>\n", "      <td>-0.2807</td>\n", "      <td>0.8571</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21_15</th>\n", "      <td>7</td>\n", "      <td>-0.0401</td>\n", "      <td>-0.2807</td>\n", "      <td>0.8571</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22_15</th>\n", "      <td>7</td>\n", "      <td>-0.0401</td>\n", "      <td>-0.2807</td>\n", "      <td>0.8571</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24_15</th>\n", "      <td>7</td>\n", "      <td>-0.0401</td>\n", "      <td>-0.2807</td>\n", "      <td>0.8571</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34_16</th>\n", "      <td>6</td>\n", "      <td>-0.0696</td>\n", "      <td>-0.4178</td>\n", "      <td>0.8333</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       lcs     lms     lps     lrs\n", "23_15    7 -0.0401 -0.2807  0.8571\n", "21_15    7 -0.0401 -0.2807  0.8571\n", "22_15    7 -0.0401 -0.2807  0.8571\n", "24_15    7 -0.0401 -0.2807  0.8571\n", "34_16    6 -0.0696 -0.4178  0.8333"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["mfx = ump_deg.cprs[(ump_deg.cprs['lcs'] > 5)].sort_values(by='lrs')[::-1]\n", "mfx.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["从上面的输出可以看到23_15是失败概率很大的分类簇，接下来我们查看ump_deg.nts表，它是字典结构。\n", "\n", "下面获得mfx分类簇下的所有交易的DataFrame数据对象, 寻找deg_ang252中存在非常大的数值的分类簇, 且分类簇中交易数量最多的GMM分类簇。\n", "\n", "备注：不同的运行环境下分类的结果序号等是不相同的（GMM中初始随机数, 预热参数导致）, 即不同的环境下运行的结果不一定是23-15这个分类簇序号，但是分类的性质基本相似"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>result</th>\n", "      <th>buy_deg_ang42</th>\n", "      <th>buy_deg_ang252</th>\n", "      <th>buy_deg_ang60</th>\n", "      <th>buy_deg_ang21</th>\n", "      <th>ind</th>\n", "      <th>cluster</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2015-02-18</th>\n", "      <td>1</td>\n", "      <td>7.544</td>\n", "      <td>21.959</td>\n", "      <td>2.577</td>\n", "      <td>0.429</td>\n", "      <td>53</td>\n", "      <td>15</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-03-25</th>\n", "      <td>1</td>\n", "      <td>5.217</td>\n", "      <td>16.483</td>\n", "      <td>8.213</td>\n", "      <td>0.654</td>\n", "      <td>68</td>\n", "      <td>15</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-04-10</th>\n", "      <td>0</td>\n", "      <td>8.559</td>\n", "      <td>23.632</td>\n", "      <td>2.437</td>\n", "      <td>6.003</td>\n", "      <td>77</td>\n", "      <td>15</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-10-19</th>\n", "      <td>0</td>\n", "      <td>6.492</td>\n", "      <td>23.973</td>\n", "      <td>2.085</td>\n", "      <td>2.096</td>\n", "      <td>121</td>\n", "      <td>15</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-10-20</th>\n", "      <td>0</td>\n", "      <td>8.226</td>\n", "      <td>18.983</td>\n", "      <td>-6.655</td>\n", "      <td>2.002</td>\n", "      <td>124</td>\n", "      <td>15</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-10-20</th>\n", "      <td>0</td>\n", "      <td>15.407</td>\n", "      <td>14.483</td>\n", "      <td>3.308</td>\n", "      <td>3.866</td>\n", "      <td>125</td>\n", "      <td>15</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-10-22</th>\n", "      <td>0</td>\n", "      <td>6.979</td>\n", "      <td>23.305</td>\n", "      <td>2.701</td>\n", "      <td>3.120</td>\n", "      <td>129</td>\n", "      <td>15</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-10-26</th>\n", "      <td>0</td>\n", "      <td>7.901</td>\n", "      <td>19.609</td>\n", "      <td>-2.029</td>\n", "      <td>5.574</td>\n", "      <td>132</td>\n", "      <td>15</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-10-27</th>\n", "      <td>0</td>\n", "      <td>14.644</td>\n", "      <td>14.923</td>\n", "      <td>4.431</td>\n", "      <td>3.581</td>\n", "      <td>138</td>\n", "      <td>15</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-12-30</th>\n", "      <td>0</td>\n", "      <td>2.868</td>\n", "      <td>17.983</td>\n", "      <td>9.238</td>\n", "      <td>-0.160</td>\n", "      <td>152</td>\n", "      <td>15</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2016-04-14</th>\n", "      <td>0</td>\n", "      <td>4.611</td>\n", "      <td>18.428</td>\n", "      <td>3.134</td>\n", "      <td>0.733</td>\n", "      <td>181</td>\n", "      <td>15</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            result  buy_deg_ang42  buy_deg_ang252  buy_deg_ang60  \\\n", "2015-02-18       1          7.544          21.959          2.577   \n", "2015-03-25       1          5.217          16.483          8.213   \n", "2015-04-10       0          8.559          23.632          2.437   \n", "2015-10-19       0          6.492          23.973          2.085   \n", "2015-10-20       0          8.226          18.983         -6.655   \n", "2015-10-20       0         15.407          14.483          3.308   \n", "2015-10-22       0          6.979          23.305          2.701   \n", "2015-10-26       0          7.901          19.609         -2.029   \n", "2015-10-27       0         14.644          14.923          4.431   \n", "2015-12-30       0          2.868          17.983          9.238   \n", "2016-04-14       0          4.611          18.428          3.134   \n", "\n", "            buy_deg_ang21  ind  cluster  \n", "2015-02-18          0.429   53       15  \n", "2015-03-25          0.654   68       15  \n", "2015-04-10          6.003   77       15  \n", "2015-10-19          2.096  121       15  \n", "2015-10-20          2.002  124       15  \n", "2015-10-20          3.866  125       15  \n", "2015-10-22          3.120  129       15  \n", "2015-10-26          5.574  132       15  \n", "2015-10-27          3.581  138       15  \n", "2015-12-30         -0.160  152       15  \n", "2016-04-14          0.733  181       15  "]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["max_failed_cluster_orders = None\n", "for mfx_ind in np.arange(0, len(mfx)):\n", "    tmp = ump_deg.nts[mfx.index[mfx_ind]]\n", "    if tmp.buy_deg_ang252.mean() > 10:\n", "        if max_failed_cluster_orders is None:\n", "            max_failed_cluster_orders = tmp\n", "        elif len(tmp) > len(max_failed_cluster_orders):\n", "            # 寻找分类簇中交易数量最多的\n", "            max_failed_cluster_orders = tmp\n", "\n", "if max_failed_cluster_orders is None:\n", "    max_failed_cluster_orders = ump_deg.nts[mfx.index[0]]\n", "max_failed_cluster_orders"]}, {"cell_type": "markdown", "metadata": {}, "source": ["下面分别统计分类簇和训练数据集中特征的平均值，可以看到：\n", "\n", "- 分类簇中deg_ang252非常大，deg_ang42的值相比较训练集平均值也很大\n", "- deg_ang21，deg_ang60平均值基本和训练集数据平均值持平"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["分类簇中deg_ang60平均值为2.68\n", "训练数据集中deg_ang60平均值为1.56\n", "\n", "分类簇中deg_ang21平均值为2.54\n", "训练数据集中deg_ang21平均值为4.87\n", "\n", "分类簇中deg_ang42平均值为8.04\n", "训练数据集中deg_ang42平均值为4.48\n", "\n", "分类簇中deg_ang252平均值为19.43\n", "训练数据集中deg_ang252平均值为3.25\n"]}], "source": ["print('分类簇中deg_ang60平均值为{0:.2f}'.format(\n", "    max_failed_cluster_orders.buy_deg_ang60.mean()))\n", "print('训练数据集中deg_ang60平均值为{0:.2f}\\n'.format(\n", "    orders_pd_train.buy_deg_ang60.mean()))\n", "\n", "print('分类簇中deg_ang21平均值为{0:.2f}'.format(\n", "    max_failed_cluster_orders.buy_deg_ang21.mean()))\n", "print('训练数据集中deg_ang21平均值为{0:.2f}\\n'.format(\n", "    orders_pd_train.buy_deg_ang21.mean()))\n", "\n", "\n", "print('分类簇中deg_ang42平均值为{0:.2f}'.format(\n", "    max_failed_cluster_orders.buy_deg_ang42.mean()))\n", "print('训练数据集中deg_ang42平均值为{0:.2f}\\n'.format(\n", "    orders_pd_train.buy_deg_ang42.mean()))\n", "\n", "print('分类簇中deg_ang252平均值为{0:.2f}'.format(\n", "    max_failed_cluster_orders.buy_deg_ang252.mean()))\n", "print('训练数据集中deg_ang252平均值为{0:.2f}'.format(\n", "    orders_pd_train.buy_deg_ang252.mean()))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["更进一步，我们将所有分类簇中的交易快照进行可视化，进行人工分析，如下代码所示："]}, {"cell_type": "code", "execution_count": 23, "metadata": {"scrolled": false}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABJEAAALKCAYAAACC18C8AAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nJHVkE0mSJEmSJEkd/R8+Dd4ty0F7TgAAAABJRU5ErkJggg==\n", "text/plain": ["<matplotlib.figure.Figure at 0x12609fcf8>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABI0AAALKCAYAAAC/RaHaAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nkiRJ2oxNI0mSJEmSJG3m/wGrcCf2zUTDxgAAAABJRU5ErkJggg==\n", "text/plain": ["<matplotlib.figure.Figure at 0x1264dd668>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABJEAAALKCAYAAACC18C8AAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\n", "text/plain": ["<matplotlib.figure.Figure at 0x12868cb70>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABIoAAALKCAYAAABdmbqjAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nSZKkzn8Aw5WdHZY0AmoAAAAASUVORK5CYII=\n", "text/plain": ["<matplotlib.figure.Figure at 0x126c752e8>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["cp = []\n", "for ind in np.arange(0, len(max_failed_cluster_orders)):\n", "    # 获取其在原始orders中的ind\n", "    order_ind = int(max_failed_cluster_orders.iloc[ind].ind)\n", "    # 从原始orders中取出order\n", "    order = ump_deg.fiter.order_has_ret.iloc[order_ind]\n", "    if order.symbol.isdigit() and order.symbol not in cp:\n", "        # 介于篇幅长度，只可视化a股市场的了，每个symbol只绘制一次，避免42d和60d策略同时生效，两个单子，这里绘制两次\n", "        cp.append(order.symbol)\n", "        ABuMarketDrawing.plot_candle_from_order(order, date_ext=252)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 5. 赋予宏观上合理的解释：\n", "\n", "上面显示的交易sh600809是本节初失败结果人工分析的那个案例的交易，这里它在主裁deg识别中被捕获。这样我们就不需要在具体策略中编写代码阻止类似的交易生效，它被机器学习gmm识别到一个固定的分类簇中，我们保存这个分类簇，在之后的交易可以运用这个分类簇对新的交易进行裁判。\n", "\n", "从上面的走势快照以及特征值分析可以对gmm这次分类进行宏观上合理的解释：\n", "\n", "* 过去一年的股价走势快速拉升（deg_ang252非常大）\n", "* 过去3三个月走势失去了前期的气势，开始走下坡路（deg_ang60平均值持平与训练集数据平均值）\n", "* 过去2个月走势有一次回光反照(deg_ang42的值相比较训练集平均值也很大)\n", "\n", "**最终拦截的交易宏观上的解释为：快速拉升后的震荡下行走势下的小上升走势，且遇到了短期阻力位(由上面交易图可见)**\n", "\n", "\n", "上面的分析即做到了机器学习技术在搜索引擎（量化策略）的改进，必须赋予宏观上合理的解释。\n", "\n", "\n", "你可以发现如果你想要手工在策略中通过编写代码添加这个规则时，逻辑代码的实现会相当复杂，而且不得不面对阀值问题，使用gmm分类簇可以有效规避此类问题，而且使得代码逻辑清晰，没有过多的硬编码，且在之后的交易中指导策略进行信号拦截"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 6. 最优分类簇筛选：\n", "\n", "上面我们抽取了gmm大于阀值失败率的分类簇后，对ump_deg.cprs进行分析可以发现:\n", "\n", "在很多分类簇中的交易胜率不高，但是交易获利比例总和却为正值, 即有很多交易簇，虽然簇的失败率很高，但是簇中所有交易的收益和却是正值，即一直强调的不能只关注胜率，盈亏比更是关键。\n", "\n", "那么我们将所有分类簇保存在本地，对之后的交易进行裁决显然是不妥当的。"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>lcs</th>\n", "      <th>lms</th>\n", "      <th>lps</th>\n", "      <th>lrs</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>21_1</th>\n", "      <td>10</td>\n", "      <td>0.0216</td>\n", "      <td>0.2156</td>\n", "      <td>0.7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22_1</th>\n", "      <td>10</td>\n", "      <td>0.0216</td>\n", "      <td>0.2156</td>\n", "      <td>0.7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23_1</th>\n", "      <td>10</td>\n", "      <td>0.0216</td>\n", "      <td>0.2156</td>\n", "      <td>0.7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24_1</th>\n", "      <td>10</td>\n", "      <td>0.0216</td>\n", "      <td>0.2156</td>\n", "      <td>0.7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25_1</th>\n", "      <td>10</td>\n", "      <td>0.0216</td>\n", "      <td>0.2156</td>\n", "      <td>0.7</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      lcs     lms     lps  lrs\n", "21_1   10  0.0216  0.2156  0.7\n", "22_1   10  0.0216  0.2156  0.7\n", "23_1   10  0.0216  0.2156  0.7\n", "24_1   10  0.0216  0.2156  0.7\n", "25_1   10  0.0216  0.2156  0.7"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["ump_deg.cprs[ump_deg.cprs['lps'] > 0].head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "下面我们使用全局最优技术对分类簇集合进行筛选, 如下所示:\n", "\n", "备注：\n", "\n", "- 对外的使用实际不会涉及如下内容，如在之后的章节中主裁的训练只使用一行代码即可完成，这里不必过分深入\n", "- 如对内部实现敢兴趣，请阅读《量化交易之路》中相关内容或者阅读源代码。"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["AbuUmpMainDeg: brute min progress::100.0%\r"]}, {"data": {"text/plain": ["array([-0.098, -0.018,  0.667])"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["brust_min = ump_deg.brust_min()\n", "brust_min"]}, {"cell_type": "markdown", "metadata": {}, "source": ["下面根据上面计算出的最优参数对分类簇集合进行筛选。\n", "\n", "* 分类簇中样本交易获利比例总和小于-0.1\n", "* 分类簇中样本每笔交易平均获利小于-0.01\n", "* 分类簇中样本失败率大于0.67\n", "\n", "如下代码返回的llps为最终筛选结果, 将筛选后的结果使用dump_clf接口进行保存（最终角度主裁模型）保存在本地，以预备之后对新的交易进行裁决。"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["please wait! dump_pickle....: /Users/<USER>/abu/data/ump/ump_main_us_deg_main\n"]}], "source": ["llps = ump_deg.cprs[(ump_deg.cprs['lps'] <= brust_min[0]) & (ump_deg.cprs['lms'] <= brust_min[1]) & \n", "                    (ump_deg.cprs['lrs'] >= brust_min[2])]\n", "ump_deg.dump_clf(llps)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["小结：\n", "\n", "本节演示了使用abupy对回测结果进行的人工分析，分步讲解ump裁判拦截的大体实现思路，训练了角度主裁，下一节将完成其它主裁的训练，但是会使用一行代码完成，不会像本节示例如此繁琐。"]}, {"cell_type": "markdown", "metadata": {"collapsed": true}, "source": ["#### abu量化文档目录章节\n", "\n", "1. [择时策略的开发](http://www.abuquant.com/lecture/lecture_1.html)\n", "2. [择时策略的优化](http://www.abuquant.com/lecture/lecture_2.html)\n", "3. [滑点策略与交易手续费](http://www.abuquant.com/lecture/lecture_3.html)\n", "4. [多支股票择时回测与仓位管理](http://www.abuquant.com/lecture/lecture_4.html)\n", "5. [选股策略的开发](http://www.abuquant.com/lecture/lecture_5.html)\n", "6. [回测结果的度量](http://www.abuquant.com/lecture/lecture_6.html)\n", "7. [寻找策略最优参数和评分](http://www.abuquant.com/lecture/lecture_7.html)\n", "8. [A股市场的回测](http://www.abuquant.com/lecture/lecture_8.html)\n", "9. [港股市场的回测](http://www.abuquant.com/lecture/lecture_9.html)\n", "10. [比特币，莱特币的回测](http://www.abuquant.com/lecture/lecture_10.html)\n", "11. [期货市场的回测](http://www.abuquant.com/lecture/lecture_11.html)\n", "12. [机器学习与比特币示例](http://www.abuquant.com/lecture/lecture_12.html)\n", "13. [量化技术分析应用](http://www.abuquant.com/lecture/lecture_13.html)\n", "14. [量化相关性分析应用](http://www.abuquant.com/lecture/lecture_14.html)\n", "15. [量化交易和搜索引擎](http://www.abuquant.com/lecture/lecture_15.html)\n", "16. [UMP主裁交易决策](http://www.abuquant.com/lecture/lecture_16.html)\n", "17. [UMP边裁交易决策](http://www.abuquant.com/lecture/lecture_17.html)\n", "18. [自定义裁判决策交易](http://www.abuquant.com/lecture/lecture_18.html)\n", "19. [数据源](http://www.abuquant.com/lecture/lecture_19.html)\n", "20. [A股全市场回测](http://www.abuquant.com/lecture/lecture_20.html)\n", "21. [A股UMP决策](http://www.abuquant.com/lecture/lecture_21.html)\n", "22. [美股全市场回测](http://www.abuquant.com/lecture/lecture_22.html)\n", "23. [美股UMP决策](http://www.abuquant.com/lecture/lecture_23.html)\n", "\n", "abu量化系统文档教程持续更新中，请关注公众号中的更新提醒。\n", "\n", "#### 《量化交易之路》目录章节及随书代码地址\n", "\n", "1. [第二章 量化语言——Python](https://github.com/bbfamily/abu/tree/master/ipython/第二章-量化语言——Python.ipynb)\n", "2. [第三章 量化工具——NumPy](https://github.com/bbfamily/abu/tree/master/ipython/第三章-量化工具——NumPy.ipynb)\n", "3. [第四章 量化工具——pandas](https://github.com/bbfamily/abu/tree/master/ipython/第四章-量化工具——pandas.ipynb)\n", "4. [第五章 量化工具——可视化](https://github.com/bbfamily/abu/tree/master/ipython/第五章-量化工具——可视化.ipynb)\n", "5. [第六章 量化工具——数学：你一生的追求到底能带来多少幸福](https://github.com/bbfamily/abu/tree/master/ipython/第六章-量化工具——数学.ipynb)\n", "6. [第七章 量化系统——入门：三只小猪股票投资的故事](https://github.com/bbfamily/abu/tree/master/ipython/第七章-量化系统——入门.ipynb)\n", "7. [第八章 量化系统——开发](https://github.com/bbfamily/abu/tree/master/ipython/第八章-量化系统——开发.ipynb)\n", "8. [第九章 量化系统——度量与优化](https://github.com/bbfamily/abu/tree/master/ipython/第九章-量化系统——度量与优化.ipynb)\n", "9. [第十章 量化系统——机器学习•猪老三](https://github.com/bbfamily/abu/tree/master/ipython/第十章-量化系统——机器学习•猪老三.ipynb)\n", "10. [第十一章 量化系统——机器学习•ABU](https://github.com/bbfamily/abu/tree/master/ipython/第十一章-量化系统——机器学习•ABU.ipynb)\n", "11. [附录A 量化环境部署](https://github.com/bbfamily/abu/tree/master/ipython/附录A-量化环境部署.ipynb)\n", "12. [附录B 量化相关性分析](https://github.com/bbfamily/abu/tree/master/ipython/附录B-量化相关性分析.ipynb)\n", "13. [附录C 量化统计分析及指标应用](https://github.com/bbfamily/abu/tree/master/ipython/附录C-量化统计分析及指标应用.ipynb)\n", "\n", "[更多阿布量化量化技术文章](http://www.abuquant.com/article)\n", "\n", "\n", "更多关于量化交易相关请阅读[《量化交易之路》](http://www.abuquant.com/books/quantify-trading-road.html)\n", "\n", "更多关于量化交易与机器学习相关请阅读[《机器学习之路》](http://www.abuquant.com/books/machine-learning-road.html)\n", "\n", "更多关于abu量化系统请关注微信公众号: abu_quant\n", "\n", "![](./image/qrcode.jpg)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.2"}}, "nbformat": 4, "nbformat_minor": 2}