{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["### 点击上方的运行按钮，启动回测操作界面，如下图所示：\n", "\n", "![](./run_cell.png)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "54b6210ad8eb4103acf7880e2bb687f0"}}, "metadata": {}, "output_type": "display_data"}], "source": ["import widget_update_ui\n", "widget_update_ui.show_ui()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["[abu量化系统github地址](https://github.com/bbfamily/abu) (您的star是我的动力！)\n", "\n", "[更多阿布量化量化技术文章](http://www.abuquant.com/article)\n", "\n", "更多关于量化交易相关请阅读[《量化交易之路》](http://www.abuquant.com/books/quantify-trading-road.html)\n", "\n", "更多关于量化交易与机器学习相关请阅读[《机器学习之路》](http://www.abuquant.com/books/machine-learning-road.html)\n", "\n", "更多关于abu量化系统请关注微信公众号: abu_quant\n", "\n", "![](../abupy_lecture/image/qrcode.jpg)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.1"}}, "nbformat": 4, "nbformat_minor": 2}