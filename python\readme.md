# 量化交易之路 python代码


提供了python版本的代码示例实现，但请尽量能熟练使用notebook，使用了交互式操作的ipython notebook便于量化交易策略的思路快速实现，检验，面向过程的步骤，强大的可视化内嵌进一步引导思路，方便将零散的思路一点一点变成代码，验证代码的有效性，以及更多的实验特性

1. [第二章 量化语言——Python]()
2. [第三章 量化工具——NumPy]()
3. [第四章 量化工具——pandas]()
4. [第五章 量化工具——可视化]()
5. [第六章 量化工具——数学：你一生的追求到底能带来多少幸福]()
6. [第七章 量化系统——入门：三只小猪股票投资的故事]()
7. [第八章 量化系统——开发]()
8. [第九章 量化系统——度量与优化]()
9. [第十章 量化系统——机器学习•猪老三]()
10. [第十一章 量化系统——机器学习•ABU]()
11. [附录A 量化环境部署]()
12. [附录B 量化相关性分析]()
13. [附录C 量化统计分析及指标应用]()